{
    "authConfig": {
        "client": "bluewhale",
        "secret": "57d67712538a80d61af8b78b"
    },

    "restConfig": {
        "apisRestUrl": "http://esb.99bx.cn",
        "esbRestUrl": "http://************:18003",
        "racoonRestUrl": "http://10.25.243.68:8836",
        "tigerRestUrl": "http://10.25.243.67:8800",
        "umRestUrl": "https://um.hizom.cn",
        "camelRestUrl": "http://************:8834",
        "pandaRestUrl": "http://10.25.243.68:9000",
        "oprRestUrl": "https://opr.hizom.cn",
        "pigeonRestUrl": "http://10.25.243.67:8833",
        "cashvalueRestUrl":"http://10.25.243.75:8050"
    },

    "ossConfig":{
        "policy": {
            "type": {
                "resource": "policy",
                "filetype": "pdf"
            }
        },
        "invoice": {
            "type": {
                "resource": "invoice",
                "filetype": "pdf"
            }
        },
        "junziqian": {
            "type": {
                "resource": "junziqian",
                "filetype": "pdf"
            }
        },
        "litigation": {
            "type": {
                "resource": "litigation",
                "filetype": "jpg"
            }
        },
        "sign": {
            "type": {
                "resource": "sign",
                "filetype": "pdf"
            }
        },
        "shuzhikong": {
            "type": {
                "resource": "shuzhikong"
            }
        },
        "camelUrl": "http://************:8834",
        "clientId": "bluewhale"
    },

    "cacheConfig": {
        "port": 6379,
        "host": "b8409cdcac014b03553.redis.rds.aliyuncs.com",
        "options": {},
        "password": "b8409cdcac014b03:Redis1qaz12346"
    },
    "sentry":"https://<EMAIL>/4",
    "databaseConfig": {
        "host": "mongodb://dds-bp16fd1e01cf3c841529.mongodb.rds.aliyuncs.com:3717,dds-bp16fd1e01cf3c842770.mongodb.rds.aliyuncs.com:3717/oprDB?replicaSet=mgset-1257429",
        "database": "oprDB",
        "options": {
            "useNewUrlParser": true,
            "useUnifiedTopology": true,
            "useCreateIndex": true,
            "user": "oprnew",
            "pass": "hu1ba0opr"
        }
    },

    "loggerLevel": "info",
    "express": {
        "view cache": false,
        "view engine": "js",
        "views": "path:./.build/templates"
    },

    "param": {
        "directlyFailTime": "23:30:00 +0800",
        "policyWarningList": ["17602118819"],
        "policyErrorList": [],
        "intelligent":{
            "ALCX":{
                "requestUrl": "https://partner.allianz.cn/uwApp/intelligence.html#/underWriting",
                "callbackUrl": "https://bw.hizom.cn/api/intelligent/ALCX/",
                "cid": "BA100170",
                "key": "F44795DCD2E94FF"
            },
            "PAJK":{
                "requestUrl": "https://mobile.health.pingan.com/ehis-hm/aitest/outindex.do",
                "callbackUrl": "https://bw.hizom.cn/api/intelligent/PAJK/",
                "signKey": "bJPYhBp0h8eys1Te"
            },
            "HGRS": {
                "callbackUrl": "https://bw.hizom.cn/api/intelligent/HGRS/",
                "signKey": "QvzNU4oGCmW92szU",
                "requestUrlUWUrl": "https://wap.huaguilife.cn/plugin/preparedUW",
                "preparedUWInfoUrl": "https://wap.huaguilife.cn/plugin/preparedUWInfo",
                "debugMode": false,
                "channel": "hzjj"
            },
            "ZAZX":{
                "requestUrl": "https://ihealth.zhongan.com/mobile/intelligencyUnderwriting/confirm",
                "callbackUrl": "https://bw.hizom.cn/api/intelligent/ZXES2019/"
            },
            "TKZX":{
                "requestUrl": "http://channel.tk.cn/page/zhinenghebao/index.html?encryption=",
                "callbackUrl": "https://bw.hizom.cn/api/intelligent/TKZX/",
                "signKey": "tkcwuhan"
            },
            "HQRS":{
                "callbackUrl": "https://bw.hizom.cn/api/intelligent/HQRS/"
            },
            "RHJK": {
                "backhaulCallbackUrl": "https://bw.hizom.cn/api/backhaul/RHJK/0rGFu1usO63BLxcC",
                "cpsHost": "https://rhssm.rhassurance.com",
                "channel": "A2020020"
            },
            "TPCX":{
                "requestUrl": "http://************:18003/external/insure/TPCX/intelligent/callback",
                "callbackUrl": "https://bw.hizom.cn/api/intelligent/TPCX/",
                "deptCode": "99990067"
            },
            "JLRS": {
                "requestUrl": "http://************:18003/external/insure/JLRS/intelligent/callback",
                "callbackUrl": "https://bw.hizom.cn/api/intelligent/JLRS/",
                "deptCode": "99990067"
            }
        },
        "issue": {
            "RSCX": {
                "requestUrl": "https://micro-personal-sz.chinalife-p.com.cn/ThirdPartPlat/execute.action",
                "invoiceUrl": "https://micro-personal-sz.chinalife-p.com.cn/ElecInvoicePrintInfoController/policyDigitalInvoice",
                "password": "HZ01",
                "secretKey": "L795C$NEE%ka3oGG"
            },
            "RSCX2": {
                "requestUrl": "https://gpicesb.chinalife-p.com.cn:8443/ESB/P042T/NewCoreDockingInterface/S103",
                "invoiceUrl": "https://wx-gd.chinalife-p.com.cn/china-life/ElecInvoiceIssue"
            },
            "PACX": {
                "channel": "P_SHHZ_GP",
                "departmentCode": "_23515_0001",
                "uploadUrl": "http://iobs-upload.pingan.com.cn/upload"
            },
            "XFRS": {
                "sm4_key": "162A33D0C1A22908D55AD0AE9D44ED99"
            }
        },
        "jianbaolife":{
            "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrB07tkF8LFVzAvEtb+p/ZAAaY1bTSm1JVulodgBesFsDc3S0QEjHPl1qS4lmwb7/YT34ItTnMiDHAaNsv53ob3e1wYrpSILV+Rs8yZNF+PD7gKjGAnQa9hJnSMRBVNYKws9oRN69h/31Zh3IxXn6ar7LSJuzOT077Y9f9mXV4PQIDAQAB",
            "priviateKey": "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKsHTu2QXwsVXMC8S1v6n9kABpjVtNKbUlW6Wh2AF6wWwNzdLRASMc+XWpLiWbBvv9hPfgi1OcyIMcBo2y/nehvd7XBiulIgtX5GzzJk0X48PuAqMYCdBr2EmdIxEFU1grCz2hE3r2H/fVmHcjFefpqvstIm7M5PTvtj1/2ZdXg9AgMBAAECgYEAp5wi8tiw/Gi0HMKry/mDXCQVUwTlEB1eBFv74rtc967Y1dsARaa/ICXBgv/HTA1IYxIMfzYz7clvkJRycrEUzcZiAu7VuhZuvCZ7qEc0NEayFWDeJpubEvgQK79tc+QZ7qzEng6JgdTt2y1+D4ARyb4Nov3cRvO3mGDBxHZePHUCQQD/Ok5Fwafaa4zkdglgz/Tb1hPOS2dMfZsz0C2P+pIjpAyTw26YG+v+hudbhUdMHtgJ7o8YZ+zV8jUX+4TT480fAkEAq4vIl1/jJ/sl5qnrMgpxcBqw5ouBnRbkmAzjqtFT037htEH5ZyZf2Dhud6LOI2mYHncMMX1tjaHfhX4CiuTzIwJAWr9acUcH7YtsDxdBAmEuiLO3WxRIaFQIyQVuDdFkALQlnKxSOFdFM7y3qjzbOt+EpRNjYaQh+3ghFKPjeIt4tQJAXmTWheUve6qI2noZfzmb3hHx0YpmUehs9PXyuOXieHhS/ftnn0jQaIv0zUP6piXOnMTpIrpf9BVkh3IlOARvfQJBAOBg+5Ol611TMFvmq/qrlXxVTx1QLjrVtTdEPKE0iok93kJJXSvUz2U5iSuOYdT0GXSsIIMjFmTqmmu14Gy/hjc=",
            "salt": "ybhmb",
            "url": "https://m.jianbaolife.com/views/lbyz/auth.jsp",
            "bxChannel": "shanghaihuizhong"
        },
        "luban":{
            "url": "https://api.lubanyonggong.com/insurance/policy_personnel_info"
        },
        "huixinbao": {
            "url": "https://app.hxbaoxian.com/baoxg-product/#/welcome"
        },
        "huize": {
            "partnerId": "1100268",
            "url": "https://api.qixin18.com/api/",
            "secretKey": "ONNjQzMTMxNGZiYmE1100268",
            "fileUrl": "https://files.qixin18.com/api/downloadFile"
        },
        "baotong": {
            "partnerId": "536325",
            "appKey": "iyb793984f15fafb84e",
            "url": "https://open.baoinsurance.com/open/v3/route/baoyunChkEncryptSign",
            "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHb6doKbrbAJbxbwMrWKfqC6TrCb0J/HMZenc5N27aZZoeE2UxPtAwPTfQUc6l5icKQEHix3aNPI4+VrnNUUHKwd/dCoXWm1m/4MSS8hcRGrToVyLECVDE9+FVb+ahXIaQbzgyyyp326gOjFvnnTtUHX698FgQjIXWQa5c4tN1DwIDAQAB",
            "privateKey": "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIQQ08xc8tmlYvFLGPmSdteTNH7jICfBGbEZHP2OrEmkcuLmAFHFZvxDg8Ry48Xk+tZkWz2vr3uJvlYZXxnNYY9afUW0YdHJTEpjaaYkAyTHLfDRugw/4KHeaM7NxW2O8UCznNoSQfnQw5al1SIOK7ToejsGHm0ABsxX8CG+ZfAdAgMBAAECgYBgGHp0vsF6uwwGtXVDzF4rOJK/LXctViXipnpgyZr1rvJlxaQ/29q2/k13+X1hdt9KdxWpChgvVSgpMCbx+G7eu/yyjW6QWdDIE/q8+fHMJM6UzlyN0/ds/wC6zMgO/FxsFPSXfAH2BPNUC9iNgf2/Vbjs/KOLio+KhBB5mJPBxQJBALvPtbi+Uc8naD3kzYTwkhFJPjdIbJrJxNpLknPpxwguGcldNflmbjPw05BOjTmWZ0mWfeVqBi1tnXca7OWqnOsCQQC0A8qKRPsQ1Hi60Z0ZU7OI4D0YlO9/AqQZQDYnOB8TWf/+F3vEVKkK1FXBxWI4W5jge+PK+Y495nne5nkLs8UXAkEAlmqcmAGVc9uT2T5Oe5JQ6hdHBO+0S6QdTNgJy1wIU7zYWjWU7NHkoSTXzFOdN+oFfyJQYlSv7eJEowelg6tJUQJBALEnyfupRyuY7fdssh5qsRTTGU1HxSpJfxUajDCsmmiA+d0lXzgn2PQ/YuX/SloMSrchNVxbnBBu8SDbz+QkVqECQGPCGTO893z56U4Wex2ptgJtjYhzEErkQtxbCPkbyu2WaaQOP2rIFwELmhdgTDrJXPBsfgM1iyN5gzSzvwLN00k="
        },
        "fanhua": {
            "appId": "1000268",
            "appSecret": "Re4V6NtuBrYhyts7tKsr0CPfni1zDb",
            "url": "https://www.fanhuacloud.com",
            "signKey": "8Xz9MhUjSe7NMOLSzHXSTipFFNjhos3IT5fLLUKV"
        },
        "lecheng": {
            "aesKey": "82e8ac94c2cf2a196e5541fe94dd8067"
        },
        "umbrella": {
            "channelId": "202142376",
            "aesKey": "043852dc120f2fff6e7bf8aba3995841",
            "tokenKey": "85d771446092cc004f634abb38c951d7",
            "url": "https://openapi.baodan100.com"
        },
        "anlan": {
            "url": "https://anlan-api-gateway.cn-anlan.com",
            "md5Key": "co2v0rttd2dxeu",
            "aesKey": "9zf346cqtdr3mu93"
        },
        "haierbx": {
            "url": "https://h5.haierbx.net",
            "rsaKey": "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCThTFxHAFM3xQe9/OXFiGORThSLPGuDs7rvxKRRwx9TU3Hx32u54mpkYU4CAdnYv/qFIVcNwDbVD/uB5NP7EY6FpDbmBVOE3Ww+uAG8N7TVcFTr0OJHa1SHQMU57EYP+sZAA5ogoJA6ugzABIgyS/e9lUuMWDNk3fXYpK/RVQstkROucIBfZ40RftwYxIDzr1xicQ6N07mNFk9+tpzyT2jwAH10hzdzNrhMfF5dDmO9wA8LSJRebEZoSREb/G8fsbkuSPsDajm8+pDTRomH49aM0vgZ4bMMKJlxV1iCx7BDG9o3nVBA8OzJe8n7DOnr12KE5/qmN0qt7dac00+VQinAgMBAAECggEBAII8zfihE9jM/ZOVrl2N8VQMcnvCKCBV/IKO7xKJWlJpZCjjS2eWDKhCzA06sWaqNWtwcIiIGt6IE+TOlVSzMRDpiTwFmZGTho0X/57BGsgTZpm+Gu6735+l82mKX4QcMi+hKBdGWT585UDZaCe/1t6ONYeRmngEIIJsCUiKKZVKgf48tgUgcjkYLQjL7prGw67d6C+94Zeo1w2SCnSMcMDp0OFN+IAFzbPwmxxbSwJQ7I8PwkEVnxmupmMGtlowFQM5hTjIMDUY0mQVXQkpmYLDJA5/S4RQRt5X1KkR1XQ30rap5CHyqc7+x6+0w8H99D3bSpK5DoqVJJ3W7SKjC5ECgYEAxFZh0uScGt02hypGOnQzQsF4XkStjkt2zn7UYodwIzxeTeuCn4FLy0XhnTJntZJGXqICeewqW4tewRw9bX9Hfajh13VXdhjqlfwxuWOrwD1+lRIPu6dsl2/toX9zh1LCubCrNurJmmtEu3VRD7gsGH59OodvmR8FKhifTKlc888CgYEAwFkxml/jfmL5UnCBIatFvQEptN4702JfyFovoa8XM9tQyydRigP9XG1GZVfbIIwLsGS/ZX9Lu1dTQgFbstbDe0B+R3nVaMYzT8UCMoQra6EDxvPrSSt3KTnWChAcF1gkTza+eME+orNl54kno8+BwWcJQELJ1N8jpHNhdIm626kCgYEAwtJQmZr9h+6wN+lr7H73iqs+DJSrf0JErtzNVMO+M0io7zrdz/bVxewe1wR4QoTWTPsH6AG/ej1OleK21ZtxzjAxgcHPOsG4rYGluac8ezLKE5PrhrK4n4CSVSmJ8uLdlLLJAmJyEEeW2UitWiLPprKFAvn3dAKC9mWeVMzODqMCgYApxTTYaWplz+iE1pm0ThVe7hm4hWpIhVx3jd46JmPLhx0MgQVcC3HB2Jko+ONFNiRzse0+hV5U2Knj85eORsU8xsyliPenQSNzdzz662jifpzrX50AzseZ1E1+8fCnwSRhb2n9nzmS65J9rXNGR5HOBiokIlziNXLwC35maGKDwQKBgB2ERYh9I45fl5uBGoiscdjzGNZ6BhNklI/G+5vbmAKA3i2QN/pexT9aHlvLc1vLYjVJTjs4ktXUmRxEhNPxqDM+vmD5q0vG0jQY74T4lztdGj9oQJYPo0BC2CaTDYtNABSFFU5oFgDmuTNt3w8kb7eoqvvc3rAJJPRD3ipVPjHG"
        },
        "lianchuang": {
            "url": "https://lcmb-h5.lcbx.com",
            "aesKey": "3b629d2b2576751351f2762588c225f4"
        },
        "aig": {
            "url": "https://portal.connect.aig.com.cn/issuing/Home/Index/policy",
            "pid": "1003004000405137313",
            "secret": "25b87c363ece8c638a84a26931ea605d"
        },
        "wutongshu": {
            "url": "https://ins-openapi.lazhuyun.cn",
            "desKey": "20,50,88,12,34,55,34,90",
            "accessKey": "MTNTU5AF/OE3T",
            "accessSecret": "xNy00MDJiLTdmc0d0cVFjcWEjNWp3QE2ofF/Oe3T"
        },
        "shuzhikong": {
            "userCode": "342718"
        },
        "sunshine": {
            "md5Key": "ygbx20211130"
        },
        "cschat": {
            "url": "https://cschat.antcloud.com.cn/index.htm?tntInstId=SxI_8hor&scene=SCE01240694"
        }
    },

    "proposalConfig": {
        "presentedScene": {
            "nuanping": {
                "enabled": true,
                "userCodes": [
                    "228396"
                ],
                "timeRanges": [
                    "2019-09-01 00:00:00",
                    "2020-01-31 23:59:59"
                ],
                "productCodes": [
                    "ALSEYMJZ",
                    "FBGRYWSHBX"
                ]
            }
        }
    },

    "view engines": {
        "js": {
            "module": "engine-munger",
            "renderer": {
                "method": "js",
                "arguments": [
                    { "cache": true },
                    {
                        "views": "config:express.views",
                        "view engine": "config:express.view engine",
                        "specialization": "config:specialization",
                        "i18n": "config:i18n"
                    }
                ]
            }
        }
    },




    "specialization": {
    },

    "middleware": {



        "static": {
            "module": {
                "arguments": [ "path:./.build" ]
            }
        },
        "firstRouter": {
            "enabled": true,
            "priority": 10,
            "module": {
                "name": "path:./lib/middlewear/special_handler",
                "arguments": [
                    "combined"
                ]
            }
        },
        "router": {
            "module": {
                "arguments": [{ "directory": "path:./controllers" }]
            }
        },

        /**
         * Override the default lusca configuration to disable CSRF handling.
         */
        "appsec": {
            "module": {
                "arguments": [
                    {
                        "xframe": "SAMEORIGIN",
                        "p3p": false,
                        "csp": false
                    }
                ]
            }
        },

        /**
         * Enable *ONLY* CSRF filtered by route.
         * Note: The route "regex" needs the double parens
         * because of how express parses route strings.
         */
        "csrf": {
            "enabled": true,
            "priority": 111,
            "route": "/((?!api))*",
            "module": {
                "name": "lusca",
                "method": "csrf",
                "arguments": [ {} ]
            }
        },
        "json": {
            "enabled": true,
            "priority": 70,
            "module": {
                "name": "body-parser",
                "method": "json",
                "arguments": [
                    {
                        "extended": true,
                        "limit": "5mb"
                    }
                ]
            }
        }


    }
}
