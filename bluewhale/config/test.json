{
    "restConfig": {
        "esbRestUrl": "http://************:18002",
        "racoonRestUrl": "http://172.19.215.219:8838",
        "tigerRestUrl": "https://testpay.99bx.cn",
        "umRestUrl": "https://testum.99bx.cn",
        "apisRestUrl": "https://testesb.99bx.cn",
        "camelRestUrl": "https://testcamel.99bx.cn",
        "pandaRestUrl": "https://testpanda.99bx.cn",
        "oprRestUrl": "https://testopr.99bx.cn",
        "pigeonRestUrl": "http://172.19.215.219:8833",
        "cashvalueRestUrl": "http://************:8050"
    },

    "authConfig": {
        "authMockFlag": false,
        "client": "bluewhale",
        "secret": "57d6745a5514691f2bb56fc1"
    },

    "ossConfig": {
        "policy": {
            "type": {
                "resource": "policy",
                "filetype": "pdf"
            }
        },
        "invoice": {
            "type": {
                "resource": "invoice"
            }
        },
        "junziqian": {
            "type": {
                "resource": "junziqian",
                "filetype": "pdf"
            }
        },
        "litigation": {
            "type": {
                "resource": "litigation",
                "filetype": "jpg"
            }
        },
        "sign": {
            "type": {
                "resource": "sign",
                "filetype": "pdf"
            }
        },
        "camelUrl": "https://testcamel.99bx.cn",
        "clientId": "bluewhale"
    },
    "sentry": "https://<EMAIL>/3",
    "databaseConfig": {
        "host": "************:27077",
        "database": "huibaoDB",
        "options": {
            "useNewUrlParser": true,
            "useUnifiedTopology": true,
            "useCreateIndex": true,
            "user": "huibao",
            "pass": "FsdoX001$DT112lC"
        }
    },
    "loggerLevel": "debug",
    "cacheConfig": {
        "port": 6379,
        "host": "r-uf60bkkcit2uxrnxmp.redis.rds.aliyuncs.com",
        "options": {},
        "password": "780fdbad2df545ae_Redisat2016"
    },

    "param": {
        "directlyFailTime": "14:30:00 +0800",
        "policyWarningList": [],
        "policyErrorList": [],
        "intelligent": {
            "ALCX": {
                "requestUrl": "https://test.allianz.cn/uwApp/intelligence.html#/underWriting",
                "callbackUrl": "https://testbw.99bx.cn/api/intelligent/ALCX/",
                "cid": "BA100170",
                "key": "87D6EDFEAF88419"
            },
            "PAJK": {
                "requestUrl": "http://test-mobile.health.pingan.com/ehis-hm/aitest/outindex.do",
                "callbackUrl": "https://testbw.99bx.cn/api/intelligent/PAJK/",
                "signKey": "nPOz6ehKGANC0w5m"
            },
            "HGRS": {
                "requestUrlUWUrl": "https://wapuat.huaguilife.cn/plugin/preparedUW", // 智能核保 2.0
                "preparedUWInfoUrl": "https://wapuat.huaguilife.cn/plugin/preparedUWInfo", // 智能核保2.0数据查询接口
                "callbackUrl": "https://testbw.99bx.cn/api/intelligent/HGRS/",
                "signKey": "LHgAkgbTuLAjxZcX",
                "debugMode": true,
                "channel": "hzjj"
            },
            "ZAZX": {
                "requestUrl": "https://ihealth-test.zhongan.com/mobile/intelligencyUnderwriting/confirm",
                "callbackUrl": "https://testbw.99bx.cn/api/intelligent/ZAZX/"
            },
            "HQRS": {
                "callbackUrl": "https://testbw.99bx.cn/api/intelligent/HQRS/"
            },
            "TKZX": {
                "requestUrl": "http://ecuat.tk.cn/channel/jq/zhinenghebao/index.html?encryption=",
                "callbackUrl": "https://testbw.99bx.cn/api/intelligent/TKZX/",
                "signKey": "tkcwuhan"
            },
            "RHJK": {
                "backhaulCallbackUrl": "https://testbw.99bx.cn/api/backhaul/RHJK/0rGFu1usO63BLxcC",
                "cpsHost": "https://backendwxu.rhassurance.com",
                "channel": "A2020020"
            },
            "TPCX": {
                "requestUrl": "http://************:18002/external/insure/TPCX/intelligent/callback",
                "callbackUrl": "https://testbw.99bx.cn/api/intelligent/TPCX/",
                "deptCode": "99990067"
            },
            "JLRS": {
                "requestUrl": "http://************:18002/external/insure/JLRS/intelligent/callback",
                "callbackUrl": "https://testbw.99bx.cn/api/intelligent/JLRS/",
                "deptCode": "99990067"
            }
        },
        "issue": {
            "RSCX": {
                "requestUrl": "https://test-micro-personal-sz.chinalife-p.com.cn/ThirdPartPlat/EncodeExecute.action",
                "invoiceUrl": "https://test-micro-personal-sz.chinalife-p.com.cn/ElecInvoicePrintInfoController/policyDigitalInvoice",
                "password": "123456",
                "secretKey": "F028%$#1702ES158"
            },
            "RSCX2": {
                "requestUrl": "http://wx-gd.chinalife-p.com.cn/headbranchdocksystemnew/request",
                "invoiceUrl": "http://wx-gd.chinalife-p.com.cn/dev/pay-server/china-life/ElecInvoiceIssue"
            },
            "PACX": {
                "channel": "P_SHHZ_GP",
                "departmentCode": "_23515_0001",
                "uploadUrl": "http://stg-iobs-upload.pingan.com.cn/upload"
            },
            "XFRS": {
                "sm4_key": "162A33D0C1A22908D55AD0AE9D44ED99"
            }
        },
        "jianbaolife":{
            "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCFg/mcv3IDE0TfGpXpuriOnKwqSmLAnyQV9uqWodxWwKdZzENRDjLhx5uHukRCGHnhHybTSGVV1FejCLXX/MejBn8ncAbPpIqx8dYHi9OVS8rfcJiLvbz+522WoJbZQzWCuIG8xBUC8wi945HKbwtQPWEPmqUNXjq8kY2UJsMyKQIDAQAB",
            "priviateKey": "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIWD+Zy/cgMTRN8alem6uI6crCpKYsCfJBX26pah3FbAp1nMQ1EOMuHHm4e6REIYeeEfJtNIZVXUV6MItdf8x6MGfydwBs+kirHx1geL05VLyt9wmIu9vP7nbZagltlDNYK4gbzEFQLzCL3jkcpvC1A9YQ+apQ1eOryRjZQmwzIpAgMBAAECgYEAgZ9RVRagj8/DS366EILQ7tO4ag5xtWRLWm+v7LIUAr1jO9ENgcHxMDXkD2+NGUY9/YuK3BErIJrYwlWQhbLJ9VP22D8YIDKQuwAzpEzydvWONi7r60/YkqvoERSt7uBzKRQkSFKx6ZQguPs8A+86zDFsZ5Wck14iJt9QHaeSKwECQQDiBf5lKmd3FkCPG3lTGzeN7GOohwIHs1BpOMU0iXIgQJK5U+dsyisqjedOTyZ401N7zCaF4w1oXcw+7mGfUKvRAkEAlzkhGap+fTq1DrDpxzrba4b3Es8Rjcl9CLd/bY2V8c4vVnWq0xpZtdFMjXd/JlIkebQIRXMghAxAT/ROjuIu2QJBAMOpBqFTBQA9PiPkV7HOxNfSRC9WuDYsTzbXUzf4p9gtrhZLAPLXTGjpMMxEjnxYDFQF1W9fr3KiiDYOLGo67pECQFJK9IhTD6J2YDEudQnGLcHGII5LwPG3xCMKOH4VnpOH8JrMTbmzjT40N8SEUt03tkHEhvvEOt2hDR51hIqc64ECQFgp15u3Q0kl7QWXbvloq/JPGgceV4LVWnnd8v8cz461iWvkEPIVE06i0tnR1V4gMR5uqeb6fFE5yVDcRVZ8nJ4=",
            "salt": "ybhmb",
            "url": "https://m-test.jianbaolife.com/views/lbyz/auth.jsp",
            "bxChannel": "shanghaihuizhong"
        },
        "luban":{
            "url": "https://test.lubanyonggong.com/insurance/policy_personnel_info"
        },
        "huixinbao": {
            "url": "https://develop.xinwuyou.cn/test-baoxg/#/welcome"
        },
        "huize": {
            "partnerId": "1100268",
            "url": "https://tuneapi.qixin18.com/api/",
            "secretKey": "TNMTRhMzY1ZjFmNmV1100268",
            "fileUrl": "https://files-test.qixin18.com/api/downloadFile"
        },
        "baotong": {
            "partnerId": "491686",
            "appKey": "test123",
            "url": "https://open-uat.baoinsurance.com/open/v3/route/baoyunChkEncryptSign",
            "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCVKp7PDkjdXh+WmVCuhHj9GkcW5J4x/HzZ+krsCcSwv1Vl9/eXxuGqaJvy/O87LIZJgybsuIOH6blVpE0CVReCu5ItTAZnyGlsW4KuUgC0/VIEiQo+LR0MAn7peJyJcvnV/ex178665ZhE4mMVPagdijwoUdqOL6va9DECuGhf4QIDAQAB",
            "privateKey": "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAOMKJZpqbnsf05yqTYx5EtkOyD7NV61VHRitjIE5d+BeSPnnZ3Mrj2Z6qZbLIHuQfwDtMhy+R4hN5jVkj+Sm1o5dRVl41GDZ2CdXAdhHRSSTf0V9WJlNrrAWNYjNFrhMMBkZEZkN/mJq5jLvh4sJucHV32KboabbtrIMkbGIxfW1AgMBAAECgYBeCArQ7Hm7hs/219XscwyqV2P6FsPUZyb43JAPyURr6cDht8Rxv73RnMX2TUSzyIHBWXviybAItPK+dFrgQXUkk5QaV9rp+VbyQICRXlaTdS4y4H+yhvmq1JKVUXj619Rq/O3lBan2k1+WPzOd2+cpKmbOSCvxLI2sA7QlD+ZA4QJBAPh51QPrsGWl+Hg12Q4WaWhsAAPnxdvSjlN0JDHYRA8zFnt1hcsDI4UJvCu0oq3v0ZOHTQ/Roj8UAduai4XRSF0CQQDp6iRvRqEAre249ZKGoBcxqWbkpLyiCzJxYFWLMrffn4bQax3bf7MoPR8vjVSn6RcXx6St4vZzUUKwC1pf/q05AkEAxJFtSSq9Y8zHIiJHUnO8c9Vstao3xs1ttuucbqpOPVAHKygv7gjPosGu0UyXnc6pF3LkLe2D0y76wUYjtnt4KQJALcdPHh/6foGLlrko1gQaxfNbWcAL0sLSOnIaC8X/SlqULdHo4/5X97YOlmXYfYwWoubFiepzRRpQLNfr9gGHsQJAOXRHSjbA5Zd+2GxkXMWnFo+204MIazUfTHsNdccYjAx3TPQIijpbeQWg/MC3lARESKOb7Y0Aw6ARTNsDuZWlzA=="
        },
        "fanhua": {
            "appId": "1015581",
            "appSecret": "mHqQJxbkRPrptLgT3QutWZrz29GJvU",
            "url": "https://uat.fanhuacloud.com",
            "signKey": "Ijnx6bBp07sbLVss8OX8yoXe7EfkJwphgWqYAyCF"
        },
        "lecheng": {
            "aesKey": "e10adc3949ba59abbe56e057f20f883e"
        },
        "umbrella": {
            "channelId": "202142376",
            "aesKey": "043852dc120f2fff6e7bf8aba3995841",
            "tokenKey": "85d771446092cc004f634abb38c951d7",
            "url": "https://openapi.baodan100.com"
        },
        "anlan": {
            "url": "https://anlan-api-gateway-test.cn-anlan.com",
            "md5Key": "anlanMD5123456",
            "aesKey": "anlanAES12345678"
        },
        "haierbx": {
            "url": "http://p2-h5.haierbx.net",
            "rsaKey":"MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCThTFxHAFM3xQe9/OXFiGORThSLPGuDs7rvxKRRwx9TU3Hx32u54mpkYU4CAdnYv/qFIVcNwDbVD/uB5NP7EY6FpDbmBVOE3Ww+uAG8N7TVcFTr0OJHa1SHQMU57EYP+sZAA5ogoJA6ugzABIgyS/e9lUuMWDNk3fXYpK/RVQstkROucIBfZ40RftwYxIDzr1xicQ6N07mNFk9+tpzyT2jwAH10hzdzNrhMfF5dDmO9wA8LSJRebEZoSREb/G8fsbkuSPsDajm8+pDTRomH49aM0vgZ4bMMKJlxV1iCx7BDG9o3nVBA8OzJe8n7DOnr12KE5/qmN0qt7dac00+VQinAgMBAAECggEBAII8zfihE9jM/ZOVrl2N8VQMcnvCKCBV/IKO7xKJWlJpZCjjS2eWDKhCzA06sWaqNWtwcIiIGt6IE+TOlVSzMRDpiTwFmZGTho0X/57BGsgTZpm+Gu6735+l82mKX4QcMi+hKBdGWT585UDZaCe/1t6ONYeRmngEIIJsCUiKKZVKgf48tgUgcjkYLQjL7prGw67d6C+94Zeo1w2SCnSMcMDp0OFN+IAFzbPwmxxbSwJQ7I8PwkEVnxmupmMGtlowFQM5hTjIMDUY0mQVXQkpmYLDJA5/S4RQRt5X1KkR1XQ30rap5CHyqc7+x6+0w8H99D3bSpK5DoqVJJ3W7SKjC5ECgYEAxFZh0uScGt02hypGOnQzQsF4XkStjkt2zn7UYodwIzxeTeuCn4FLy0XhnTJntZJGXqICeewqW4tewRw9bX9Hfajh13VXdhjqlfwxuWOrwD1+lRIPu6dsl2/toX9zh1LCubCrNurJmmtEu3VRD7gsGH59OodvmR8FKhifTKlc888CgYEAwFkxml/jfmL5UnCBIatFvQEptN4702JfyFovoa8XM9tQyydRigP9XG1GZVfbIIwLsGS/ZX9Lu1dTQgFbstbDe0B+R3nVaMYzT8UCMoQra6EDxvPrSSt3KTnWChAcF1gkTza+eME+orNl54kno8+BwWcJQELJ1N8jpHNhdIm626kCgYEAwtJQmZr9h+6wN+lr7H73iqs+DJSrf0JErtzNVMO+M0io7zrdz/bVxewe1wR4QoTWTPsH6AG/ej1OleK21ZtxzjAxgcHPOsG4rYGluac8ezLKE5PrhrK4n4CSVSmJ8uLdlLLJAmJyEEeW2UitWiLPprKFAvn3dAKC9mWeVMzODqMCgYApxTTYaWplz+iE1pm0ThVe7hm4hWpIhVx3jd46JmPLhx0MgQVcC3HB2Jko+ONFNiRzse0+hV5U2Knj85eORsU8xsyliPenQSNzdzz662jifpzrX50AzseZ1E1+8fCnwSRhb2n9nzmS65J9rXNGR5HOBiokIlziNXLwC35maGKDwQKBgB2ERYh9I45fl5uBGoiscdjzGNZ6BhNklI/G+5vbmAKA3i2QN/pexT9aHlvLc1vLYjVJTjs4ktXUmRxEhNPxqDM+vmD5q0vG0jQY74T4lztdGj9oQJYPo0BC2CaTDYtNABSFFU5oFgDmuTNt3w8kb7eoqvvc3rAJJPRD3ipVPjHG"
        },
        "lianchuang": {
            "url": "https://lcmb-h5-dev.lcbx.com",
            "aesKey": "e10adc3949ba59abbe56e057f20f883e"
        },
        "aig": {
            "url": "https://release-portal.connect.aig.com.cn/issuing/Home/Index/policy",
            "pid": "1002806000509437061",
            "secret": "eae882afd766e49a06e841e109f92552"
        },
        "wutongshu": {
            "url": "https://fqjdapi.wts9999.net",
            "desKey": "32,55,89,40,53,56,72,36",
            "accessKey": "MTNTU23GWZI1V",
            "accessSecret": "xNy00MDJiLTdmbUxGTzhahtg0AASxfquvTGwzI1V"
        },
        "shuzhikong": {
            "userCode": "50091"
        },
        "sunshine": {
            "md5Key": "ygbx20211130"
        },
        "cschat": {
            "url": "https://cschat.antcloud.com.cn/index.htm?tntInstId=SxI_8hor&scene=SCE01262140"
        }
    },

    "proposalConfig": {
        "presentedScene": {
            "nuanping": {
                "enabled": true,
                "userCodes": [
                    "124796"
                ]
            }
        }
    },

    "express": {
        "view cache": false,
        "view engine": "dust",
        "views": "path:./public/templates"
    },


    "view engines": {
        "dust": {
            "module": "engine-munger",
            "renderer": {
                "method": "dust",
                "arguments": [
                    { "cache": false },
                    {
                        "views": "config:express.views",
                        "view engine": "config:express.view engine",
                        "specialization": "config:specialization",
                        "i18n": "config:i18n"
                    }
                ]
            }
        }
    },



    "middleware": {

        "devtools": {
            "enabled": true,
            "priority": 35,
            "module": {
                "name": "construx",
                "arguments": [
                    "path:./public",
                    "path:./.build",
                    {

                        "template": {
                            "module": "construx-dustjs-i18n",
                            "files": "/templates/**/*.js",
                            "base": "templates"

                        },


                        "copier": {
                            "module": "construx-copier",
                            "files": "**/*"
                        }
                    }
                ]
            }
        }
    }
}
