'use strict';
const Promise = require('bluebird');
const cors = require('cors');
const co = require('co');
const moment = require('moment');

const logger = require('@huibao/logger').logger();
const Contract = require('../../../models/Contract');
const OrderInfo = require('../../../models/OrderInfo');
const policyService = require('../../../services/policyService');
const PropertyProposal = require('../../../models/PropertyProposal');

module.exports = app => {

    app.options('*', cors());

    // 海保人寿 协议支付异步回调接口
    app.post('/hbrs/callback', cors(), (req, res, next) => {
        const policyJson = req.body;
        console.log('payCallBack_req===>', JSON.stringify(policyJson));
        const reqObj = policyJson.INSUREQ.PAYINFO;
        const resObj = { equestNumber: getTranNo(), policyNo: reqObj.POLICY_NO };
        co(function* () {
            let proposal = yield PropertyProposal.findOne({ 'mainInfo.policyNo': reqObj.POLICY_NO, status: '1' });
            if (!proposal) return Promise.reject('投保单号不存在或已处理!');
            if (reqObj.STATE === '0') {
                proposal.status = '14';
                proposal.failReason = policyJson.INSUREQ.POLICYINFO.ERR_INFO;
                yield proposal.save();
                const orderInfo = yield OrderInfo.findOne({ oid: proposal.oid });
                orderInfo.policyNo = policyJson.INSUREQ.POLICYINFO.POLICY_NO;
                orderInfo.payFailureRemark = policyJson.INSUREQ.POLICYINFO.ERR_INFO;
                yield orderInfo.save();
            } else {
                if (!reqObj.PREM) return Promise.reject('支付金额为空');
                proposal.amount = reqObj.PREM;
                proposal.payChannel = reqObj.PAYMODE;
                proposal.cardInfo.cardNo = reqObj.BANKACCNO;
                proposal.cardInfo.bankCode = reqObj.BANKCODE;
                proposal.status = '12';
                proposal = yield proposal.save();
                yield policyService.policyBefore(proposal);
            }
            resObj.errInfo = '处理成功';
            resObj.resultCode = '1';
            res.json(resObj);
        }).catch(err => {
            logger.info('policyPay error:', err);
            resObj.errInfo = '处理失败：' + err;
            resObj.resultCode = '0';
            res.json(resObj);
        });
    });

    // 国任财险 保司支付后异步回调接口
    app.post('/grcx/callback', cors(), (req, res, next) => {
        const reqObj = req.body;
        console.log('grcx_callback_req===>', JSON.stringify(reqObj));

        const resObj = { equestNumber: getTranNo(), orderId: reqObj.orderId };
        let poar = reqObj.policyNo.split(':');//保单号中带冒号要手工处理下 太垃圾了
        let policyNo = poar[poar.length - 1];
        // 投保单号也一样
        let prar = reqObj.proposalNo.split(':');
        let proposalNo = prar[prar.length - 1];
        co(function* () {
            let proposal = yield PropertyProposal.findOne({ 'mainInfo.proposalNo': reqObj.orderNoMall, status: '1' });
            if (!proposal) return Promise.reject('投保单号不存在或已处理!');
            if (reqObj.result === '0' && policyNo) { //已支付 有保单
                proposal.status = '12';
                proposal.mainInfo.proposalNo = proposalNo;
                proposal.mainInfo.policyNo = policyNo;
                proposal.amount = proposal.mainInfo.sumPremium;
                proposal.payTime = new Date();
                proposal.payChannel = '5';
                proposal = yield proposal.save();
                yield policyService.policyBefore(proposal);
            }
            resObj.errInfo = '处理成功';
            resObj.resultCode = '1';
            res.json(resObj);
        }).catch(err => {
            logger.info('policyPay error:', err);
            resObj.errInfo = '处理失败：' + err;
            resObj.resultCode = '0';
            res.json(resObj);
        });
    });

    // 华农财险 保司支付后异步回调接口
    app.post('/hncx/callback', cors(), (req, res, next) => {
        const reqObj = req.body;
        logger.info('hncx_callback_req===>', JSON.stringify(reqObj));
        const resObj = {};
        resObj.head = reqObj.head;
        co(function* () {
            if (!reqObj.policyNotice) return Promise.reject('数据格式错误!');
            const policyNotice = reqObj.policyNotice; // 数组中只有一个
            if (!policyNotice.proposalNo) return Promise.reject('投保单号为空无法处理!');
            if (!policyNotice.policyNo) return Promise.reject('保单号为空无法处理!');

            let proposal = yield PropertyProposal.findOne({ 'mainInfo.proposalNo': policyNotice.proposalNo, status: '1' });
            if (!proposal) return Promise.reject('投保单号不存在或已处理!');
            proposal.status = '12';
            proposal.mainInfo.policyNo = policyNotice.policyNo;
            proposal.payChannel = '5';
            proposal.extInfo = proposal.extInfo || {};
            proposal.extInfo.eUrl = policyNotice.elecPolicyUrl;
            proposal.amount = proposal.mainInfo.sumPremium;
            proposal = yield proposal.save();
            yield policyService.policyBefore(proposal);
            resObj.respBody = {
                policyNo: proposal.mainInfo.policyNo,
                dealCode: '1',
                dealDesc: '处理成功',
            };
            res.json(resObj);
        }).catch(err => {
            logger.info('policyPay error:', err);
            resObj.respBody = {
                dealDesc: '处理失败：' + err,
                dealCode: '0',
            };
            res.json(resObj);
        });
    });

    // 平安财险 保司支付后异步回调接口
    app.post('/pacx/callback', cors(), (req, res, next) => {
        const reqObj = req.body;
        logger.info('pacx_callback_req===>', JSON.stringify(reqObj));
        co(function* () {
            if (!reqObj.applyPolicyNo) return Promise.reject('投保单号为空无法处理!');

            let data = { responseCode: '999999', responseMsg: '处理成功' };
            let proposal = yield PropertyProposal.findOne({ 'mainInfo.proposalNo': reqObj.applyPolicyNo });
            if (!proposal) return Promise.reject('投保单号不存在或已处理!');
            if(proposal.status == '2') {
                return res.json(data);
            }
            // 已承保并且有保单号
            if(reqObj.status == 'B5' && reqObj.policyNo) {
                proposal.status = '12';
                proposal.payChannel = '5';
                proposal.payTradeId = reqObj.tradeNo || `PACX${+moment()}`;
                proposal.mainInfo.policyNo = reqObj.policyNo || '';
                proposal.amount = proposal.mainInfo.sumPremium;
                proposal = yield proposal.save();
                yield policyService.policyBefore(proposal);
            }
            res.json(data);
        }).catch(err => {
            logger.info('pacx_policy_pay error:', err);
            res.json({ responseCode: '800000', responseMsg: err });
        });
    });

    // 平安财险发票通知 合作方-->我方
    app.post('/pacx/invoice', (req, res, next) => {
        const body = req.body;
        console.log('pacx_invoice_req===>', JSON.stringify(body));
        co(function* () {
            let [oid,] = body.applyNo.split('_');
            let proposal = yield PropertyProposal.findOne({ 'oid': oid, status: '2' });
            if (!proposal) return Promise.reject('保单不存在或已处理!');
            let elec_url = body.data[0].invoicePdfUrl;
            proposal.extInfo.evUrl = elec_url;
            proposal.evFlag = '2';
            yield proposal.save();
            res.json({ responseCode: '999999', responseMsg: '通知成功' });
        }).catch(err => {
            logger.info('pacx_invoice_res_error:', err);
            res.json({ responseCode: '800000', responseMsg: '处理失败:' + err });
        });
    });


    // 大家人寿 保司支付后异步回调接口
    app.post('/djrs/callback', cors(), (req, res, next) => {
        const reqObj = req.body;
        logger.info('djrs_callback_req===>', JSON.stringify(reqObj));
        const resObj = {};
        co(function* () {
            if (!reqObj) return Promise.reject('数据格式错误!');
            if (!reqObj.transNo) return Promise.reject('交易号为空无法处理!');

            let proposal = yield PropertyProposal.findOne({ 'extInfo.tradeId': reqObj.transNo, status: '1' });
            if (!proposal) return Promise.reject('投保单号不存在或已处理!');
            if (reqObj.payResult == 'PAD') { // 已支付
                proposal.status = '12';
                proposal.amount = proposal.mainInfo.sumPremium;
                proposal.payTime = new Date();
                proposal.payChannel = '5';
                proposal = yield proposal.save();
                yield policyService.policyBefore(proposal);
            } else {
                return Promise.reject('状态非已支付!' + reqObj.payResult);
            }
            resObj.msg = '处理成功';
            resObj.resultCode = '200';
            logger.info('djrs_callback_res===>', JSON.stringify(resObj));
            res.json(resObj);
        }).catch(err => {
            logger.info('policyPay error:', err);
            resObj.msg = '处理失败：' + err;
            resObj.code = '999';
            res.json(resObj);
        });
    });

    // 中国人寿财险 保司支付后异步回调接口
    app.post('/rscx/callback', cors(), (req, res, next) => {
        const reqObj = req.body;
        console.log('rscx_callback_req===>', JSON.stringify(reqObj));

        let proposalNo = reqObj.appNo || reqObj.proposalNo;
        let policyNo = reqObj.polNo || reqObj.policyNo;

        const resObj = {};
        co(function* () {
            if (!policyNo) return Promise.reject('保单号为空无法处理!');
            if(!reqObj.state) reqObj.state = '1';
            let proposal = yield PropertyProposal.findOne({ 'mainInfo.proposalNo': proposalNo, status: '1' }); // 不处理
            if (!proposal) return Promise.reject('投保单号不存在或已处理!');
            if (reqObj.state == '1') { // 已承保
                proposal.status = '2';
                proposal.mainInfo.policyNo = policyNo;
                proposal.amount = proposal.mainInfo.sumPremium;
                proposal.payTime = new Date();
                proposal.payChannel = '5';
                if(reqObj.policyUrlAddr){
                    proposal.extInfo ? null : proposal.extInfo = {};
                    proposal.extInfo.eUrl = reqObj.policyUrlAddr;
                }
                proposal = yield proposal.save();
                yield policyService.policyBefore(proposal);
            } else {
                return Promise.reject('状态非已承保!');
            }
            resObj.msg = '处理成功';
            resObj.code = '10000';
            resObj.errorCode = '0000';
            res.json(resObj);
        }).catch(err => {
            logger.info('policyPay error:', err);
            let msg = '处理失败：' + err;
            resObj.msg = msg
            resObj.code = '999';
            resObj.errorCode = '9999';
            resObj.errorDesc = msg;
            res.json(resObj);
        });
    })


    // 泰康在线财险 保司支付后异步回调接口
    app.post('/tkzxcx/callback', cors(), (req, res, next) => {
        const reqObj = req.body;
        console.log('tkzxcx_callback_req===>', JSON.stringify(reqObj));

        const trade_billno = reqObj.trade_billno;
        const billno = reqObj.billno;
        const payJournalId = reqObj.payJournalId;
        const paywayType = reqObj.paywayType;
        const resObj = {
            process_date: moment().format('YYYY-MM-DD HH:mm:ss'),
            billno,
        };
        co(function* () {
            if (!payJournalId) return Promise.reject('保单号为空无法处理!');
            let proposal = yield PropertyProposal.findOne({ oid: trade_billno, status: '1' });
            if (!proposal) return Promise.reject('投保单号不存在或已处理!');
            if (proposal.status !== '2') { // 已承保
                proposal.status = '2';
                proposal.mainInfo.policyNo = payJournalId;
                proposal.amount = proposal.mainInfo.sumPremium;
                proposal.payTime = new Date();
                proposal.payChannel = '5';
                if (paywayType) {
                    proposal.extInfo ? null : proposal.extInfo = {};
                    proposal.extInfo.payFlag = paywayType;
                    proposal.extInfo.tradeId = billno;
                }
                proposal = yield proposal.save();
                yield policyService.policyBefore(proposal);
            } else {
                return Promise.reject('保单已承保!');
            }
            resObj.message = '处理成功';
            resObj.code = '200';
            res.json(resObj);
        }).catch(err => {
            logger.info('policyPay error:', err);
            resObj.message = '处理失败：' + err;
            resObj.code = '500';
            res.json(resObj);
        });
    });

    // 保司支付完回调SPA做一次中转
    app.post('/providerCode/:productCode/:oid', cors(), function (req, res) {
        co(function* () {
            const body = req.body;
            logger.info('providerCodeBody==>', JSON.stringify(body));
            const oid = req.params.oid;
            const path = global.env === 'production' ? `https://spa.hizom.cn/appui/#/pay/success?oid=${oid}` : `https://t.99bx.cn/appui/#/pay/success?oid=${oid}`;
            res.redirect(path);
        }).catch(err => {
            logger.error('providerCode-err: ', err);
            res.send({ err });
        });
    });

    // 横琴人寿 协议支付异步回调接口
    app.post('/hqrs/callback', cors(), (req, res, next) => {
        const policyJson = req.body;
        console.log('payCallBack_req===>', JSON.stringify(policyJson));
        const resObj = { result: '' };
        co(function* () {
            if (!policyJson.code || !policyJson.data || !policyJson.data.proposalNo) {
                return Promise.reject('数据不完整!');
            }
            const reqObj = policyJson.data;
            let proposal = yield PropertyProposal.findOne({ 'mainInfo.proposalNo': reqObj.proposalNo, status: { $in: ['1', '14'] } });
            if (!proposal) return Promise.reject('投保单号不存在或已处理!');
            if (reqObj.policyState != '1' || policyJson.code != '200') {
                proposal.status = '14';
                proposal.failReason = resultCode[policyJson.code];
                yield proposal.save();
                const orderInfo = yield OrderInfo.findOne({ oid: proposal.oid });
                orderInfo.policyNo = reqObj.policyNo || '';
                orderInfo.payFailureRemark = resultCode[policyJson.code] + ': ' + policyJson.message;
                yield orderInfo.save();
            } else {
                proposal.payTime = new Date(moment(reqObj.effectTime).format('YYYY/MM/DD'));
                proposal.payChannel = '5';
                proposal.status = '12';
                proposal.amount = proposal.mainInfo.sumPremium;
                proposal.mainInfo.startDate = new Date(moment(reqObj.effectTime).format('YYYY/MM/DD'));
                if (!reqObj.policyNo) {
                    return Promise.reject('保单号未传!');
                }
                // temp solution
                proposal.mainInfo.proposalNo = reqObj.proposalNo;
                proposal.mainInfo.policyNo = reqObj.policyNo;
                proposal = yield proposal.save();
                yield policyService.policyBefore(proposal);
            }
            resObj.result = 'success';
            res.json(resObj);
        }).catch(err => {
            logger.info('policyPay error:', err);
            resObj.result = 'failed';
            res.json(resObj);
        });
    });


    // 安联财险 收银台支付异步回调接口
    app.post('/alcx/callback', cors(), (req, res, next) => {
        const policyJson = req.body;
        console.log('alcx_paycallback_req===>', JSON.stringify(policyJson));
        co(function* () {
            if (!policyJson) return Promise.reject('数据格式错误!');
            if (!policyJson.policyRef || !policyJson.payNo) return Promise.reject('!');

            let proposal = yield PropertyProposal.findOne({ 'mainInfo.proposalNo': policyJson.policyRef, status: '1' });
            if (!proposal) return Promise.reject('投保单号不存在或已处理!');
            if (policyJson.tradeStatus == 'SUCCESS') { // 支付成功
                proposal.status = '12';
                proposal.amount = proposal.mainInfo.sumPremium;
                proposal.payTime = moment(policyJson.payTime);
                proposal.payChannel = '5';
                proposal = yield proposal.save();
                yield policyService.policyBefore(proposal);
            } else {
                return Promise.reject('状态非已支付!' + policyJson.tradeStatus);
            }
            res.send('SUCCESS');
        }).catch(err => {
            logger.info('policyPay error:', err);
            res.send('FAILED');
        });
    });

    // 华泰财险 收银台支付异步回调接口
    app.post('/htcx/callback', cors(), (req, res, next) => {
        const policyJson = req.body;
        let t_date = moment().format('YYYY-MM-DD HH:mm:ss');
        console.log('htcx_paycallback_req===>', JSON.stringify(policyJson));
        co(function* () {
            let info = policyJson.contractMainList[0];
            if (!info) return Promise.reject('数据格式错误!');
            if (!info.policyNo) return Promise.reject('保单号为空请核实!');

            let proposal = yield PropertyProposal.findOne({ 'mainInfo.proposalNo': info.orderNo, status: '1' });
            if (!proposal) return Promise.reject('投保单号不存在或已处理!');
            proposal.status = '12';
            proposal.mainInfo.policyNo = info.policyNo;
            proposal.amount = proposal.mainInfo.sumPremium;
            proposal.payTime = moment().toDate();
            proposal.payChannel = '5';
            if(info.policyUrl){
                proposal.extInfo ? null : proposal.extInfo = {};
                proposal.extInfo.eUrl = info.policyUrl;
            }
            proposal = yield proposal.save();
            yield policyService.policyBefore(proposal);

            res.json({ responseDate: t_date, statusText: '同步成功', status: 0 });
        }).catch(err => {
            logger.info('htcx_policyPay error:', err);
            res.json({ responseDate: t_date, statusText: '同步失败 ' + err, status: 99 });
        });
    });

    // 美亚财险 出单后异步回调接口
    app.post('/mycx/callback', cors(), (req, res, next) => {
        const reqObj = req.body;
        console.log('mycx_callback_req===>', JSON.stringify(reqObj));

        const data = JSON.parse(reqObj.MessageText);
        const [time, oid ] = data.header.transactionId.split('_');
        co(function* () {
            if (!data.segment || data.segment.length == 0) return Promise.reject('数据格式错误!');
            let policyObj = data.segment[0]; // 数组中只有一个
            if (!policyObj.policyNumber) return Promise.reject('保单号为空无法处理!');

            if (data.header.transactionType == 'insure') {
                let proposal = yield PropertyProposal.findOne({ oid: oid, status: '12' });
                if (!proposal) return Promise.reject('投保单号不存在或已处理!');
                if (policyObj.errorCode == '0') { // 已承保
                    proposal.asyncFlag = '1';
                    proposal.status = '12';
                    proposal.amount = policyObj.premium;
                    proposal.mainInfo.policyNo = policyObj.policyNumber;
                    proposal.payTime = new Date();
                    if (policyObj.policyPdfUrl) {
                        proposal.extInfo ? null : proposal.extInfo = {};
                        proposal.extInfo.eUrl = policyObj.policyPdfUrl;
                    }
                    proposal = yield proposal.save();
                    yield policyService.policyBefore(proposal);
                } else {
                    return Promise.reject('状态非已承保!');
                }
                console.log('insure success');
            } else if(data.header.transactionType == 'cancel') {
                console.log('cancel success');
            }
            res.send('success');
        }).catch(err => {
            logger.info('mycx_callback_error:', err);
            res.send('fail:' + err);
        });
    });

    // 鼎和财险 收银台支付异步回调接口
    app.post('/dhcx/callback', cors(), async (req, res, next) => {
        const policyJson = req.body;
        // 响应对象
        let resObj = {
            header: { ...policyJson.header },
            body: {
                data: {},
                resultCode: '0000',
                resultMsg: '处理成功',
                success: true,
            }
        }
        try {
            console.log('dhcx_paycallback_req===>', JSON.stringify(policyJson));

            let { body } = policyJson;
            if (!body) throw new Error('数据格式错误');

            let proposal = await PropertyProposal.findOne({ 'mainInfo.proposalNo': body.orderNo, status: '1' });
            if (!proposal) {
                logger.info('鼎和财险支付回调: 订单不存在或已处理', {
                    orderNo: body.orderNo
                });
                return res.json(resObj);
            };
            if (!body.policyNo) {
                throw new Error('无保单号数据异常');
            }
            if (body.policyNo) { // 有保单号表示支付成功
                proposal.status = '12';
                proposal.amount = proposal.mainInfo.sumPremium;
                proposal.payTime = body.payTime? moment(body.payTime) : moment().toDate();
                proposal.payChannel = '5';
                proposal.mainInfo.policyNo = body.policyNo;
                proposal = await proposal.save();
                await policyService.policyBefore(proposal);
            }
            return res.json(resObj);
        }catch(err) {
            logger.error('鼎和财险支付回调处理失败:', {
                error: err.message,
                stack: err.stack,
                policyJson
            });
            resObj.body.resultCode = '9999';
            resObj.body.resultMsg = '处理失败：' + err;
            resObj.body.success = false;
            res.json(resObj);
        };
    });
};

const TransType = {
    1202: '客户编码查询',
    1601: '投被保人客户信息变更',
    1602: '续期自动转账账户变更',
    1603: '复星联合健康平台退保通知',
    1604: '保全详',
    1605: '根据保单号查询保全项',
};

const resultCode = {
    200: '成功，其他都表示失败',
    201: '支付中，待出单',
    300: '支付成功、出单失败,需人工介入',
    500: '支付失败',
};

const sexMap = {
    0: '1',
    1: '2',
};
const IdMap = {
    '01': '1',
    '02': '13',
    '03': '11',
    '04': '2',
    '05': '11',
    10: '11',
    11: '11',
    12: '4',
    99: '11',
    A: '3',
    B: '11',
    C: '11',
    D: '11',
    E: '11',
    F: '11',
};

const productCodeMap = {
    110032: 'KLYSZDJBBK',
    110025: 'KLYSZDJBBXC',
};


function getTranNo() {
    const topstr = moment().format('YYYYMMDD');
    // 可用其他规则保持唯一性
    return topstr + getTimeStamp(8);
}

/**
 * 截取时间戳后几位
 * @param {*} num 截取个数，默认5位
 */
function getTimeStamp(num = 5) {
    const time = moment().format('x');
    return time.substring(time.length - num);
}
