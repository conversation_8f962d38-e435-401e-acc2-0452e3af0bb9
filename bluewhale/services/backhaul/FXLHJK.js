'use strict';

const _ = require('lodash');
const moment = require('moment');

const logger = require('@huibao/logger').logger();

const Contract = require('../../models/Contract');
const ProviderBackhaul = require('../../models/ProviderBackhaul');
const customerService = require('../customerService');
const proposalService = require('../proposalService');
const policyService = require('../policyService');
const commonService = require('../commonService');
const productService = require('../productService');

/**
 * 复星联合健康保单数据回传通知服务
 * @type {module.FXLHJKBackHaulService}
 */

module.exports = class FXLHJKBackHaulService {

        set id(providerId) {
            this.providerId = providerId;
        }

        set code(providerCode) {
            this.providerCode = providerCode;
        }

        /**
         *  需要支持幂等,建议根据订单号或者保单号
         * @param postData
         * @return {Promise<Boolean>}
         */
        * receive(postData) {
            if (!this.providerId || !this.providerCode) {
                throw new Error('请指定供应商标识');
            }
            if (!postData.contractNo
                || !postData.appnt
                || !postData.insuredList
                || !postData.riskList
            ) {
                throw new Error('数据格式不完整');
            }

            let reqObj = postData;

            // 如果订单数据已经同步成功，再次用相同的数据请求时，需要返回成功
            const condition = {
                providerId: this.providerId,
                contractNo: reqObj.contractNo,
            };
            const contractExisted = yield Contract.countDocuments(condition);
            if (contractExisted) {
                return true;
            }

            // 先将原始报文存到数据库里，然后再做后续处理 需要考虑重复通知的情况

            let providerBackhaul = yield ProviderBackhaul.findOne(condition);
            if (!providerBackhaul) {
                providerBackhaul = new ProviderBackhaul(condition);
            }

            let userCode = providerBackhaul.bizTag || reqObj.agent.userCode;
            providerBackhaul.providerCode = this.providerCode;
            providerBackhaul.orderNo = reqObj.proposalNo; // 订单号
            providerBackhaul.bizTag = userCode;  // 跳链带出去的业务参数（userCode）
            providerBackhaul.contract = reqObj; // 供应商回传回来的业务数据内容，每家供应商内容格式不同，先保存后续再解析
            providerBackhaul.handleAt = new Date();

            try{
                yield* commonService.backhaulNextHandle(providerBackhaul, '0');

                // 先走投保流程,数据转换前置一步
                const proposalData = yield* this.mapToProposalData(postData);

                // 如果没有回传用户标识
                if (!userCode) {
                    yield* this.sendMail(proposalData);
                    // bizTag 为空阻断流程，通知运营处理
                    return true;
                }
                console.log('fxlhjk_user:' ,userCode);
                const result = yield global.restClient.userInfo({ userCode });
                if (!result || (result.code !== '000') || !result.data.userCode) {
                    throw new Error(`未获取到${userCode}对应的用户信息`);
                }

                let proposal = yield* this.proposal(proposalData, result.data);

                // 先将投保单状态改成符合手动投保流程的状态
                proposal.status = '12';
                yield proposal.save();

                // 再走承保流程
                const policyJson = {
                    oid: proposal.oid,
                    // 投保单号
                    proposalNo: proposal.mainInfo.proposalNo,
                    // 保单号
                    policyNo: reqObj.contractNo,
                    // 支付金额
                    amount: proposal.mainInfo.amount,
                    // 支付时间 暂时按对方的订单创建时间
                    payTime: moment(reqObj.payDate).toDate(),
                    // 承保时间
                    acceptDate: proposal.mainInfo.acceptDate,
                    // 手动补单机制
                    manualFlag: true,
                };

                logger.info('policyJson:', policyJson);

                proposal = yield* this.policy(policyJson);

                yield* commonService.backhaulNextHandle(providerBackhaul, '1');

                return true;
            }catch(error){
                console.log('error ',error.message || error);
                yield* commonService.backhaulNextHandle(providerBackhaul, '-1', error.message || error);
                throw error;
            }
        }

        // 回执
        * receipt(body) {
            let contract = yield Contract.findOne({ contractNo: body.contractNo })
            if(!contract) return ;
            const reqObj = {
                returnReceiptDate: body.receiptDate,
                acceptDate: moment(body.acceptDate).add(1, 'd').toDate(),
                contractId: contract._id,
            };
            const resObj = yield global.restClient.postReceipt(reqObj);
            console.log('receipt_res:', JSON.stringify(resObj));
            return true;
        }

        /**
         * 模拟前端投保接口流程 先生成投保信息
         * todo /api/order/proposal 接口，代码暂时先放这里打磨，等对接下一家供应商时，移出去
         * @param {Object} proposalJson
         * @param {Object} user User及UserInfo
         * @return {PropertyProposal}
         */
        * proposal(proposalJson, user) {
            logger.info('req.user:', user.userCode);
            proposalJson.userCode = user.userCode;
            proposalJson.agentId = user.agentId;
            proposalJson.saleman = user.name;

            logger.info('fxlhjk_proposal proposalJson:', JSON.stringify(proposalJson));

            const customer = yield customerService.saveCustomer(proposalJson, true);
            logger.info('customer saved:', customer);

            // 不是重新投保的情况下不需要 _id
            if (proposalJson && proposalJson._id && !proposalJson.isReProposal) {
                logger.info('tmpProposal err:暂存单核保存在_id字段');
                throw new Error('暂存单核保存在_id字段');
            }

            let proposal = yield proposalService.generateProposal(proposalJson);
            if(!proposal.appliInfo.mobile) throw new Error('投保人手机号不能为空');

            // Agent User
            proposal.agentId = user.agentId;
            proposal.saleman = user.name || user.saleman;
            proposal.userCode = user.userCode;
            proposal.orderType = 'A';

            proposal.openid = (user.wechatInfo && user.wechatInfo.openID)
                ? user.wechatInfo.openID
                : null;

            // validate proposal info
            proposal = yield proposalService.validate(proposal).catch(err => {
                logger.info('validation failed:', err);
                throw new Error('订单信息验证失败:' + err);
            });
            // auto complete proposal info;
            proposal = yield proposalService.autoComplete(proposal).catch(err => {
                logger.info('autoComplete error:', err);
                throw new Error('自动配置失败:' + err);
            });

            // to proposal
            proposal = yield proposalService.toProposal(proposal)
            proposal = yield proposalService.acceptProposal(proposal);

            // 投保人三要素实名校验
            proposal.appliInfo.realFlag = yield proposalService.hasRealnameByAppliInfo(proposal);

            return proposal;
        }

        /**
         * 模拟手动出单流程
         * todo /api/order/policy 接口，代码暂时先放这里打磨，等对接下一家供应商时，移出去
         * @param policyJson
         * @return {PropertyProposal}
         */
        * policy(policyJson) {
            policyJson = yield policyService.validate(policyJson);
            // TODO should validate pay info with tiger
            const proposal = yield policyService.policy(policyJson);

            return proposal;
        }

        /**
         * 将外部数据转换成系统标准的常规投保接口所需的数据结构，以便后续走常规投保流程进行处理
         */
        * mapToProposalData(postData) {

            const proposal = {
                // 外部平台
                source: '5',
                // 场景 供应商平台投保回传
                scene: 'baosi_platform',
                // 保单归属保险公司
                providerCode: '',
                // 保险公司名称
                providerName: '',
                // 签约方
                partiesId: '',
                partiesName: '',
                // 产品ID
                product: '',
                // 产品代码
                productCode: '',
                // 产品名称
                productName: '',
                // 产品别名
                productAlias: '',
                // 产品种类
                productType: '',

                // 出单方式 1-正常 2-链接 3-清单
                issueType: '2',
                // 团险标志
                gFlag: '0',

                // 订单号
                orderNo: postData.proposalNo,
                // 归档保单号
                contractId: '',
                // 续保标志 0-新保，1-续保
                renewalInd: postData.isAutoRenewal == 'Y' ? '1' : '0',
                // 续保单号
                migratedPolicyNo: postData.renewalContractNo || '',
                // 投保单状态
                status: '12',
                // 支付日期 用订单创建日期当支付日期(TODO 暂时使用投保日期)
                payTime: moment(postData.payDate).toDate(),

                mainInfo: {

                    // 投保单号
                    proposalNo: postData.proposalNo,
                    // 保单号
                    policyNo: null,
                    // 保险起期
                    startDate: moment(postData.startDate).toDate(),
                    // 保险止期
                    endDate: '',
                    // 承保日期
                    acceptDate: moment(postData.acceptDate).toDate(),

                    // 回执日期
                    receiptDate: postData.receiptDate ? moment(postData.receiptDate).toDate() : '',
                    // 回访日期
                    visitDate: postData.visitDate ? moment(postData.visitDate).toDate() : '',

                    // 投保日期
                    inputDate: moment(postData.inputDate).toDate(),
                    // 核保状态
                    underwriteInd: '1',

                    // 总保额
                    sumAmount: 0,
                    // 总保费
                    sumPremium: +postData.sumPrem,
                    // 投保份数
                    unitCount: 1,
                    // 保费
                    unitPremium: +postData.sumPrem,
                    // 首年保费
                    fyPremium: +postData.sumPrem,
                    // 主险保费保费
                    mRiskPremium: 0.0,
                    // 投保人数
                    holderNum: 1,
                    // 自动续保标志
                    autoRenewFlag: '0',
                },
                cardInfo: {
                    // 持卡人姓名
                    ownerName: postData.cardInfo.ownerName,
                    // 银行码（目前未编码，值为名字）
                    bankCode: postData.cardInfo.bankName || '',
                    // 银行名称
                    bankName: postData.cardInfo.bankName || '',
                    // 卡号
                    cardNo: postData.cardInfo.cardNo
                },
                // 投保人信息
                appliInfo: {
                    // 客户名称
                    name: postData.appnt.name,
                    // 性别
                    gender: this.mapGender(postData.appnt.gender),
                    // 证件类型
                    idType: this.mapIdType(postData.appnt.idType),
                    // 证件号码
                    idNo: postData.appnt.idNo,
                    // 移动电话
                    mobile: postData.appnt.mobile,
                    // 生日
                    birthday: postData.appnt.birthday,
                    // 邮箱
                    email: postData.appnt.email || '',
                    // 地址
                    address: postData.appnt.address,
                    idSDate: postData.appnt.idSDate,
                    idEDate: postData.appnt.idEDate,
                    // 投保人三要素校验
                    realFlag: false
                },
                // 被保人信息
                insuredList: [],
                // 受益人信息
                beneList: [],

                addRiskList: [],
                coverages: [],

                // 费用信息
                fee: {
                    // 手续费
                    agencyfee: 0.00,
                    // 佣金
                    commission: 0.00,
                    // 创业补助金
                    cySubsidy: 0.00,
                    // 一级推荐奖
                    firstRecomm: 0.00,
                    // 二级推荐奖
                    secondRecomm: 0.00,

                    // 手续费率
                    agencyFeeRate: 0.00,
                    // 佣金比例
                    commissionRate: 0.00,
                    // 一级推荐奖率
                    firstFactor: 0.00,
                    // 二级推荐奖率
                    secondFactor: 0.00,
                    // 不含税保费
                    extaxModalPrem: 0.00,
                    // 增值税税率
                    vatRate: 0.00,
                    // 增值税金额
                    vatAmount: 0.00,
                    // 结算方式 0-不含税 1-含税
                    hasTax: '1',
                },

                // 其他信息
                extInfo: {},
                otherInfo: { },
                // 操作员代码
                handlerCode: 'system',
            };

            if(proposal.renewalInd == '1') {
                proposal.paymentYear = 2;
            }

            // 被保人
            let insuredList = postData.insuredList;
            for(let insured of insuredList) {
                proposal.insuredList.push({
                    // 被保人名称
                    name: insured.name,
                    // 被保险人与投保人关系
                    insAppRel: this.mapInsuredRelation(insured.relationToAppnt),
                    // 证件类型
                    idType: this.mapIdType(insured.idType),
                    // 证件号码
                    idNo: insured.idNo,
                    // 性别
                    gender: this.mapGender(insured.gender),
                    // 出生日期
                    birthday: insured.birthday,
                    mobile: insured.mobile,
                    email: insured.email || '',
                    address: insured.address,
                    idSDate: insured.idSDate,
                    idEDate: insured.idEDate,
                });
            }

            let productList = postData.riskList;
            for (let i = 0; i < productList.length; i++) {
                const addRisk = productList[i];
                let product = yield productService.getProduct({ icpCode: `fxlhjk_${addRisk.riskCode}` });
                if (!product) {
                    throw new Error(`保司险种代码 ${addRisk.riskCode} 对应产品不存在`);
                }
                product = JSON.parse(JSON.stringify(product));
                if (!product.parties || !product.parties.length) {
                    throw new Error(`${product.productCode} 尚未配置签约方`);
                }

                proposal.mainInfo.sumAmount += parseFloat(addRisk.amount);

                if (addRisk.isMain == '主险') { // 主险
                    // 保单归属保险公司
                    proposal.providerCode = product.providerName.split(':')[0];
                    // 保险公司名称
                    proposal.providerName = product.providerName.split(':')[1];
                    // todo 签约方 选哪个？
                    // 产品ID
                    proposal.product = product._id.toString();
                    // 产品代码
                    proposal.productCode = product.productCode;
                    // 产品名称
                    proposal.productName = product.productAbbr;
                    // 产品别名
                    proposal.productAlias = product.productAbbr;
                    // 产品种类
                    proposal.productType = product.productType;


                    // 保障年期值，如 "80y"、"_30"
                    proposal.mainInfo.termCode = this.mapTermCode(addRisk.polPeriod, addRisk.maturInd);
                    // 保障年期描述，如 "终身"、"至80岁"、"30年"等
                    proposal.mainInfo.termName = this.mapTermName(addRisk.polPeriod, addRisk.maturInd);
                    // 实际情况是 termCode直接存 中文
                    proposal.mainInfo.termCode = proposal.mainInfo.termName;
                    // 交费方式-交费/缴费间隔
                    proposal.mainInfo.chargeGap = this.mapChargeGap(postData.payFreqCode);
                    // 交费期间 值 如 "3"
                    proposal.mainInfo.chargeCode = addRisk.period || 1;
                    // 交费期间 中文形式 如 "3年交"
                    proposal.mainInfo.chargeName = `${addRisk.period}年`;

                    // 保费
                    proposal.mainInfo.unitPremium = 0.00;
                    proposal.mainInfo.mRiskPremium = parseFloat(addRisk.premium) || 0.00;
                    // 保额
                    proposal.mainInfo.mRiskAmount = parseFloat(addRisk.amount) || 0.00;

                    // todo 收款账户，目前合作的方式是保司收款
                    proposal.account = 'SLBX'; // 这个字段应该是未起作用
                    proposal.accountName = 'provider'; // product.accountName
                } else {
                    proposal.addRiskList.push({
                        // 产品代码
                        productCode : product.productCode,
                        // 产品名称
                        productName : product.productAbbr,
                        // 产品别名
                        productAlias : product.productAbbr,
                        // 产品种类
                        productType : product.productType,
                        // 交费期间
                        chargeCode: addRisk.period || 1,
                        // 交费期间
                        chargeName: addRisk.period,
                        // // 投保期限/保险期间
                        termCode: this.mapTermCode(addRisk.polPeriod, addRisk.maturInd),
                        // // 投保期限描述
                        termName: this.mapTermName(addRisk.polPeriod, addRisk.maturInd),
                        // todo 保费
                        premium: +addRisk.premium,
                        // 保额0
                        amount: parseFloat(addRisk.amount) || 0.00,
                    });
                }
            }
            let bnfInfo = postData.bnf;
            if (bnfInfo && bnfInfo.bnfs.length > 0) {
                let bnfList = bnfInfo.bnfs;
                for (let i = 0; i < bnfList.length; i++) {
                    const beneInfo = bnfList[i];
                    proposal.beneList.push({
                        // 受益人名称
                        name: beneInfo.name,
                        // 性别
                        gender: this.mapGender(beneInfo.gender),
                        // 受益人证件类型
                        idType: this.mapIdType(beneInfo.idType),
                        // 受益人证件号码
                        idNo: beneInfo.idNo,
                        // 出生日期
                        birthday: beneInfo.birthday || '',
                        idEDateFlag: '0',
                        // 受益人与被保险人关系
                        benInsRel: this.mapInsuredRelation(beneInfo.benRelationCode),
                        // 受益人类别 1-身故受益人 2-生存受益人
                        benKind: '2',
                        // 受益人类型 1-法定 2-指定 3-投保人 4-被保人
                        benType: '2',
                        // 受益比例
                        percent: beneInfo.benRatio,
                        // 受益顺序
                        beneIndex: beneInfo.benGrade,
                        // 备注
                        remark: '',
                    });
                }
            } else {
                // 受益人默认为法定
                proposal.beneList.push({
                    // 受益人类别 1-身故受益人 2-生存受益人
                    benKind: '2',
                    // 受益人类型 1-法定 2-指定 3-投保人 4-被保人
                    benInsRel: '1',
                    benType: '1',
                    // 备注
                    remark: '',
                });
            }

        return proposal;
    }

    /**
     * 发送邮件
     */
    * sendMail(proposal){
        const content = `
                <br/>您好 <br/>
                复星联合健康 回传数据中缺少业务员相关信息，数据无法保存，请联系渠道确认！<br/>
                具体保单数据如下：<br/>
                保单号:${proposal.mainInfo.policyNo || proposal.mainInfo.proposalNo}<br/>
                投保人:${proposal.appliInfo.name}<br/>
                总保费:${proposal.mainInfo.sumPremium}<br/>
            `;
        const mailObj = {key: 'policy_error', content};
        const mailRes = yield commonService.sendEmail(mailObj);
        console.log('mailRes==', mailRes);
    }

    /**
     * 男-M-1 女-F-2
     * @param {String} gender
     */
    mapGender(gender){
        return (gender == 'M') ? '1' : '2';
    }

    /**
     * 将外部证件类型转为系统中证件类型
     * @param {String} certiType
     */
    mapIdType(certiType){
        const idTypes = {
            '01': '1', // 身份证
            '02': '13', // 户口簿
            'A': '2', // 护照
            '98':'7',   // 台湾居民来往大陆通行证
            '96':'5',   // 港澳居民来往大陆通行证
            '97':'5',   // 港澳居民来往大陆通行证
            '04': '14', // 军人证
            '12':'4',   // 出生证
        };
        return (idTypes[certiType] === undefined) ? '99' : idTypes[certiType];
    }

    /**
     * @param rel
     */
    mapInsuredRelation(rel){
        const rels = {
            '00': '2', // 本人
            '01': '4', // 配偶
            '02': '5', // 子女
            '03': '3', // 父母
        };
        return (rels[rel] === undefined) ? '99' : rels[rel];
    }

    /**
     * 保障类型coveredType
     * 其他, 9
     年, 42
    月, 41
    天, 40
    周, 30
    岁, 20
    终身, 18
    无关, 0
    *
    *  保障年限coveredYears：
    *  保终身时为0；
    * 按年限保时为保障年限；
    * 保至某确定年龄时为保障期满年龄；
    * 按月保时为保障月数；
    * 按天保时为保障天
    * @param {String} coveredType
    * @param {Number} coveredYears
    * @return {String}
    */
    mapTermCode(InsureYears, InsureYearsIntv){
        if (InsureYears === '105') {
            return '终身';
        }
        const units = {
            'A': 'a',
            'Y': 'y'
        };

        return `${InsureYears}${units[InsureYearsIntv] || units['Y']}`;
    }

    /**
     * 保障期限名称如 至多少岁
     * @param {String} coveredType
     * @param {Number} coveredYears
     * @return {String}
     */
    mapTermName(InsureYears, InsureYearsIntv) {
        if (InsureYears === '105') {
            return '终身';
        }

        const units = {
            'A': '岁',
            'Y': '年'
        };

        return `${InsureYears}${units[InsureYearsIntv] || units['Y']}`;
    }

    /**
     * 缴费方式
    * @param payFrequency
    */
    mapChargeGap(payFrequency) {
        const gaps = {
            'S': '0', //  趸缴
            'Y': '1', // 年缴
            'M': '4', //  月缴
            'Q': '3', //  季缴
            'H': '2', //  半年缴
        };
        return gaps[payFrequency] || gaps['Y'];
    }

    mapChargeCode(payIntv, payingYears){
        if (payIntv == '0') {
            return '1';
        }
        return `${parseInt(payingYears)}`;
    }

    /**
     * {
     * "responseType":"1", // 成功-1 失败-0
     * }
     * @param {Boolean} result
     * @param {Boolean} errMsg
     * @param {Boolean} body
     * @return {Promise<Object>}
     */
    * wrapResult(result, errMsg, body)
    {
        let resDate = {
            "errorMsg": "回调成功",
            "errorCode": "0000",
        };
        if(!result){
            resDate = {
                "errorMsg": "回调失败 "+errMsg,
                "errorCode": "999",
            };
        }
        return resDate;
    }
}
