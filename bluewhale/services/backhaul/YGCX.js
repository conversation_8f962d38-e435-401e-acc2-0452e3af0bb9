'use strict';

const _ = require('lodash');
const moment = require('moment');
const crypto = require('crypto');
const utils = require('../../utils/utils');
const logger = require('@huibao/logger').logger();
const Contract = require('../../models/Contract');
const UserInfo = require('../../models/UserInfo');
const ProviderBackhaul = require('../../models/ProviderBackhaul');

const customerService = require('../customerService');
const proposalService = require('../proposalService');
const policyService = require('../policyService');
const commonService = require('../commonService');
const productService = require('../productService');

const PropertyProposal = require('../../models/PropertyProposal');
const sunshine = require('../../lib/paramHelper').param.sunshine;

/**
 * 阳光财险保单数据回传通知服务
 * @type {module.SunshineBackHaulService}
 */
module.exports = class SunshineBackHaulService {

    set id(providerId) {
        this.providerId = providerId;
    }

    set code(providerCode) {
        this.providerCode = providerCode;
    }

    /**
     * 模拟前端投保接口流程 先生成投保信息
     * @param {Object} proposalJson
     * @param {String} userInfo
     * @return {PropertyProposal}
     */
    * proposal(proposalJson, userInfo, flag) {

        const result = yield global.restClient.userInfo({ userCode: userInfo.userCode });
        if (!result || (result.code !== '000') || !result.data) {
            throw new Error(`未获取到${userInfo.userCode}对应的用户信息`);
        }
        let user = result.data;
        logger.info('req.user:', user.userCode);
        proposalJson.userCode = user.userCode;
        proposalJson.agentId = user.agentId;
        proposalJson.saleman = user.name;

        logger.info('proposalJson:', JSON.stringify(proposalJson));

        const customer = yield customerService.saveCustomer(proposalJson, true);
        logger.info('customer saved:', customer);

        // 不是重新投保的情况下不需要 _id
        if (proposalJson && proposalJson._id && !proposalJson.isReProposal) {
            logger.info('tmpProposal err:暂存单核保存在_id字段');
            throw new Error('暂存单核保存在_id字段');
        }

        let proposal = yield proposalService.generateProposal(proposalJson);

        // Agent User
        proposal.agentId = user.agentId;
        proposal.saleman = user.name || user.saleman;
        proposal.userCode = user.userCode;
        proposal.orderType = 'A';

        proposal.openid = (user.wechatInfo && user.wechatInfo.openID)
            ? user.wechatInfo.openID
            : null;
        proposal.scene = user.scene;

        // validate proposal info
        proposal = yield proposalService.validate(proposal).catch(err => {
            logger.info('validation failed:', err);
            throw new Error('订单信息验证失败:' + err);
        });
        // auto complete proposal info;
        proposal = yield proposalService.autoComplete(proposal).catch(err => {
            logger.info('autoComplete error:', err);
            throw new Error('自动配置失败:' + err);
        });

        // to proposal
        proposal = yield proposalService.toProposal(proposal)
        // 保单同步执行acceptProposal
        if (flag) {
            proposal = yield proposalService.acceptProposal(proposal)
        }
        return proposal;
    }

    /**
     *  需要支持幂等,建议根据订单号或者保单号
     * @param postData
     * @return {Promise<Boolean>}
     */
    * receive(postData) {
        if (!this.providerId || !this.providerCode) {
            throw new Error('请指定供应商标识');
        }

        let body = '';
        if(postData.pushHead.encryFlag == 'N') {
            body = postData.pushBody; // 兼容手工处理数据不加密的情况
        } else {
            let data = utils.decryptForOpen(postData.pushBody.data, sunshine.md5Key);
            body = JSON.parse(data);
            body.baoDanFile = "";
        }

        let key = postData.pushHead.type;
        logger.info(`sunshine_${key}`,JSON.stringify(body));
        if (key == 'P_ELE') { // 投保完成出单通知
            return yield this.issuePolicy(body);
        } else if(key == 'cancel') { // 整单退保通知
            return yield this.cancel(body);
        } else {
            // 其他类型的不处理，直接返回
            return true;
        }
    }

    // 出单通知 保单保单相关数据
    * issuePolicy(body) {
        // 如果订单数据已经同步成功，再次用相同的数据请求时，需要返回成功
        const contractExisted = yield Contract.countDocuments({
            contractNo: body.policyNo
        });
        if (contractExisted) {
            return true;
        }

        // 先将原始报文存到数据库里，然后再做后续处理 需要考虑重复通知的情况
        const condition = {
            providerId: this.providerId,
            contractNo: body.policyNo,
        };
        let providerBackhaul = yield ProviderBackhaul.findOne(condition);
        if (!providerBackhaul) {
            providerBackhaul = new ProviderBackhaul(condition);
        }
        let bizTag = body.orderNo;
        if(bizTag) {
            let [userCode, tpChannel1, tpChannel2] = bizTag.split('_');
            body.tpChannel1 = tpChannel1 || '';
            body.tpChannel2 = tpChannel2 || '';
            bizTag = userCode;
        }
        providerBackhaul.providerCode = this.providerCode;
        providerBackhaul.orderNo = body.proposalNo;
        providerBackhaul.bizTag = bizTag;
        providerBackhaul.contract = body;
        providerBackhaul.handleAt = new Date();

        try{
            yield* commonService.backhaulNextHandle(providerBackhaul, '0');

            // 先走投保流程,数据转换前置一步
            const proposalData = yield* this.mapToProposalData(body);

            let userInfo = yield UserInfo.findOne({ userCode: bizTag });

            if (!userInfo) throw new Error(`用户code不存在`);
            if (userInfo.signResult != '1') {
                // 业务员非已签约状态，也阻断流程通知运营
                yield* this.sendMail(proposalData);
                return true;
            }

            let proposal = yield* this.proposal(proposalData, userInfo, true);

            // 先将投保单状态改成符合手动投保流程的状态
            proposal.status = '12';
            yield proposal.save();

            // 再走承保流程
            const policyJson = {
                oid: proposal.oid,
                // 投保单号
                proposalNo: proposal.mainInfo.proposalNo,
                // 保单号
                policyNo: body.policyNo,
                // 支付金额
                amount: +body.premium,
                // 支付时间 暂时按对方的订单创建时间
                payTime: moment(body.policyDate).toDate(),
                // 支付通道 //付款方式(1-支付宝 2-微信支付 3-银联个人支付）
                payChannel: '5',
                // 承保时间
                acceptDate: moment(body.policyDate).toDate(),
                // 手动补单机制
                manualFlag: true,
            };

            logger.info('policyJson:', policyJson);

            proposal = yield* this.policy(policyJson);

            yield* commonService.backhaulNextHandle(providerBackhaul, '1');

            return true;
        }catch(error){
            console.log('error ',error.message || error);
            yield* commonService.backhaulNextHandle(providerBackhaul, '-1', error.message || error);
            throw error;
        }
    }

    // 保全通知
    * cancel(body) {
        // 枚举值：WT-犹退、CT-退保 、BT-通融退保、XT-协议退保、AT-生效前撤单、JB-加保、OTA-一次性追加、PB-减保、
        let endorType = body.endorseType;
        let reqObj = {
            provider: 'baotong',
            policyNo: body.policyNo,
            endorNo: `P${body.policyNo}`,
            endorType, // WT-犹退 CT-退保
            surrenderPremium: body.surrenderPremium,
            surrenderDate: moment(body.surrenderTime).toDate(),
        }
        let result = yield global.restClient.saveEndor(reqObj);
        if (result.code != '000') {
            throw new Error(`保单:${body.policyNo}退保失败,${result.msg}`);
        }
        return true;
    }


    /**
     * 模拟手动出单流程
     * todo /api/order/policy 接口，代码暂时先放这里打磨，等对接下一家供应商时，移出去
     * @param policyJson
     * @return {PropertyProposal}
     */
    * policy(policyJson) {
        policyJson = yield policyService.validate(policyJson);
        // TODO should validate pay info with tiger
        const proposal = yield policyService.policy(policyJson);

        return proposal;
    }

    /**
     * 将外部数据转换成系统标准的常规投保接口所需的数据结构，以便后续走常规投保流程进行处理
     * 投保单
      */
    * mapToProposalData(body) {
        const proposal = {
            tpChannel1: body.tpChannel1,
            tpChannel2: body.tpChannel2,
            // 外部平台
            source: '5',
            // 场景 供应商平台投保回传
            scene: 'baosi_platform',
            // 保单归属保险公司
            providerCode: '',
            // 保险公司名称
            providerName: '',
            // 签约方
            partiesId: '',
            partiesName: '',
            // 产品ID
            product: '',
            // 产品代码
            productCode: '',
            // 产品名称
            productName: '',
            // 产品别名
            productAlias: '',
            // 产品种类
            productType: '',

            // 出单方式 1-正常 2-链接 3-清单
            issueType: '2',
            // 团险标志
            gFlag: '0',

            // 订单号
            orderNo: body.proposalNo,
            // 归档保单号
            contractId: '',
            // 上年保单号
            migratedPolicyNo: '',
            // 续保标志 0-新保，1-续保
            renewalInd: '0',
            // 投保单状态
            status: '0',
            // 电子保单下载状态
            dlFlag: '0',

            mainInfo: {

                // 投保单号
                proposalNo: body.proposalNo,
                // 保险起期
                startDate: moment(body.startDate).toDate(),
                // 保险止期
                endDate: moment(body.endDate).toDate(),
                // 承保日期
                acceptDate: moment(body.policyData).toDate(),
                // 投保日期
                inputDate: moment(body.policyData).toDate(),
                // 核保状态
                underwriteInd: '1',

                // 总保额
                sumAmount: +body.amount,
                // 总保费
                sumPremium: +body.premium,
                // 投保份数
                unitCount: 1,
                // 保费
                unitPremium: 0,
                // 首年保费
                fyPremium: 0,
                // 主险保费保费
                mRiskPremium: 0.00,
                // 投保人数
                holderNum: 1,
                // 自动续保标志
                autoRenewFlag: '0',
            },

            // 投保人信息
            appliInfo: {
                // 客户名称
                name: body.holderName,
                // 性别
                gender: '',
                // 证件类型
                idType: this.mapIdType(body.holderCardType),
                // 证件号码
                idNo: body.holderCode,
                // 出生日期
                birthday: '',
                // 是否有社保
                socialSecurityFlag: '',
                // 移动电话
                mobile: body.holderPhone,
                // 邮箱
                email: body.holderMail,
                // 地址/住址
                address: body.address || '',
                // 不校验，默认为true
                realFlag: true
            },
            // 被保人信息
            insuredList: [],
            // 受益人信息
            beneList: [],

            addRiskList: [],
            coverages: [],

            // 费用信息
            fee: {
                // 手续费
                agencyfee: 0.00,
                // 佣金
                commission: 0.00,
                // 创业补助金
                cySubsidy: 0.00,
                // 一级推荐奖
                firstRecomm: 0.00,
                // 二级推荐奖
                secondRecomm: 0.00,

                // 手续费率
                agencyFeeRate: 0.00,
                // 佣金比例
                commissionRate: 0.00,
                // 一级推荐奖率
                firstFactor: 0.00,
                // 二级推荐奖率
                secondFactor: 0.00,
                // 不含税保费
                extaxModalPrem: 0.00,
                // 增值税税率
                vatRate: 0.00,
                // 增值税金额
                vatAmount: 0.00,
                // 结算方式 0-不含税 1-含税
                hasTax: '1',
            },

            // 其他信息
            extInfo: {
                branchCode: body.agentName,
                eUrl: body.bdUrl
            },
            // 操作员代码
            handlerCode: 'system',
        };

        if(proposal.extInfo.eUrl) {
            proposal.dlFlag = '1';
        }

        // 补充投保人的生日，性别
        if(proposal.appliInfo.idType == '1') {
            let info = utils.getInfo(proposal.appliInfo.idNo);
            proposal.appliInfo.gender = info.gender;
            proposal.appliInfo.birthday = info.birthday;
            proposal.appliInfo.age = info.age;
        }

        const insured = {
            // 被保人名称
            name: body.bidderName,
            // 被保险人与投保人关系
            insAppRel: this.mapInsuredRelation(body.relation),
            // 证件类型
            idType: this.mapIdType(body.bidderCardType),
            // 证件号码
            idNo: body.bidderCode,
            // 移动电话
            mobile: body.mobile || '',
            // 邮箱
            email: body.email || '',
        }
        if(insured.idType == '1') {
            let info = utils.getInfo(insured.idNo);
            insured.gender = info.gender;
            insured.birthday = info.birthday;
            insured.age = info.age;
        }
        proposal.insuredList.push(insured);

        let product = yield productService.getProduct({ icpCode: `ygcx_${body.productCode}` });
        if (!product) {
            throw new Error(`保司产品id ${body.productCode} 对应产品不存在`);
        }
        if (!product.parties || !product.parties.length) {
            throw new Error(`${product.productCode} 尚未配置签约方`);
        }

        // 保单归属保险公司
        proposal.providerCode = product.providerName.split(':')[0];
        // 保险公司名称
        proposal.providerName = product.providerName.split(':')[1];

        // 产品ID
        proposal.product = product._id.toString();
        // 产品代码
        proposal.productCode = product.productCode;
        // 产品名称
        proposal.productName = product.productName;
        // 产品别名
        proposal.productAlias = product.productAbbr;
        // 产品种类
        proposal.productType = product.productType;
        // 计划代码（保险公司）// 保司产品组合ID
        proposal.mainInfo.planCode = body.productCode;
        // 计划名称（保险公司）? // 保司渠道名称
        proposal.mainInfo.planName = body.productName;

        // 收款账户，目前合作的方式是保司收款
        proposal.account = 'SLBX';
        proposal.accountName = 'provider';
        return proposal;
    }

    /**
     * 发送邮件
     */
    * sendMail(proposal) {
        const content = `
            <br/>您好 <br/>
            阳光回传数据中缺少业务员或业务员已注销，数据无法保存，请确认！<br/>
            具体保单数据如下：<br/>
            保单号:${proposal.mainInfo.policyNo || proposal.mainInfo.proposalNo}<br/>
            投保人:${proposal.appliInfo.name}<br/>
            总保费:${proposal.mainInfo.sumPremium}<br/>
        `;
        const mailObj = { key: 'policy_error', content };
        const mailRes = yield commonService.sendEmail(mailObj);
        console.log('mailRes==', mailRes);
    }

    /**
     * 将外部证件类型转为系统中证件类型
     * @param {String} certiType
     */
    mapIdType(certiType) {
        const idTypes = {
            '01': '1', // 身份证
            '03': '2', // 护照
        };
        return (idTypes[certiType] === undefined) ? '99' : idTypes[certiType];
    }

    /**
     * @param rel
     */
    mapInsuredRelation(rel) {
        const rels = {
            '01': '2', // 本人
            '10': '4', // 配偶
            '50': '3', // 父母
            '40': '5', // 子女
        };
        return (rels[rel] === undefined) ? '99' : rels[rel];
    }

    /**
     * {
     * "flag":"00", // 成功-00 失败-01
     * }
     * @param {Boolean} result
     * @param {Boolean} errMsg
     * @param {Boolean} body
     * @return {Promise<Object>}
     */
    * wrapResult(result, errMsg, body) {
        if (!result) {
            const content = `
                Req Time: ${moment().format('YYYY-MM-DD hh:mm Z')} <br><br>
                Req Body:<br> ${JSON.stringify(body)} <br><br>
                ErrorInfo:<br> ${errMsg}
            `;
            yield commonService.sendEmail({ key: 'policy_error', content });
        }
        return result ? {code: 1, msg: 'success' } : {code: 0, msg: errMsg }
    }
};
