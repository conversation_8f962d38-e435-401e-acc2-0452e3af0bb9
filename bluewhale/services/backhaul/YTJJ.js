'use strict';

const _ = require('lodash');
const moment = require('moment');

const logger = require('@huibao/logger').logger();

const Contract = require('../../models/Contract');
const UserInfo = require('../../models/UserInfo');
const ProviderBackhaul = require('../../models/ProviderBackhaul');

const customerService = require('../customerService');
const proposalService = require('../proposalService');
const policyService = require('../policyService');
const commonService = require('../commonService');
const productService = require('../productService');
const PropertyProposal = require('../../models/PropertyProposal');

/**
 * 宇泰经纪保单数据回传通知服务（南燕）
 * @type {module.YTJJBackHaulService}
 */
module.exports = class YTJJBackHaulService {

    set id(providerId) {
        this.providerId = providerId;
    }

    set code(providerCode) {
        this.providerCode = providerCode;
    }

    /**
     * 模拟前端投保接口流程 先生成投保信息
     * todo /api/order/proposal 接口，代码暂时先放这里打磨，等对接下一家供应商时，移出去
     * @param {Object} proposalJson
     * @param {String} userInfo
     * @return {PropertyProposal}
     */
    * proposal(proposalJson, userInfo, flag) {

        const result = yield global.restClient.userInfo({ userCode: userInfo.userCode });
        if (!result || (result.code !== '000') || !result.data) {
            throw new Error(`未获取到${userInfo.userCode}对应的用户信息`);
        }
        let user = result.data;
        logger.info('req.user:', user.userCode);
        proposalJson.userCode = user.userCode;
        proposalJson.agentId = user.agentId;
        proposalJson.saleman = user.name;

        logger.info('proposalJson:', JSON.stringify(proposalJson));

        const customer = yield customerService.saveCustomer(proposalJson, true);
        logger.info('customer saved:', customer);

        // 不是重新投保的情况下不需要 _id
        if (proposalJson && proposalJson._id && !proposalJson.isReProposal) {
            logger.info('tmpProposal err:暂存单核保存在_id字段');
            throw new Error('暂存单核保存在_id字段');
        }

        let proposal = yield proposalService.generateProposal(proposalJson);

        // Agent User
        proposal.agentId = user.agentId;
        proposal.saleman = user.name || user.saleman;
        proposal.userCode = user.userCode;
        proposal.orderType = 'A';

        proposal.openid = (user.wechatInfo && user.wechatInfo.openID)
            ? user.wechatInfo.openID
            : null;
        proposal.scene = user.scene;

        // validate proposal info
        proposal = yield proposalService.validate(proposal).catch(err => {
            logger.info('validation failed:', err);
            throw new Error('订单信息验证失败:' + err);
        });
        // auto complete proposal info;
        proposal = yield proposalService.autoComplete(proposal).catch(err => {
            logger.info('autoComplete error:', err);
            throw new Error('自动配置失败:' + err);
        });

        // to proposal
        proposal = yield proposalService.toProposal(proposal)
        // 保单同步执行acceptProposal
        if (flag) {
            proposal = yield proposalService.acceptProposal(proposal)
        }
        return proposal;
    }

    /**
     *  需要支持幂等,建议根据订单号或者保单号
     * @param postData
     * @return {Promise<Boolean>}
     */
    * receive(postData) {
        if (!this.providerId || !this.providerCode) {
            throw new Error('请指定供应商标识');
        }


        // 退保数据
        if (postData.cancelPolicyList) {
            return yield* this.cancel(postData.cancelPolicyList);
        }

        let base = postData.policy;
        // policyNo为空不处理
        if (!base || !base.policyNo) return true;

        // 如果订单数据已经同步成功，再次用相同的数据请求时，需要返回成功
        const contractExisted = yield Contract.countDocuments({
            contractNo: base.policyNo
        });
        if (contractExisted) {
            return true;
        }

        // 先将原始报文存到数据库里，然后再做后续处理 需要考虑重复通知的情况
        const condition = {
            providerId: this.providerId,
            contractNo: base.policyNo,
        };
        let providerBackhaul = yield ProviderBackhaul.findOne(condition);
        if (!providerBackhaul) {
            providerBackhaul = new ProviderBackhaul(condition);
        }
        providerBackhaul.providerCode = this.providerCode;
        providerBackhaul.orderNo = base.policyNo;
        providerBackhaul.bizTag = base.operCode;
        providerBackhaul.contract = postData;
        providerBackhaul.handleAt = new Date();
        // providerBackhaul.handleStatus = '0';
        // providerBackhaul = yield providerBackhaul.save();

        try{
            yield* commonService.backhaulNextHandle(providerBackhaul, '0');

            // 先走投保流程,数据转换前置一步
            const proposalData = yield* this.mapToProposalData(postData);

            const bizTag = { userCode: base.operCode };

            const userInfo = yield UserInfo.findOne({ userCode: bizTag.userCode });
            if (!userInfo) throw new Error(`用户code：${bizTag.userCode} 不存在`);
            if (userInfo.signResult != '1' || postData.policyHolder.idNumber.includes("**")) {
                // 业务员非已签约状态，也阻断流程通知运营
                yield* this.sendMail(proposalData);
                return true;
            }

            let proposal = yield* this.proposal(proposalData, userInfo, true);

            // 先将投保单状态改成符合手动投保流程的状态
            proposal.status = '12';
            yield proposal.save();

            // 再走承保流程
            const policyJson = {
                oid: proposal.oid,
                // 投保单号
                proposalNo: proposal.mainInfo.proposalNo,
                // 保单号
                policyNo: base.policyNo,
                // 支付金额
                amount: +base.totalPremium,
                // 支付时间 暂时按对方的订单创建时间
                payTime: moment(base.payTime || base.issueDate).toDate(),
                // 承保时间
                acceptDate: moment(base.issueDate).toDate(),
                // 手动补单机制
                manualFlag: true,
            };

            logger.info('policyJson:', policyJson);

            proposal = yield* this.policy(policyJson);

            yield* commonService.backhaulNextHandle(providerBackhaul, '1');

            return true;
        }catch(error){
            console.log('error ',error.message || error);
            yield* commonService.backhaulNextHandle(providerBackhaul, '-1', error.message || error);
            throw error;
        }
    }

    /**
     * 模拟手动出单流程
     * todo /api/order/policy 接口，代码暂时先放这里打磨，等对接下一家供应商时，移出去
     * @param policyJson
     * @return {PropertyProposal}
     */
    * policy(policyJson) {
        policyJson = yield policyService.validate(policyJson);
        // TODO should validate pay info with tiger
        const proposal = yield policyService.policy(policyJson);

        return proposal;
    }

    /**
     * 将外部数据转换成系统标准的常规投保接口所需的数据结构，以便后续走常规投保流程进行处理
     * 投保单
      */
    * mapToProposalData(postData) {
        if (!postData.policy || !postData.policyHolder || !postData.insureds) {
            throw new Error('数据格式不完整');
        }

        const proposal = {
            // 外部平台
            source: '5',
            // 保单归属保险公司
            providerCode: '',
            // 保险公司名称
            providerName: '',
            // 签约方
            partiesId: '',
            partiesName: '',
            // 产品ID
            product: '',
            // 产品代码
            productCode: '',
            // 产品名称
            productName: '',
            // 产品别名
            productAlias: '',
            // 产品种类
            productType: '',

            // 出单方式 1-正常 2-链接 3-清单
            issueType: '2',
            // 团险标志
            gFlag: '0',

            // 订单号
            orderNo: postData.policy.policyNo,
            // 归档保单号
            contractId: '',
            // 续保标志 0-新保，1-续保
            renewalInd: '0',
            // 上年保单号
            migratedPolicyNo: postData.policy.oldPolicyNo || '',
            // 投保单状态
            status: '12',
            // 支付日期 用订单创建日期当支付日期
            // payTime: postData.orderBuildDate,
            // 电子保单下载状态
            dlFlag: '0',

            mainInfo: {

                // 投保单号
                proposalNo: postData.policy.policyNo,
                // 保单号
                // policyNo: postData.policyCode,
                // 保险起期
                startDate: moment(postData.policy.startDate).toDate(),
                // 保险止期
                endDate: moment(postData.policy.endDate).toDate(),
                // 承保日期
                acceptDate: moment(postData.policy.payDate).toDate(),

                // 投保日期
                inputDate: moment(postData.policy.issueDate).toDate(),
                // 核保状态
                underwriteInd: '1',

                // 总保额
                sumAmount: 0.00,
                // 总保费
                sumPremium: 0,
                // 投保份数
                unitCount: 1,
                // 保费
                unitPremium: 0,
                // 首年保费
                fyPremium: 0,
                // 主险保费保费
                mRiskPremium: 0.00,
                // 投保人数
                holderNum: 1,
                // 自动续保标志
                autoRenewFlag: '0',
            },

            // 投保人信息
            appliInfo: {
                // 客户名称
                name: postData.policyHolder.name,
                // 性别
                gender: this.mapGender(postData.policyHolder.gender),
                // 证件类型
                idType: this.mapIdType(postData.policyHolder.idType),
                // 证件号码
                idNo: postData.policyHolder.idNumber,
                // 出生日期
                birthday: postData.policyHolder.birthDate,

                // 固定电话
                tel: '',
                // 移动电话
                mobile: postData.policyHolder.telephone,
                // 邮箱
                email: postData.policyHolder.email,
                // 地址/住址
                address: postData.policyHolder.address || '',

            },
            // 被保人信息
            insuredList: [],
            // 受益人信息
            beneList: [],

            addRiskList: [],

            // 费用信息
            fee: {
                // 手续费
                agencyfee: 0.00,
                // 佣金
                commission: 0.00,
                // 创业补助金
                cySubsidy: 0.00,
                // 一级推荐奖
                firstRecomm: 0.00,
                // 二级推荐奖
                secondRecomm: 0.00,

                // 手续费率
                agencyFeeRate: 0.00,
                // 佣金比例
                commissionRate: 0.00,
                // 一级推荐奖率
                firstFactor: 0.00,
                // 二级推荐奖率
                secondFactor: 0.00,
                // 不含税保费
                extaxModalPrem: 0.00,
                // 增值税税率
                vatRate: 0.00,
                // 增值税金额
                vatAmount: 0.00,
                // 结算方式 0-不含税 1-含税
                hasTax: '1',
            },

            // 其他信息
            extInfo: {
                eUrl: postData.policy.ePolicy || '',
                branchCode: postData.policy.channelCode,
            },
            cardInfo: {
                // 银行名称
                bankName: postData.policy.bankName || '',
                // 卡号
                cardNo: postData.policy.bankAccout || '',
            },
            // 操作员代码
            handlerCode: 'system',
        };

        // 有续保保单号
        if(proposal.migratedPolicyNo) {
            proposal.paymentYear = 2;
            proposal.renewalInd = '1';
        }
        if(proposal.extInfo.eUrl) {
            proposal.dlFlag = '1';
        }

        const insureList = postData.insureds;
        for (let i = 0; i < insureList.length; i++) {
            const insured = insureList[i];
            proposal.insuredList.push({
                // 被保人名称
                name: insured.name,
                // 被保险人与投保人关系
                insAppRel: this.mapInsuredRelation(insured.relationship),
                // 性别
                gender: this.mapGender(insured.gender),
                // 证件类型
                idType: this.mapIdType(insured.idType),
                // 证件号码
                idNo: insured.idNumber,
                // 出生日期
                birthday: insured.birthDate,
                // 固定电话
                tel: '',
                // 移动电话
                mobile: insured.telephone,
                // 邮箱
                email: insured.email,
                // 国籍
                nationality: insured.nationality,
                // 地址/住址
                address: insured.address || '',
            });

            let benList = insured.beneficialList || [];
            for (let i = 0; i < benList.length; i++) {
                const beneficiary = abenList[i];
                proposal.beneList.push({
                    // 受益人名称
                    name: beneficiary.name,
                    idType: this.mapIdType(beneficiary.idType),
                    idNo: beneficiary.idNumber,
                    // 受益人类别 1-身故受益人 2-生存受益人
                    benKind: '2',
                    // 受益人类型 1-法定 2-指定 3-投保人 4-被保人
                    benType: '2',
                    // 受益比例
                    percent: beneficiary.percentage,
                    // 受益顺序
                    beneIndex: 1,
                    // 备注
                    remark: '',
                });
            }
        }


        let riskInfos = postData.policy.dutyInfo;
        if (!riskInfos || riskInfos.length == 0) {
            // 短险可能没有dutyInfo
            riskInfos.push({
                amount: postData.policy.totalAmount || 0,
                premium: postData.policy.totalPremium,
                dutyCode: postData.policy.productCode
            })
        }
        riskInfos = riskInfos.filter(p=> p.premium > 0);
        for (let i = 0; i < riskInfos.length; i++) {
            const addRisk = riskInfos[i];
            let icpCode = addRisk.dutyCode;
            let product = yield productService.getProduct({ icpCode: `ytjj_${icpCode}` });
            if (!product) {
                throw new Error(`保司险种代码 ${icpCode} 对应产品不存在`);
            }

            if (!product.parties || !product.parties.length) {
                throw new Error(`${product.productCode} 尚未配置签约方`);
            }

            proposal.mainInfo.sumAmount += addRisk.amount;
            // todo 保费
            proposal.mainInfo.sumPremium +=  addRisk.premium;

            if (product.planRiderType == '1') { // 主险
                // 保单归属保险公司
                proposal.providerCode = product.providerName.split(':')[0];
                // 保险公司名称
                proposal.providerName = product.providerName.split(':')[1];
                // todo 签约方 选哪个？
                // 产品ID
                proposal.product = product._id.toString();
                // 产品代码
                proposal.productCode = product.productCode;
                // 产品名称
                proposal.productName = product.productAbbr;
                // 产品别名
                proposal.productAlias = product.productAbbr;
                // 产品种类
                proposal.productType = product.productType;

                // 计划代码（保险公司）?
                proposal.mainInfo.planCode = postData.policy.planCode;
                // 计划名称（保险公司）?
                proposal.mainInfo.planName = postData.policy.planName;

                // 保费
                proposal.mainInfo.unitPremium = 0;
                proposal.mainInfo.mRiskPremium = parseFloat(addRisk.premium) || 0.00;
                // 保额0--没有
                proposal.mainInfo.amount = parseFloat(addRisk.amount) || 0.00;
                // 交费期间 值 如 "3"
                proposal.mainInfo.chargeCode = addRisk.payendyer || 1;
                // 保险期间
                proposal.mainInfo.termCode = addRisk.insuyear;
                proposal.mainInfo.termName = addRisk.insuyear;

                // todo 收款账户，目前合作的方式是保司收款
                proposal.account = 'SLBX'; // 这个字段应该是未起作用
                proposal.accountName = 'provider'; // product.accountName
            } else {
                proposal.addRiskList.push({
                    // 系统内的产品名称
                    productName: `${product.productCode}:${product.productAbbr}`,
                    // 系统内的产品编码
                    productCode: product.productCode,
                    // 缴费年期
                    chargeCode: addRisk.payendyer || 1,
                    // 保险期间
                    termCode: addRisk.insuyear || '',
                    // 保费
                    premium: parseFloat(addRisk.premium) || 0.00,
                    // 保额0
                    amount: parseFloat(addRisk.amount) || 0.00,
                });
            }
        }

        return proposal;
    }

    /**
     * 发送邮件
     */
    * sendMail(proposal) {
        const content = `
            <br/>您好 <br/>
            宇泰经纪回传数据中缺少业务员或业务员已注销，数据无法保存，请确认！<br/>
            具体保单数据如下：<br/>
            保单号:${proposal.mainInfo.policyNo || proposal.mainInfo.proposalNo}<br/>
            投保人:${proposal.appliInfo.name}<br/>
            总保费:${proposal.mainInfo.sumPremium}<br/>
        `;
        const mailObj = { key: 'policy_error', content };
        const mailRes = yield commonService.sendEmail(mailObj);
        console.log('mailRes==', mailRes);
    }

    /**
     * 退保
     * @param {*} data
     */
    * cancel(data){
        // TODO
        console.log('cancel data:', data);
        return true;
    }

    /**
     * 男-1 女-2
     * @param {String} gender
     */
    mapGender(gender) {
        return (gender == '男') ? '1' : '2';
    }

    /**
     * 将外部证件类型转为系统中证件类型
     * @param {String} certiType
     */
    mapIdType(certiType) {
        const idTypes = {
            '身份证': '1', // 身份证
            '港澳通行证': '25', // 港澳居民来往内地通行证
            '台湾通行证': '27', // 台湾居民来往大陆通行证
            '护照': '2', // 护照
            '出生证': '4', // 出生证明
            '其他': '99',
        };

        return (idTypes[certiType] === undefined) ? '99' : idTypes[certiType];
    }

    /**
     * @param rel
     */
    mapInsuredRelation(rel) {
        const rels = {
            '本人': '2', // 本人
            '子女': '5', // 子女
            '配偶': '4', // 配偶
            '父母': '3', // 父母
            '其他': '99'
        };

        return (rels[rel] === undefined) ? '99' : rels[rel];
    }

    /**
     * 20年、30年、106终身
     * @param {Number} coveredYears
     * @return {String}
     */
    mapTermCode(coveredYears) {
        if (!coveredYears) return '';
        if (coveredYears === '106') {
            return '终身';
        }
        const units = {
            Y: 'y',
        };

        return `${coveredYears}${units['Y']}`;
    }

    /**
     * 保障期限名称如 至多少岁
     * @param {String} coveredType
     * @param {Number} coveredYears
     * @return {String}
     */
    mapTermName(coveredYears) {
        if (!coveredYears) return '';
        if (coveredYears === '106') {
            return '终身';
        }

        const units = {
            Y: '年',
            M: '月',
            D: '天',
        };

        return `${coveredYears}${units['Y']}`;
    }

    /**
     * 缴费方式 1-年缴2-半年缴3-季缴4-月缴5-趸缴6-不定期交
     * @param payFrequency
     */
    mapChargeGap(payFrequency) {
        const gaps = {
            Y: '1',
            //2: '2',
        };

        return gaps[payFrequency] || gaps['Y'];
    }

    /**
     *
     * @param payingType 缴费年期类型，1-趸缴；2-按年限缴；3-缴至某确定年龄；4-终身
     * @param payingYears 缴费年期，趸缴时为0
     */
    mapChargeName(payingType, payingYears) {
        if (payingType === '1') {
            return '趸缴';
        }

        if (payingType === '4') {
            return '缴终身';
        }

        if (payingType === '3') {
            return `缴至${payingYears}岁`;
        }

        return `缴${payingYears}年`;
    }

    /**
     * {
     * "flag":"00", // 成功-00 失败-01
     * }
     * @param {Boolean} result
     * @param {Boolean} errMsg
     * @param {Boolean} body
     * @return {Promise<Object>}
     */
    * wrapResult(result, errMsg, body) {
        if (!result) {
            const content = `
                Req Time: ${moment().format('YYYY-MM-DD hh:mm Z')} <br><br>
                Req Body:<br> ${JSON.stringify(body)} <br><br>
                ErrorInfo:<br> ${errMsg}
            `;
            yield commonService.sendEmail({ key: 'policy_error', content });
        }

        return {
            code: result ? 'success' : 'fail', message: errMsg
        };
    }
};
