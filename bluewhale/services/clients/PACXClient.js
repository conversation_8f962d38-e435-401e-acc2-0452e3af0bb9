'use strict';

const esbClient = global.restClient.getEsbClient();

exports.proposal = (reqObj, cb) => {
    esbClient.post('/external/insure/PACX/wx?service=proposal', reqObj, function(err, req, res, obj) {
        cb(err, obj);
    });
}

exports.policyAsync = function* (reqObj) {
    reqObj.code = '000';
    return reqObj;
};

exports.cancel = (reqObj, cb) => {
    esbClient.post('/external/insure/PACX/wx?service=cancel', reqObj, function(err, req, res, obj) {
        cb(err, obj);
    });
};

exports.downloadPolicy = (reqObj, cb) => {
    esbClient.post('/external/insure/PACX/wx?service=download', reqObj, function(err, req, res, obj) {
        cb(null, obj);
    });
};

exports.downloadInvoice = (reqObj, cb) => {
    esbClient.post('/external/insure/PACX/wx?service=invoice', reqObj, function(err, req, res, obj) {
        cb(err, obj);
    });
};
