"use strict";
/**
 *  北京人寿大黄蜂13号少儿重疾险（全能版）   HBJRS2025P00002197
 *
 */
const _ = require("lodash");
const logger = require("@huibao/logger").logger();
const moment = require("moment");
const rp = require("request-promise");
const way = require("../way");
const ProductRate = require("../../../models/ProductRate");
const co = require("co");
const qs = require("qs");
exports.getInsurancePlansInputs = function (order) {
    logger.info(
        "北京人寿大黄蜂13号少儿重疾险（全能版）_order_保障项目：" +
        JSON.stringify(order)
    );
    const birthday = new Date(order["insuredInfo.birthday"]);
    const startDate = order["mainInfo.startDate"]
        ? new Date(order["mainInfo.startDate"])
        : new Date();
    const age = way.getRealAge(birthday, startDate);
    const termCode = order["mainInfo.termCode"];
    const amount = order["mainInfo.amount"] / 10000 || 0;
    let planCode =  'B';
    if (age > 17) return { errMsg: "被保人年龄为：28天-17周岁" };
    let jobLvl = "";
    if (order["insuredInfo.occType"]) jobLvl = order["insuredInfo.occType"].lvl;
    if (!["1", "2", "3", "4", "5", "6"].includes(jobLvl)) {
        return { errMsg: "被保人仅支持1-6类职业" };
    }
    // 交费年期
    let options = [
        {
            name: "趸交",
            value: "1",
        },

        {
            name: "10年",
            value: "10",
        },
        {
            name: "15年",
            value: "15",
        },
        {
            name: "20年",
            value: "20",
        },
    ];
    if (planCode === "B")
        options.push(
            {
                name: "30年",
                value: "30",
            },
            {
                name: "35年",
                value: "35",
            }
        );
    let termArr =
        [
            {
                value: "终身",
                name: "终身",
            },
        ]


    return {
        insurancePlansInputs: [
            {
                options: [
                    // {
                    //     value: "A",
                    //     name: "定期款",
                    // },
                    {
                        value: "B",
                        name: "长期版",
                    },
                ],
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                title: "选择方案",
                fieldName: "mainInfo.planCode",
                required: true,
                hasOtherInfo: false,
                inputType: "radiobutton",
                dataType: "string",
                disabled: false,
                defaultVal: "B",
            },
            {
                options,
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                title: "交费期间",
                fieldName: "mainInfo.chargeCode",
                required: true,
                hasOtherInfo: false,
                inputType: "select",
                dataType: "string",
                disabled: false,
                defaultVal: "10",
            },
            {
                options: termArr,
                callbackArgs: [],
                preOptions: [],
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                disabled: false,
                required: true,
                fieldName: "mainInfo.termCode",
                dataType: "string",
                inputType: "radiobutton",
                title: "保险期间",
                isWatch: true,
                productFactor: "1",
            },
            {
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.addition1",
                dataType: "string",
                isAddition: true,
                defaultVal: "1",
                inputType: "radiobutton",
                title: "重度疾病/特定疾病/罕见疾病多次给付保险金（可选）",

                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                productFactor: "1",
                amountText: `按合同约定`,
            },
            {
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.addition2",
                dataType: "string",
                isAddition: true,
                defaultVal: "1",
                inputType: "radiobutton",
                title: "恶性肿瘤-重度医疗津贴保险金（可选）",
                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                amountText: `${(amount * 1.7).toFixed(2)}万`,
                productFactor: "1",
            },
            {
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.addition3",
                dataType: "string",
                isAddition: true,
                defaultVal: "1",
                inputType: "radiobutton",
                title: "疾病关爱保险金（可选）",
                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                amountText: `${(amount * 1.2).toFixed(2)}万`,
                productFactor: "1",
            },
            {
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.addition4",
                dataType: "string",
                isAddition: true,
                defaultVal: "1",
                inputType: "radiobutton",
                title: "身故/高残保险金（可选）",
                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                amountText: `${amount.toFixed(2)}万`,
                productFactor: "1",
            },

            {
                options: [],
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                title: "基本保额",
                inputType: "inputgroup",
                dataType: "number",
                fieldName: "mainInfo.amount",
                radix: "元",
                required: true,
                disabled: false,
                placeholder: `最低10万，且为万元的整数倍递增`,
                defaultVal: "",
                productFactor: "0",
            },
        ],
    };
};

exports.getCoverages = function (order) {
    let planCode = order[`mainInfo.planCode`];
    logger.info(
        "北京人寿大黄蜂13号少儿重疾险（全能版）_order_保险计划：" +
        JSON.stringify(order)
    );

    const amount = order.amount || order["mainInfo.amount"];
    let plan = [
        {
            _id: "67da5926ff567a23e5efb9f5",
            name: "重度疾病保险金",
            value: `${amount * 1.0}元`,
            subText:
                "等待期180天，意外或等待期后经专科医生确诊初次发生本合同所指的125种重度疾病赔付100%基本保额，赔1次。确诊初次发生重疾后，豁免后续未交保费，合同继续有效。",
            name2: "125种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/0b94e544-d1d7-4a34-81b0-c42f661e08dd-%E9%87%8D%E7%96%BE.pdf",
        },
        {
            _id: "67da5926ff567a23e5efb9f8",
            name: "中症疾病保险金",
            value: `${amount * 0.6}元*6次（共享）`,
            subText:
                "等待期180天，意外或等待期后经专科医生确诊初次发生本合同所指的30种中度疾病赔付60%基本保额。每种中度疾病给付以一次为限，中度疾病与轻度疾病累计最多赔付6次，不分组，无间隔期。第一次中度疾病赔付后，豁免后续未交保费。被保险人确诊重度疾病90天后，若中度疾病与轻度疾病累计赔付未满6次，与已确诊重疾非同组的中度疾病还可再赔，直至与轻度疾病合计赔付满6次。如被保险人确诊重度疾病90天内确诊中度疾病，则中度疾病责任及中度疾病额外给付保险金终止。",
            name2: "30种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/cade88ba-613d-4351-83b8-3b27eaf5eb13-%E4%B8%AD%E7%97%87.pdf",
        },
        {
            _id: "67da5926ff567a23e5efb9f9",
            name: "轻症疾病保险金",
            value: `${amount * 0.3}元*6次（共享）`,
            subText:
                "等待期180天，意外或等待期后经专科医生确诊初次发生本合同所指的43种轻度疾病赔付30%基本保额。每种轻度疾病给付以一次为限，轻度疾病与中度疾病累计最高赔付6次，不分组，无间隔期。第一次轻度疾病赔付后，豁免后续未交保费。被保险人确诊重度疾病90天后，若轻度疾病与中度疾病累计赔付未满6次，与已确诊重疾非同组的轻度疾病还可再赔，直至与中度疾病合计赔付满6次。如被保险人确诊重度疾病90天内确诊轻度疾病，则轻度疾病责任及轻度疾病额外给付保险金终止。",
            name2: "43种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/bc51740e-d1ed-4b6b-a2aa-96d33b5e881b-%E8%BD%BB%E7%97%87.pdf",
        },
        {
            _id: "67da5926ff567a23e5efb9fa",
            name: "特定疾病额外给付保险金",
            value: `${amount * 1.2}元`,
            subText:
                "等待期180天，意外或等待期后经专科医生确诊初次发生本合同所指的20种特定疾病，按120%给付首次特定疾病额外给付保险金，该保险金的给付以一次为限。",
            name2: "20种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/a9291cb7-4dfa-4205-bc2a-cec00af2f6b6-%E7%89%B9%E7%96%BE.pdf",
        },
        {
            _id: "67da5926ff567a23e5efb9fe",
            name: "罕见疾病额外给付保险金",
            value: `${amount * 2.0}元`,
            subText:
                "等待期180天，意外或等待期后经专科医生确诊初次发生本合同所指的20种罕见疾病，按200%给付首次特定疾病额外给付保险金，该保险金的给付以一次为限。",
            name2: "20种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/d91ae404-ed83-456e-84c1-a7497e4e1a72-%E7%BD%95%E8%A7%81.pdf",
        },
        {
            _id: "67da5926ff567a23e5efb9ff",
            name: "恶性肿瘤-重度拓展保险金",
            value: `${amount * 1.0}元`,
            subText:
                "被保险人于本合同等待期后经专科医生确诊初次发生本合同所指的轻度疾病中的“恶性肿瘤——轻度”或“原位癌”，在保险公司按照合同给付轻症保险金后，被保险人后续确诊“恶性肿瘤——重度”，赔付100%基本保额。",
        },
        {
            _id: "67da5926ff567a23e5efba00",
            name: "特定疾病移植治疗额外给付保险金",
            value: `${amount * 0.8}元`,
            subText:
                "等待期180天。被保险人于本合同等待期后经专科医生确诊初次发生本合同所指的特定疾病，并且在18周岁前因治疗该特定疾病接受了骨髓移植治疗、干细胞移植治疗或器官移植治疗的，额外赔付80%基本保额，限赔付1次。",
        },
        {
            _id: "67da5926ff567a23e5efba02",
            name: "首次特定意外重度疾病额外给付保险金",
            value: `${amount * 0.2}元`,
            name2: "13种病种查询",
            value2: `//oss2.99bx.cn/opr/static/file/2fd21901-b533-4209-92fa-9ca55a16b368-%E7%89%B9%E5%AE%9A%E6%84%8F%E5%A4%96%E9%87%8D%E5%BA%A6%E7%96%BE%E7%97%85.pdf`,
            subText:
                "被保险人因意外伤害经专科医生确诊初次发生本合同所指的13种特定意外重度疾病，按照20%基本保额给付首次特定意外重度疾病额外给付保险金，该保险金的给付以一次为限。",
        },
        {
            _id: "67da5926ff567a23e5efba03",
            name: "特定手术保险金",
            value: `${amount * 0.1}元`,
            subText:
                "等待期180天。保30年：被保险人于18周岁前经专科医生确诊初次发生本合同所指的6种特定手术，在赔付首次重大疾病保险金的同时，按照10%基本保额给付特定手术保险金，该保险金的给付以一次为限。保终身：被保险人经专科医生确诊初次发生本合同所指的6种特定手术，在赔付首次重大疾病保险金的同时，按照10%基本保额给付特定手术保险金，该保险金的给付以一次为限。",
            name2: "6种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/adc6dca5-871c-4181-a016-bfc2666b18e1-%E7%89%B9%E5%AE%9A%E6%89%8B%E6%9C%AF.pdf",
        },
        {
            _id: "67da5926ff567a23e5efba04",
            name: "特定少儿生长发育手术保险金",
            value: `${amount * 0.2}元`,
            subText:
                "等待期180天，被保险人于18周岁前经专科医生确诊初次发生本合同所指的5种特定少儿生长发育手术，按照20%基本保额给付特定少儿生长发育手术关爱保险金，该保险金的给付以一次为限。",
            name2: "5种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/5d152e44-e563-4eee-a53c-18326cbf2e84-%E7%89%B9%E5%AE%9A%E5%B0%91%E5%84%BF%E7%94%9F%E8%82%B2%E6%89%8B%E6%9C%AF.pdf",
        },
    ];

    if (order.otherInfo.addition1 === "1") {
        plan.push({
            _id: "67da5926ff567a23e5efb9fb",
            name: "重度疾病/特定疾病/罕见疾病多次给付保险金（可选）",
            value: "按合同约定",
            subText:
                "在首次确诊重度疾病且已按约定给付重度疾病保险金之后，经专科医生确诊再次发生本合同所指的重度疾病（非同种重度疾病），第2次确诊赔付120%基本保额，第3次确诊赔付140%基本保额，第4次确诊赔付160%基本保额。每种重度疾病给付以一次为限，累计最高额外赔付3次，不分组。 情形一：仅选择重度疾病多次保险金，每次赔付期间隔期1年。 情形二：同时选择重度疾病多次保险金和“恶性肿瘤——重度”医疗津贴保险金，若前次重度疾病非“恶性肿瘤——重度”，后经专科医生确诊本合同所指的“恶性肿瘤——重度”，间隔期为180天，其他情形，间隔期1年。如第2/3/4次重大疾病确诊的重疾属于特定疾病额外赔付120%基本保额，如确诊的重疾属于罕见病额外赔付200%基本保额。",
        });
    }
    if (order.otherInfo.addition2 === "1") {
        plan.push({
            _id: "67da5926ff567a23e5efb9fc",
            name: "恶性肿瘤-重度医疗津贴保险金\n\n（可选）",
            value: `${amount * 0.4}元/${amount * 0.5}元/${amount * 0.3}元/${amount * 0.5
                }元`,
            subText:
                "确诊初次发生“恶性肿瘤——重度”，间隔1年后，再次确诊“恶性肿瘤——重度”，按40%基本保额赔付首次“恶性肿瘤——重度”医疗津贴，间隔1年后再次确诊“恶性肿瘤——重度”，按照50%基本保额赔付第2次“恶性肿瘤——重度”医疗津贴，间隔1年后再次确诊“恶性肿瘤——重度”，按照30%基本保额赔付第3次“恶性肿瘤——重度”医疗津贴； 确诊初次发生非“恶性肿瘤——重度”的重疾，间隔180天后，确诊“恶性肿瘤——重度”，按40%基本保额赔付首次“恶性肿瘤——重度”医疗津贴，间隔1年后再次确诊“恶性肿瘤——重度”，按照50%基本保额赔付第2次“恶性肿瘤——重度”医疗津贴，间隔1年后再次确诊“恶性肿瘤——重度”，按照30%基本保额赔付第3次“恶性肿瘤——重度”医疗津贴；在赔付第3次“恶性肿瘤——重度”医疗津贴后，每间隔3年，再次确诊“恶性肿瘤——重度”，按照50%基本保额赔付“恶性肿瘤——重度”医疗津贴保险金，直至保单终止。再次确诊“恶性肿瘤——重度”的情况包含新发、复发、持续存在、转移或扩散。",
        });
    }
    if (order.otherInfo.addition3 === "1") {
        plan.push({
            _id: "67da5926ff567a23e5efba01",
            name: "疾病关爱保险金（可选）",
            value: `${amount * 0.8}元/${amount * 0.3}元/${amount * 0.1}元`,
            subText:
                "等待期180天。保30年：意外或等待期后在被保险人第10个保单周年日零时前经专科医生确诊初次发生本合同所指的重度疾病额外给付80%基本保额；确诊初次发生合同所指中度疾病额外赔付30%基本保额；确诊初次发生合同所指轻度疾病额外赔付10%基本保额。保终身：意外或等待期后在被保险人60周岁的保单周年日前经专科医生确诊初次发生本合同所指的重度疾病额外给付80%基本保额；确诊初次发生合同所指中度疾病额外赔付30%基本保额；确诊初次发生合同所指轻度疾病额外赔付10%基本保额。",
        });
    }
    if (order.otherInfo.addition4 === "1") {
        plan.push({
            _id: "67da5926ff567a23e5efb9fd",
            name: "身故/高残保险金（可选）",
            value: `累计已交保费/${amount}元`,
            subText:
                "等待期180天，在18周岁之前，因意外事故或等待期后身故或全残赔付已交保费，18周岁之后赔付100%基本保额。",
        });
    }
    order.additionalRisk.forEach((item) => {
        if (item.productCode == "FJTBRHMDHF12H") {
            plan.push({
                name: "附加投保人豁免保险费E款",
                value: "豁免剩余保费",
                subText:
                    "等待期180天，投保人初次确诊125种重疾、30种中度疾病、43种轻度疾病，或身故、高度残疾，豁免后续未交保费。",
                cTemplateSubTextFlag: true,
            });
        }
    });

    return plan;
};

exports.getPremium = function (order) {
    logger.info(
        "北京人寿大黄蜂13号少儿重疾险（全能版）_order_保费：" +
        JSON.stringify(order)
    );
    const gender = order["insuredInfo.gender"];
    let plancode = order["mainInfo.planCode"];
    const birthday = new Date(order["insuredInfo.birthday"]);
    const startDate = order["mainInfo.startDate"]
        ? new Date(order["mainInfo.startDate"])
        : new Date();
    const age = way.getRealAge(birthday, startDate);
    const applicantAge = way.getRealAge(
        new Date(order["appliInfo.birthday"]),
        startDate
    );

    let MaxAmount = 30;
    if (order["insuredInfo.area"]) {
        MaxAmount = getMaxAmount(age, order["insuredInfo.area"]);
    }

    return co(function* () {
        const amount = +order["mainInfo.amount"] / 10000;
        if (plancode == "A" && amount < 30)
            return { errMsg: "定期款最低保额为30万" };
        if (plancode == "B" && amount < 10)
            return { errMsg: "长期版最低保额为10万" };
        if (MaxAmount < amount) return { errMsg: `最高保额为${MaxAmount}万元` };
        if (amount % 1 !== 0) return { errMsg: "保额需为1万的整数倍" };
        // 费率表信息
        let fixedParameter = {
            tableId: "20903",
            "genes[common][groupId]": "177422",
            "genes[groupId]": "177422",
            "genes[common][chn]": "cps_202142376",
            "genes[common][brid]": "longbuy_168271015-main_0",
            "genes[common][insuranceFullId]":
                "8561e0d01681d7e51704e7d6fc2004a5_e85x-1-28",
            "genes[common][csid]": "0",

            "genes[plan][versionId]": "36772",
            "genes[plan][payType]": "Y",
            "genes[plan][account]": "",

            "genes[insured][begTime]": moment().format("YYYYMMDD"),
        };
        let data = {
            ...fixedParameter,

            "genes[applicant][sex]": getGender(order["appliInfo.gender"]),
            "genes[applicant][birth]": moment(
                order["appliInfo.birthday"]
            ).format("YYYYMMDD"),
            "genes[applicant][hyphenAge]": `${applicantAge}-2-20-12863`,
            // "genes[insured][resident][residentAddr][]":
            //     order["insuredInfo.addr"].provinceCode,
            // "genes[insured][resident][residentAddr][]":
            //     order["insuredInfo.addr"].cityCode,
            // "genes[insured][resident][residentAddr][]":
            //     order["insuredInfo.addr"].townCode,
            "genes[insured][birth]": moment(
                order["insuredInfo.birthday"]
            ).format("YYYYMMDD"),
            "genes[insured][sex]": getGender(order["insuredInfo.gender"]),
            "genes[insured][hyphenAge]": `${age}-2-20-3732`,
            "genes[plan][coverage]": +order["mainInfo.amount"] * 100,
            "genes[plan][insuredTime]":
                order["mainInfo.termCode"] == "终身"
                    ? "C"
                    : order["mainInfo.termCode"] == "70"
                        ? `A70`
                        : "Y30",
            "genes[plan][chargeYears]": `Y${order["mainInfo.chargeCode"]}`,

            "genes[plan][ex_16701]": order["otherInfo.addition1"],
            "genes[plan][ex_16702]": order["otherInfo.addition2"],
            "genes[plan][ex_16703]": order["otherInfo.addition3"],
            "genes[plan][ex_16704]": order["otherInfo.addition4"],
            "genes[plan][ex_16705]": "0", //投保人豁免
        };
        console.log("req_data:", JSON.stringify(data));
        let reqData = qs.stringify(data);
        let res = yield rp({
            method: "POST",
            url: "https://www.baodan100.com/buy/getCostTable",
            headers: {
                priority: "u=1, i",
                "x-requested-with": "XMLHttpRequest",
                "User-Agent":
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0",
                "content-type": "application/x-www-form-urlencoded",
            },
            body: reqData,
        });
        res = JSON.parse(res);
        console.log("res", JSON.stringify(res));
        if (res.errmsg !== "success") return { errMsg: res.errmsg };

        let premium = res.data.rows[0][2];
        logger.info("主险保费为:" + premium);
        return { premium };
    }).catch((e) => {
        console.log(e);
        return { errMsg: "HBJRS2025P00002197_保费计算失败" };
    });
};
let getGender = (gender) => {
    return gender == "1" ? "M" : "F";
};
let getMaxAmount = (age, addr) => {
    let arr70 = ["北京", "上海", "浙江", "江苏", "广东", "天津"];
    let arr40 = ["河北", "湖北", "吉林", "辽宁", "黑龙江", "山东", "河南"];
    let isFirstCity70 = arr70.some((item) => addr.includes(item));
    if (isFirstCity70) return 70;
    let isFirstCity40 = arr40.some((item) => addr.includes(item));
    if (isFirstCity40) return 40;
    return 55;
};
