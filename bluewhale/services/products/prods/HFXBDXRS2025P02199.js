"use strict";
/**
 *  复保大黄蜂13号少儿重疾险旗舰版   HFXBDXRS2025P02199
 *
 */
const _ = require("lodash");
const logger = require("@huibao/logger").logger();
const moment = require("moment");
const rp = require("request-promise");
const way = require("../way");
const ProductRate = require("../../../models/ProductRate");
const co = require("co");
const qs = require("qs");
exports.getInsurancePlansInputs = function (order) {
    logger.info(
        "复保大黄蜂13号少儿重疾险旗舰版_order_保障项目：" +
        JSON.stringify(order)
    );
    const birthday = new Date(order["insuredInfo.birthday"]);
    const startDate = order["mainInfo.startDate"]
        ? new Date(order["mainInfo.startDate"])
        : new Date();
    const age = way.getRealAge(birthday, startDate);
    const termCode = order["mainInfo.termCode"];
    const amount = order["mainInfo.amount"] / 10000 || "";
    let planCode = 'B';
    if (age > 17) return { errMsg: "被保人年龄为：28天-17周岁" };
    let jobLvl = "";
    if (order["insuredInfo.occType"]) jobLvl = order["insuredInfo.occType"].lvl;
    if (!["1", "2", "3", "4"].includes(jobLvl)) {
        return { errMsg: "被保人仅支持1-4类职业" };
    }
    // 交费年期
    let options = [
        {
            name: "趸交",
            value: "1",
        },

        {
            name: "10年",
            value: "10",
        },
        {
            name: "15年",
            value: "15",
        },
        {
            name: "20年",
            value: "20",
        },
    ];
    if (planCode === "B")
        options.push(
            {
                name: "30年",
                value: "30",
            },
            {
                name: "35年",
                value: "35",
            }
        );
    let termArr =
        [
            {
                value: "70",
                name: "至70周岁",
            },
            {
                value: "终身",
                name: "终身",
            },
        ];
    let insurancePlansInputs = [
        {
            options: [
                // {
                //     value: "A",
                //     name: "定期款",
                // },
                {
                    value: "B",
                    name: "长期版",
                },
            ],
            callbackArgs: [],
            preOptions: [],
            isWatch: true,
            isCalc: false,
            otherInfo: {
                valueEqual: "0",
                inputs: [],
            },
            optionChanges: [],
            title: "选择方案",
            fieldName: "mainInfo.planCode",
            required: true,
            hasOtherInfo: false,
            inputType: "radiobutton",
            dataType: "string",
            disabled: false,
            defaultVal: "B",
        },
        {
            options,
            callbackArgs: [],
            preOptions: [],
            isWatch: true,
            isCalc: false,
            otherInfo: {
                valueEqual: "0",
                inputs: [],
            },
            optionChanges: [],
            title: "交费期间",
            fieldName: "mainInfo.chargeCode",
            required: true,
            hasOtherInfo: false,
            inputType: "select",
            dataType: "string",
            disabled: false,
            defaultVal: "1",
        },
        {
            options: termArr,
            callbackArgs: [],
            preOptions: [],
            isCalc: false,
            otherInfo: {
                valueEqual: "0",
                inputs: [],
            },
            optionChanges: [],
            disabled: false,
            required: true,
            fieldName: "mainInfo.termCode",
            dataType: "string",
            inputType: "radiobutton",
            title: "保险期间",
            isWatch: true,
            productFactor: "1",
        },
    ];
    // if (order["mainInfo.planCode"] == "B") {
    insurancePlansInputs.push({
        callbackArgs: [],
        preOptions: [],
        isWatch: true,
        isCalc: false,
        otherInfo: {
            valueEqual: "0",
            inputs: [],
        },
        optionChanges: [],
        required: true,
        fieldName: "otherInfo.addition1",
        dataType: "string",
        isAddition: true,
        defaultVal: "0",
        inputType: "radiobutton",
        title: "重大疾病多次给付保险金（可选）",
        options:
            planCode == "B"
                ? [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ]
                : [
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
        amountText: `按合同约定`,
        productFactor: "1",
        disabled: planCode == "B" ? false : true,
    });
    // }
    insurancePlansInputs.push(
        {
            callbackArgs: [],
            preOptions: [],
            isWatch: true,
            isCalc: false,
            otherInfo: {
                valueEqual: "0",
                inputs: [],
            },
            optionChanges: [],
            required: true,
            fieldName: "otherInfo.addition2",
            dataType: "string",
            isAddition: true,
            defaultVal: "0",
            inputType: "radiobutton",
            title: "疾病关爱保险金（可选）",
            options: [
                {
                    value: "1",
                    name: "投保",
                },
                {
                    value: "0",
                    name: "不投保",
                },
            ],
            amountText:
                planCode == "A"
                    ? `${(amount * 1.2).toFixed(2)}万`
                    : `${(amount * 1.4).toFixed(2)}万`,
            productFactor: "1",
        },
        {
            callbackArgs: [],
            preOptions: [],
            isWatch: true,
            isCalc: false,
            otherInfo: {
                valueEqual: "0",
                inputs: [],
            },
            optionChanges: [],
            required: true,
            fieldName: "otherInfo.addition3",
            dataType: "string",
            isAddition: true,
            defaultVal: "0",
            inputType: "radiobutton",
            title: "恶性肿瘤-重度关爱保险金（可选）",
            options: [
                {
                    value: "1",
                    name: "投保",
                },
                {
                    value: "0",
                    name: "不投保",
                },
            ],
            amountText: `按合同约定`,
            productFactor: "0",
        },
        {
            callbackArgs: [],
            preOptions: [],
            isWatch: true,
            isCalc: false,
            otherInfo: {
                valueEqual: "0",
                inputs: [],
            },
            optionChanges: [],
            required: true,
            fieldName: "otherInfo.addition4",
            dataType: "string",
            isAddition: true,
            defaultVal: "0",
            inputType: "radiobutton",
            title: "身故/高残保险金（可选）",
            options: [
                {
                    value: "1",
                    name: "投保",
                },
                {
                    value: "0",
                    name: "不投保",
                },
            ],
            amountText: `${(amount * 1).toFixed(2)}万`,
            productFactor: "1",
        },

        {
            options: [],
            callbackArgs: [],
            preOptions: [],
            isWatch: true,
            isCalc: false,
            otherInfo: {
                valueEqual: "0",
                inputs: [],
            },
            optionChanges: [],
            title: "基本保额",
            inputType: "inputgroup",
            dataType: "number",
            fieldName: "mainInfo.amount",
            radix: "元",
            required: true,
            disabled: false,
            placeholder: `最低10万，且为十万元整数倍递增`,
            defaultVal: "",
            productFactor: "0",
        }
    );
    return {
        insurancePlansInputs,
    };
};

exports.getCoverages = function (order) {
    let planCode = order[`mainInfo.planCode`];
    logger.info(
        "复保大黄蜂13号少儿重疾险旗舰版_order_保险计划：" +
        JSON.stringify(order)
    );

    const amount = order.amount || order["mainInfo.amount"];
    let plan = [
        {
            _id: "67d3c0e3eb51ef44d1c1d204",
            name: "重度疾病保险金",
            value:
                planCode == "B"
                    ? `${amount}元`
                    : `${amount}元/${amount * 1.2}元/${amount * 1.4}元/${amount * 1.6
                    }元`,
            subText:
                planCode == "B"
                    ? "等待期180天，意外或等待期后经医疗机构确诊初次发生本合同所指的125种重大疾病赔付100%基本保额。确诊初次发生重疾后，豁免后续未交保费，合同继续有效。"
                    : "等待期 180天，125种重疾。因意外伤害或于等待期后因意外伤害以外的原因初次确诊发生本合同约定的重大疾病，赔付100%基本保额；首次重大疾病赔付后，豁免后续未交保费。在首次确诊重大疾病且已按约定给付重大疾病保险金之后，经医疗机构确诊再次发生本合同所指的重大疾病（非同种重大疾病），第2次确诊赔付120%基本保额，第3次确诊赔付140%基本保额，第4次确诊赔付160%基本保额。每种重大疾病给付以一次为限，累计最高额外赔付3次，不分组。若前次确诊的重大疾病为非“恶性肿瘤--重度”，后经医疗机构确诊本合同所指的“恶性肿瘤--重度”，间隔期为180天，其他情形，间隔期365天。",
            name2: "125种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/e9afa98a-bbd9-408d-b730-f6acbfc2293d-%E9%87%8D%E7%96%BE.pdf",
        },
        {
            _id: "67d3c0e3eb51ef44d1c1d207",
            name: "中症疾病保险金",
            value: `${amount * 0.6}元*6次（共享）`,
            subText:
                "等待期180天，意外或等待期后经医疗机构确诊初次发生本合同所指的30种中症疾病赔付60%基本保额。每种中症疾病给付以一次为限，中症疾病与轻症疾病累计最多赔付6次，不分组，无间隔期。被保险人确诊重大疾病后，若中症疾病与轻症疾病累计赔付未满6次，与已确诊重疾非同组的中症疾病还可再赔，直至与轻症疾病合计赔付满6次。",
            name2: "30种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/8e418683-9f3e-40a1-ac23-c52dc8836630-%E4%B8%AD%E7%97%87.pdf",
        },
        {
            _id: "67d3c0e3eb51ef44d1c1d208",
            name: "轻症疾病保险金",
            value: `${amount * 0.3}元*6次（共享）`,
            subText:
                "等待期180天，意外或等待期后经医疗机构确诊初次发生本合同所指的43种轻症疾病赔付30%基本保额。每种轻症疾病给付以一次为限，轻症疾病与中症疾病累计最高赔付6次，不分组，无间隔期。被保险人确诊重大疾病后，若轻症疾病与中症疾病累计赔付未满6次，与已确诊重疾非同组的轻症疾病还可再赔，直至与中症疾病合计赔付满6次。",
            name2: "43种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/9e470d6e-e059-4565-b144-227532713f2e-%E8%BD%BB%E7%97%87.pdf",
        },
        {
            _id: "67d3c0e3eb51ef44d1c1d209",
            name: "特定疾病保险金",
            value: `${amount * 0.6}元/${amount * 1.3}元`,
            subText:
                "等待期180天，意外或等待期后经医疗机构确诊初次发生本合同所指的20种特定疾病，按如下规则给付特定疾病保险金：第1个保单年度额外赔付60%基本保额，第2个保单年度及以后额外赔付130%基本保额，该保险金的给付以一次为限。",
            name2: "20种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/c14e4ea4-0062-474e-afd1-44ddb72902f4-%E7%89%B9%E7%96%BE.pdf",
        },
        {
            _id: "67d3c0e3eb51ef44d1c1d20d",
            name: "罕见疾病保险金",
            value: `${amount * 1}元/${amount * 2.1}元`,
            subText:
                "等待期180天，意外或等待期后经医疗机构确诊初次发生本合同所指的20种罕见疾病，按如下规则给付罕见疾病保险金：第1个保单年度额外赔付100%基本保额；第2个保单年度及以后额外赔付210%基本保额，该保险金的给付以一次为限。",
            name2: "20种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/a5183bff-62be-4fe3-88ea-8934d93f0964-%E7%BD%95%E8%A7%81.pdf",
        },
        {
            _id: "67d3c0e3eb51ef44d1c1d20e",
            name: "恶性肿瘤-重度拓展保险金",
            value: `${amount * 1}元`,
            subText:
                "被保险人于本合同等待期后经医疗机构确诊初次发生本合同所指的轻症疾病中的“恶性肿瘤--轻度”或“原位癌”，在保险公司按照合同给付轻症疾病保险金后，被保险人确诊为“恶性肿瘤--重度”，赔付100%基本保额。",
        },
        {
            _id: "67d3c0e3eb51ef44d1c1d20f",
            name: "特定疾病移植治疗额外给付保险金",
            value: `${amount * 0.8}元`,
            subText:
                "等待期180天。被保险人于本合同等待期后经专科医生确诊初次发生本合同所指的特定疾病，并且在18周岁前因治疗该特定疾病接受了重大器官移植术或造血干细胞移植术，额外赔付80%基本保额，限赔付1次。",
        },
        {
            _id: "67d3c0e3eb51ef44d1c1d211",
            name: "先天性疾病保险金",
            value: `${amount * 0.2}元`,
            subText:
                "被保险人在年满3周岁之前确诊合同约定的先天性疾病，赔付20%基本保额，限赔付1次。",
        },
        {
            _id: "67d3c0e3eb51ef44d1c1d212",
            name: "严重肥胖手术关爱保险金",
            value: `${amount * 0.2}元`,
            subText:
                "被保险人已经年满2周岁且在年满18周岁之前确诊初次发生本合同所定义的严重肥胖特定合并症，且因治疗严重肥胖特定合并症接受了减重手术，赔付20%基本保额。",
        },
    ];
    if (order.otherInfo.addition1 === "1")
        plan.push({
            _id: "67d3c0e3eb51ef44d1c1d20a",
            name: "重大疾病多次给付保险金（可选）",
            value: "按合同约定",
            subText:
                "在首次确诊重大疾病且已按约定给付重大疾病保险金之后，经医疗机构确诊再次发生本合同所指的重大疾病（非同种重大疾病），第2次确诊赔付120%基本保额，第3次确诊赔付140%基本保额，第4次确诊赔付160%基本保额。每种重大疾病给付以一次为限，累计最高额外赔付3次，不分组。情形一：仅选择重大疾病多次保险金，每次赔付期间隔期365天。情形二：同时选择重大疾病多次保险金和“恶性肿瘤--重度”关爱保险金，若前次重大疾病非“恶性肿瘤--重度”，后经医疗机构确诊本合同所指的“恶性肿瘤--重度”，间隔期为180天，其他情形，间隔期365天。如第2/3/4次重大疾病确诊的重疾属于特定疾病额外给付130%基本保额,；如第2/3/4次重大疾病确诊的重疾属于罕见病额外给付210%基本保额。",
        });
    if (order.otherInfo.addition2 === "1")
        plan.push({
            _id: "67d3c0e3eb51ef44d1c1d210",
            name: "疾病关爱保险金（可选）",
            value:
                planCode == "B"
                    ? `${amount * 1}元/${amount * 0.3}元/${amount * 0.1}元`
                    : `${amount * 0.8}元/${amount * 0.3}元/${amount * 0.1}元`,
            subText:
                planCode == "B"
                    ? "等待期180天，意外或等待期后在被保险人年满60周岁后的首个保单周年日零时前经医疗机构确诊初次发生本合同所指的重大疾病额外给付100%基本保额；确诊初次发生合同所指中症疾病额外赔付30%基本保额；确诊初次发生合同所指轻症疾病额外赔付10%基本保额。"
                    : "等待期180天，意外或等待期后在第10个保单周年日零时前经医疗机构确诊初次发生本合同所指的重大疾病额外给付80%基本保额；确诊初次发生合同所指中症疾病额外赔付30%基本保额；确诊初次发生合同所指轻症疾病额外赔付10%基本保额。",
        });
    if (order.otherInfo.addition3 === "1")
        plan.push({
            _id: "67d3c0e3eb51ef44d1c1d20b",
            name: "恶性肿瘤-重度关爱保险金\n\n（可选）",
            value: "按合同约定",
            subText:
                "情况1：确诊初次发生“恶性肿瘤--重度”，间隔365天后，再次确诊“恶性肿瘤--重度”，按40%基本保额赔付首次恶性肿瘤--重度额外保险金，间隔365天后再次确诊“恶性肿瘤--重度”，按照50%基本保额赔付第2次恶性肿瘤--重度额外保险金，间隔365天后，再次确诊“恶性肿瘤--重度”，按照30%基本保额赔付第3次恶性肿瘤--重度额外保险金；情况2：确诊初次发生非“恶性肿瘤--重度”的重疾，间隔180天后，确诊“恶性肿瘤--重度”，按40%基本保额赔付首次恶性肿瘤--重度额外保险金，间隔365天后再次确诊“恶性肿瘤--重度”，按照50%基本保额赔付第2次恶性肿瘤--重度额外保险金，间隔365天后再次确诊“恶性肿瘤--重度”，按照30%基本保额赔付第3次恶性肿瘤--重度额外保险金。在赔付第3次恶性肿瘤--重度额外保险金后，每间隔3年，再次确诊“恶性肿瘤--重度”，按照50%基本保额赔付第四次及以后恶性肿瘤--重度额外保险金，直至保单终止。再次确诊“恶性肿瘤--重度”的情况包含新发、复发、持续存在、转移或扩散。",
        });
    if (order.otherInfo.addition4 === "1")
        plan.push({
            _id: "67d3c0e3eb51ef44d1c1d20c",
            name: "身故/高残保险金（可选）",
            value: `累计已交保费/${amount}元`,
            subText:
                "等待期180天，在18周岁之前，因意外事故或等待期后身故或全残赔付已交保费，18周岁之后赔付100%基本保额。",
        });
    plan.push({
        _id: "67d3c0e3eb51ef44d1c1d213",
        name: "中症疾病或轻症疾病豁免保险费",
        value: "豁免剩余保费",
        subText:
            "等待期180天，意外或等待期后经医疗机构确诊初次发生本合同所指的中症疾病或轻症疾病，豁免后续未交保费，合同继续有效。",
    });
    order.additionalRisk.forEach((item) => {
        if (item.productCode == "HFXBDXRS2025P02200") {
            plan.push({
                name: "附加投保人豁免B款",
                value: "豁免剩余保费",
                subText:
                    "等待期180天，投保人初次确诊125种重疾、30种中度疾病、43种轻度疾病，或身故、全残，豁免后续未交保费。",
                cTemplateSubTextFlag: true,
            });
        }
    });

    return plan;
};

exports.getPremium = function (order) {
    logger.info(
        "复保大黄蜂13号少儿重疾险旗舰版_order_保费：" + JSON.stringify(order)
    );
    const gender = order["insuredInfo.gender"];
    let plancode = order["mainInfo.planCode"];
    const birthday = new Date(order["insuredInfo.birthday"]);
    const startDate = order["mainInfo.startDate"]
        ? new Date(order["mainInfo.startDate"])
        : new Date();
    const age = way.getRealAge(birthday, startDate);
    let MaxAmount = 60;
    if (order["insuredInfo.area"]) {
        MaxAmount = getMaxAmount(age, order["insuredInfo.area"]);
    }

    return co(function* () {
        const amount = +order["mainInfo.amount"] / 10000;

        if (amount < 10) return { errMsg: "最低保额为10万" };
        if (MaxAmount < amount) return { errMsg: `最高保额为${MaxAmount}万元` };
        if (amount % 10 !== 0) return { errMsg: "保额需为10万的整数倍" };
        // 费率表信息
        let fixedParameter = {
            "genes[common][groupId]": "177136",
            "genes[groupId]": "177136",
            "genes[common][chn]": "cps_202142376",
            "genes[common][brid]": "longbuy_167295077-main_0",
            "genes[common][insuranceFullId]":
                "49f5a8903230d9a2f60903b057f105ac_vVe1-1-3",
            "genes[common][csid]": "0",

            "genes[plan][versionId]": "36634",
            "genes[plan][payType]": "Y",
            "genes[plan][payTypeAssist]": "1",
            "genes[plan][account]": "0",
            tableId: "20793",
            "genes[insured][begTime]": moment().format("YYYYMMDD"),
        };
        let data = {
            ...fixedParameter,

            "genes[applicant][sex]": getGender(order["appliInfo.gender"]),
            "genes[applicant][birth]": moment(
                order["appliInfo.birthday"]
            ).format("YYYYMMDD"),

            // "genes[insured][resident][residentAddr][]":
            //     order["insuredInfo.addr"].provinceCode,
            // "genes[insured][resident][residentAddr][]":
            //     order["insuredInfo.addr"].cityCode,
            // "genes[insured][resident][residentAddr][]":
            //     order["insuredInfo.addr"].townCode,

            "genes[insured][birth]": moment(
                order["insuredInfo.birthday"]
            ).format("YYYYMMDD"),
            "genes[insured][sex]": getGender(order["insuredInfo.gender"]),

            "genes[plan][coverage]": +order["mainInfo.amount"] * 100,
            "genes[plan][insuredTime]":
                order["mainInfo.termCode"] == "终身"
                    ? "C"
                    : order["mainInfo.termCode"] == "70"
                        ? `A70`
                        : "Y30",
            "genes[plan][chargeYears]": `Y${order["mainInfo.chargeCode"]}`,

            "genes[plan][ex_16542]": order["otherInfo.addition2"] || "0",
            "genes[plan][ex_16543]": order["otherInfo.addition1"] || "0",
            "genes[plan][ex_16544]": order["otherInfo.addition3"] || "0",
            "genes[plan][ex_16545]": order["otherInfo.addition4"] || "0",
            "genes[plan][ex_16619]": "0", //投保人豁免
        };
        console.log("req_data:", JSON.stringify(data));
        let reqData = qs.stringify(data);
        let res = yield rp({
            method: "POST",
            url: "https://www.baodan100.com/buy/getCostTable",
            headers: {
                priority: "u=1, i",
                "x-requested-with": "XMLHttpRequest",
                "User-Agent":
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0",
                "content-type": "application/x-www-form-urlencoded",
            },
            body: reqData,
        });
        res = JSON.parse(res);
        console.log("res", JSON.stringify(res));
        if (res.errmsg !== "success") return { errMsg: res.errmsg };

        let premium = res.data.rows[0][2];
        logger.info("主险保费为:" + premium);
        return { premium };
    }).catch((e) => {
        console.log(e);
        return { errMsg: "HFXBDXRS2025P02199_保费计算失败" };
    });
};
let getGender = (gender) => {
    return gender == "1" ? "M" : "F";
};
let getMaxAmount = (age, addr) => {
    let arr80 = ["北京", "上海", "浙江", "江苏", "广东", "福建", "天津"];
    let isFirstCity80 = arr80.some((item) => addr.includes(item));
    if (isFirstCity80) return 80;
    return 60;
};
