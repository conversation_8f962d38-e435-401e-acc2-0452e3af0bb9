"use strict";
/**
 *  超级玛丽13号重疾险   HJLRS2025P00002196
 *
 */
const _ = require("lodash");
const logger = require("@huibao/logger").logger();
const moment = require("moment");
const rp = require("request-promise");
const way = require("../way");
const ProductRate = require("../../../models/ProductRate");
const co = require("co");
const qs = require("qs");
exports.getInsurancePlansInputs = function (order) {
    logger.info("超级玛丽13号重疾险_order_保障项目：" + JSON.stringify(order));
    const birthday = new Date(order["insuredInfo.birthday"]);
    const startDate = order["mainInfo.startDate"]
        ? new Date(order["mainInfo.startDate"])
        : new Date();
    const age = way.getRealAge(birthday, startDate);
    const termCode = order["mainInfo.termCode"]||'终身';
    const amount = order["mainInfo.amount"] / 10000 || "";
    if (age > 50) return { errMsg: "被保人年龄为：28天-50周岁" };
    let jobLvl = "";
    if (order["insuredInfo.occType"]) jobLvl = order["insuredInfo.occType"].lvl;
    if (!["1", "2", "3", "4"].includes(jobLvl)) {
        return { errMsg: "被保人仅支持1-4类职业" };
    }
    // 交费年期

    let options = [
        // {
        //     name: "10年",
        //     value: "10",
        // },
        // {
        //     name: "15年",
        //     value: "15",
        // },
    ];

    if (termCode == "终身") {
        options = [
            // {
            //     name: "10年",
            //     value: "10",
            // },
            // {
            //     name: "15年",
            //     value: "15",
            // },
        ];
        if (age <= 45)
            options.push({
                name: "20年",
                value: "20",
            });
        if (age <= 35)
            options.push(
                {
                    name: "30年",
                    value: "30",
                },
                {
                    name: "35年",
                    value: "35",
                }
            );
    }
    if (termCode == "70") {
        options = [
            // {
            //     name: "10年",
            //     value: "10",
            // },
        ];
        // if (age <= 45) {
        //     options.push({
        //         name: "15年",
        //         value: "15",
        //     });
        // }
        if (age <= 40) {
            options.push({
                name: "20年",
                value: "20",
            });
        }
    }
    let termArr = [
        {
            name: "终身",
            value: "终身",
        },
        {
            name: "至70周岁",
            value: "70",
        },
    ];
    // if (age > 50) termArr.splice(1);
    // if (termCode === "终身") {
    //     if (age > 50) options.splice(1);
    //     if (age > 45) options.splice(2);
    //     if (age > 35) options.splice(3);
    // } else {
    //     options.splice(4);
    //     if (age > 45) options.splice(1);
    //     if (age > 40) options.splice(2);
    //     if (age > 30) options.splice(3);
    // }

    return {
        insurancePlansInputs: [
            {
                options,
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                title: "交费期间",
                fieldName: "mainInfo.chargeCode",
                required: true,
                hasOtherInfo: false,
                inputType: "select",
                dataType: "string",
                disabled: false,
                defaultVal: "20",
            },
            {
                options: termArr,
                callbackArgs: [],
                preOptions: [],
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                disabled: false,
                required: true,
                fieldName: "mainInfo.termCode",
                dataType: "string",
                inputType: "radiobutton",
                title: "保险期间",
                defaultVal: "终身",
                isWatch: true,
                productFactor: "1",
            },

            {
                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.addition1",
                dataType: "string",
                isAddition: true,
                defaultVal: "0",
                inputType: "radiobutton",
                title: "中度疾病及轻度疾病豁免保险费（可选）",
                productFactor: "1",
                // amountText: `${(amount * 1.2).toFixed(2)}万`,
                amountText: `-`,
            },
            {
                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.two",
                dataType: "string",
                isAddition: true,
                defaultVal: "0",
                inputType: "radiobutton",
                title: "第二次重大疾病保险金（可选）",
                productFactor: "1",
                amountText: `${(amount * 1.2).toFixed(2)}万`,
            },
            {
                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.tumour",
                dataType: "string",
                isAddition: true,
                defaultVal: "0",
                inputType: "radiobutton",
                title: "恶性肿瘤-重度医疗津贴保险金（可选）",
                productFactor: "1",
                disabled: order["otherInfo.given"] == "1",
                amountText: `${(amount * 1.2).toFixed(2)}万`,
            },
            {
                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.given",
                dataType: "string",
                isAddition: true,
                defaultVal: "0",
                inputType: "radiobutton",
                title: "恶性肿瘤重度多次给付保险金（可选）",
                productFactor: "1",
                disabled: order["otherInfo.tumour"] == "1",
                amountText: `按合同约定`,
            },

            {
                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.care",
                dataType: "string",
                isAddition: true,
                defaultVal: "0",
                inputType: "radiobutton",
                title: "疾病关爱保险金（可选）",
                productFactor: "1",
                amountText: `${(amount * 1.3).toFixed(2)}万`,
            },
            {
                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.tdzj",
                dataType: "string",
                isAddition: true,
                defaultVal: "0",
                inputType: "radiobutton",
                title: "特定重大疾病失能保险金（可选）",
                productFactor: "1",
                amountText: `${(amount * 0.5).toFixed(2)}万`,
            },

            {
                options: [
                    {
                        value: "1",
                        name: "投保",
                    },
                    {
                        value: "0",
                        name: "不投保",
                    },
                ],
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                required: true,
                fieldName: "otherInfo.die",
                dataType: "string",
                isAddition: true,
                defaultVal: "1",
                inputType: "radiobutton",
                title: "身故全残保险金",
                productFactor: "1",
                amountText: `${(amount * 1).toFixed(2)}万`,
            },
            {
                options: [],
                callbackArgs: [],
                preOptions: [],
                isWatch: true,
                isCalc: false,
                otherInfo: {
                    valueEqual: "0",
                    inputs: [],
                },
                optionChanges: [],
                title: "基本保额",
                inputType: "inputgroup",
                dataType: "number",
                fieldName: "mainInfo.amount",
                radix: "元",
                required: true,
                disabled: false,
                placeholder: `最低10万，且为1万的整数倍递增`,
                defaultVal: "",
                productFactor: "0",
            },
        ],
    };
};

exports.getCoverages = function (order) {
    logger.info("超级玛丽13号重疾险_order_保险计划：" + JSON.stringify(order));
    const birthday = new Date(order["insuredInfo.birthday"]);
    const startDate = order["mainInfo.startDate"]
        ? new Date(order["mainInfo.startDate"])
        : new Date();
    const age = way.getRealAge(birthday, startDate);
    const amount = order.amount || order["mainInfo.amount"];
    let plan = [
        {
            _id: "67d0e288eb51ef44d1b9f203",
            name: "重大疾病保险金",
            value: `${amount}元`,
            subText:
                "等待期180天。被保险人因意外伤害或于等待期后因意外伤害以外的原因初次确诊合同约定的110种重大疾病，赔付100%基本保额，赔1次。确诊重大疾病后，豁免后续未交保费，合同继续有效。",
            name2: "110种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/1ab48a2c-92e8-49e9-a6f4-c5cacfcf71d4-%E9%87%8D%E7%96%BE.pdf",
        },
        {
            _id: "67d0e288eb51ef44d1b9f20b",
            name: "中症疾病保险金",
            value: `${amount * 0.6}元*6次（共享）`,
            subText:
                "等待期180天。被保险人因意外伤害或于等待期后因意外伤害以外的原因初次发生并经医院专科医生明确诊断确定罹患本合同约定的35种中度疾病（无论一种或多种），按保险金额的60%给付“中度疾病保险金”。中度疾病保险金与轻度疾病保险金合并赔付6次，不豁免保费。第一次重疾发生后，重疾同组中度/轻度疾病不再赔付，非同组中度/轻度疾病保障继续有效。",
            name2: "35种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/62092967-6663-4ab7-acb4-45a5adb838c9-%E4%B8%AD%E7%97%87.pdf",
        },
        {
            _id: "67d0e288eb51ef44d1b9f206",
            name: "轻度疾病保险金",
            value: `${amount * 0.3}元*6次（共享）`,
            subText:
                "等待期180天。被保险人因意外伤害或于等待期后因意外伤害以外的原因初次发生并经医院专科医生明确诊断确定罹患本合同约定的40种轻度疾病（无论一种或多种），按保险金额的30%给付“轻度疾病保险金”。轻度疾病保险金与中度疾病保险金合并赔付6次，不豁免保费。第一次重疾发生后，重疾同组中度/轻度疾病不再赔付，非同组中度/轻度疾病保障继续有效。",
            name2: "40种病种查询",
            value2: "//oss2.99bx.cn/opr/static/file/ce7e8f65-aae4-4189-8b53-12fa04f1b4c2-%E8%BD%BB%E7%97%87.pdf",
        },
        {
            _id: "67d0e288eb51ef44d1b9f20d",
            name: "恶性肿瘤-重度拓展保险金",
            value: `${amount * 0.5}元`,
            subText:
                "等待期180天。被保险人因意外伤害或于等待期后因意外伤害以外的原因首次确诊合同约定的原位癌或恶性肿瘤-轻度，之后确诊合同约定的恶性肿瘤-重度，赔付50%基本保额。",
        },
        {
            _id: "67d0e288eb51ef44d1b9f20f",
            name: "肺结节切除手术保险金",
            value: `最高${amount * 0.3}元`,
            subText:
                "等待期180天，若被保险人因意外伤害或于等待期后因意外伤害以外的原因进行肺结节切除手术，且该次手术切除的肺结节不符合本合同约定的“恶性肿瘤——重度”或“原位癌”范畴，按保险金额的5%给付肺结节切除手术保险金”。做完肺结节切除手术之日起满365天，确诊肺部恶性肿瘤——重度，额外赔付30%基本保额。",
        },
    ];
    if (order.otherInfo.addition1 === "1") {
        plan.push({
            _id: "67d0e288eb51ef44d1b9f210",
            name: "中度疾病及轻度疾病豁免保险费（可选）",
            value: "豁免剩余保费",
            subText:
                "等待期180天。被保险人因意外伤害或于等待期后因意外伤害以外的原因初次确诊合同约定的轻度疾病、中度疾病中的一种或多种，豁免自轻度疾病、中度疾病之日起的各期保费，保险责任继续有效。",
        });
    }
    if (order.otherInfo.two === "1")
        plan.push({
            _id: "67d0e288eb51ef44d1b9f207",
            name: "第二次重大疾病保险金（可选）",
            value: `${amount * 1.2}元`,
            subText:
                "等待期180天。被保险人因意外伤害或于等待期后因意外伤害以外的原因，在年满65周岁后的首个保单周年日之前初次确诊合同约定的重大疾病且保险公司已按本合同约定给付重大疾病保险金的，自前述重大疾病确诊之日起满365天后，再次确诊合同约定的非首次重大疾病的其他重大疾病或自前述重大疾病确诊之日起满730天后，再次确诊首次重大疾病（不含首次重大疾病的持续），均赔付120%基本保额。本项责任赔付1次。",
        });
    if (order.otherInfo.tdzj === "1") {
        plan.push({
            _id: "67d0e288eb51ef44d1b9f209",
            name: "特定重大疾病失能保险金（可选）",
            value: `${amount * 0.1}元*5次`,
            subText:
                "等待期180天。累计给付5次为限。被保险人等待期后初次确诊本合同约定的重大疾病中的“恶性肿瘤——重度”之外的其他重大疾病,且保险公司已按照合同约定给付“重大疾病保险金”后，自该次重大疾病确诊之日起365天后，被保险人仍生存的，保险公司给付10%基本保额。自给付首次“特定重大疾病失能保险金”后，被保险人仍生存的，继续赔付“特定重大疾病失能保险金”，每次给付基本保险金额的10%，每次与上一次给付“特定重大疾病失能保险金”的给付日相隔不少于365天。当累计给付的“特定重大疾病失能保险金”达到五次时，本项保险责任终止。",
        });
    }
    if (order.otherInfo.care === "1")
        plan.push({
            _id: "67d0e288eb51ef44d1b9f208",
            name: "疾病关爱保险金（可选）",
            value: `${amount * 0.8}元/${amount * 0.5}元`,
            subText:
                "等待期180天。①重大疾病关爱金：被保险人因意外伤害或于等待期后因意外伤害以外的原因在年满60周岁后的首个保单周年日之前（不含当日）初次确诊合同约定的重大疾病，额外赔付80%基本保额，给付1次。②中度疾病关爱金：被保险人因意外伤害或于等待期后因意外伤害以外的原因在年满60周岁后的首个保单周年日之前（不含当日）初次确诊合同约定的中症，额外赔付50%基本保额，给付1次。",
        });

    if (order.otherInfo.tumour === "1")
        plan.push({
            _id: "67d0e288eb51ef44d1b9f20a",
            name: "恶性肿瘤-重度医疗津贴保险金（可选）",
            value: `${amount * 0.4}元/${amount * 0.5}元/${amount * 0.3}元`,
            subText:
                "等待期180天。累计给付3次为限。①被保险人于等待期后初次确诊合同约定的恶性肿瘤——重度之外的其他重大疾病并获得赔付，自该次重大疾病确诊之日起180天后，被保险人初次确诊合同约定的恶性肿瘤——重度，赔付40%基本保额，间隔365天后，仍处于恶性肿瘤——重度状态，给付50%基本保额，间隔365天后依然处于恶性肿瘤——重度状态，给付30%基本保额。②被保险人于等待期后首次确诊恶性肿瘤--重度，间隔365天后，被保险人确诊处于恶性肿瘤——重度状态，给付40%基本保额，间隔365天后，仍处于恶性肿瘤——重度状态，给付50%基本保额，间隔365天后，仍处于恶性肿瘤——重度状态，给付30%基本保额。恶性肿瘤——重度状态包括：恶性肿瘤——重度的新发、复发、持续、转移。本合同可选责任“恶性肿瘤——重度医疗津贴保险金”与可选责任“恶性肿瘤——重度多次给付保险金”两项责任不可以同时选择。",
        });

    if (order.otherInfo.given === "1")
        plan.push({
            _id: "67d0e288eb51ef44d1b9f20e",
            name: "恶性肿瘤-重度多次给付保险金（可选）",
            value: "按合同约定",
            subText:
                "等待期180天。①被保险人于等待期后初次确诊合同约定的恶性肿瘤——重度之外的其他重大疾病并获得赔付，自该次重大疾病确诊之日起180天后，被保险人初次确诊合同约定的恶性肿瘤——重度，赔付40%基本保额，间隔365天后，仍处于恶性肿瘤——重度状态，给付50%基本保额，间隔365天后依然处于恶性肿瘤——重度状态，给付30%基本保额。②被保险人于等待期后首次确诊恶性肿瘤--重度，间隔365天后，被保险人确诊处于恶性肿瘤——重度状态，给付40%基本保额，间隔365天后，仍处于恶性肿瘤——重度状态，给付50%基本保额，间隔365天后，仍处于恶性肿瘤——重度状态，给付30%基本保额。第四次及以后每次给付50%基本保险金额，每次与上一次给付的恶性肿瘤——重度多次给付保险金相隔不少于1095天。恶性肿瘤——重度状态包括：恶性肿瘤——重度的新发、复发、持续、转移。本合同可选责任“恶性肿瘤——重度医疗津贴保险金”与可选责任“恶性肿瘤——重度多次给付保险金”两项责任不可以同时选择。",
        });

    if (order.otherInfo.die === "1")
        plan.push({
            _id: "67d0e288eb51ef44d1b9f20c",
            name: "身故全残保险金（可选）",
            value: age < 18 ? `累计已交保费/${amount}元` : `${amount}元`,
            subText:
                "等待期180天。18岁前，赔付累计已交保费和现价较大者；18岁及以后，赔付100%基本保额。",
        });
    order.additionalRisk.forEach((item) => {
        if (item.productCode == "TBRHMCJML12H") {
            plan.push({
                name: "附加豁免保费B款重大疾病保险（互联网）",
                value: "豁免剩余保费",
                subText:
                    "等待期180天。投保人首次确诊合同约定的重大疾病/中症/轻症（无论一种或多种），或身故/全残，豁免自确诊之日或身故/全残之日以后的各期保险费，被保险人的保障继续有效。",
                cTemplateSubTextFlag: true,
            });
        }
    });

    return plan;
};

exports.getPremium = function (order) {
    logger.info("超级玛丽13号重疾险_order_保费：" + JSON.stringify(order));
    const gender = order["insuredInfo.gender"];
    const birthday = new Date(order["insuredInfo.birthday"]);
    const startDate = order["mainInfo.startDate"]
        ? new Date(order["mainInfo.startDate"])
        : new Date();
    const age = way.getRealAge(birthday, startDate);
    let MaxAmount = 50;
    if (age >= 41 && age <= 45) MaxAmount = 40;
    if (age >= 46 && age <= 50) MaxAmount = 20;

    return co(function* () {
        const amount = +order["mainInfo.amount"] / 10000;
        if (MaxAmount < amount)
            return { errMsg: `根据被保人当前年龄，最高保额为${MaxAmount}万元` };
        if (amount < 10 || amount > 50)
            return { errMsg: "保额应为10-50万之间！" };
        if (amount % 1 !== 0) return { errMsg: "保额需为1万的整数倍" };
        // 费率表信息
        let fixedParameter = {
            "genes[common][groupId]": "177392",
            "genes[common][chn]": "cps_202142376",
            "genes[common][brid]":
                "genes[common][brid]: longbuy_165087158-main_0",
            "genes[common][insuranceFullId]":
                "b6f58cf3dcbf2023341f943357d52a9f_Qifs-1-3",
            "genes[common][csid]": "0",

            "genes[groupId]": "177392",

            "genes[plan][versionId]": "36758",
            "genes[plan][payType]": "Y",
            "genes[plan][account]": "0",
            tableId: "20833",
        };
        let reqData = qs.stringify({
            ...fixedParameter,
            "genes[applicant][sex]": getGender(order["appliInfo.gender"]),
            "genes[applicant][birth]": moment(
                order["appliInfo.birthday"]
            ).format("YYYYMMDD"),

            "genes[plan][coverage]": +order["mainInfo.amount"] * 100,
            "genes[plan][insuredTime]":
                order["mainInfo.termCode"] == "终身" ? "C" : `A70`,
            "genes[plan][chargeYears]": `Y${order["mainInfo.chargeCode"]}`,

            "genes[plan][ex_16689]": order["otherInfo.addition1"],
            "genes[plan][ex_16690]": order["otherInfo.two"],
            "genes[plan][ex_16691]": order["otherInfo.tumour"],
            "genes[plan][ex_16692]": order["otherInfo.given"],
            "genes[plan][ex_16693]": order["otherInfo.care"],
            "genes[plan][ex_16694]": order["otherInfo.tdzj"],
            "genes[plan][ex_16695]": order["otherInfo.die"],
            "genes[plan][ex_16696]": "0", //投保人豁免

            "genes[insured][sex]": getGender(order["insuredInfo.gender"]),
            "genes[insured][birth]": moment(
                order["insuredInfo.birthday"]
            ).format("YYYYMMDD"),
            "genes[insured][begTime]": moment().format("YYYYMMDD"),
        });
        console.log(reqData, "reqData");
        let res = yield rp({
            method: "POST",
            url: "https://www.baodan100.com/buy/getCostTable",
            headers: {
                priority: "u=1, i",
                "x-requested-with": "XMLHttpRequest",
                "User-Agent":
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0",
                "content-type": "application/x-www-form-urlencoded",
            },
            body: reqData,
        });
        res = JSON.parse(res);
        console.log("res.data", JSON.stringify(res.data));
        if (res.errmsg !== "success") return { errMsg: res.errmsg };

        let premium = res.data.rows[0][2];
        logger.info("主险保费为:" + premium);
        return { premium };
    }).catch((e) => {
        console.log(e);
        return { errMsg: "HJLRS2025P00002196_保费计算失败" };
    });
};

let getGender = (gender) => {
    return gender == "1" ? "M" : "F";
};
