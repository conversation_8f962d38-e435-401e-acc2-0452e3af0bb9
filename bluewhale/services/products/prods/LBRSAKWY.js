"use strict";
/**
 *  安康无忧2023版百万医疗险（互联网专属）   LBRSAKWY
 *
 */
const _ = require("lodash");
const logger = require("@huibao/logger").logger();
const moment = require("moment");

const way = require("../way");
const ProductCashValue = require("../../../models/ProductCashValue");
const ProductRate = require("../../../models/ProductRate");
const co = require("co");
const qs = require("qs");
const rp = require("request-promise");
const { cityCategory } = require("../data");
exports.getInsurancePlansInputs = function (order) {
  logger.info("安康无忧2023版百万医疗险（互联网专属）_order_保障项目：" + JSON.stringify(order));
  const birthday = new Date(order["insuredInfo.birthday"]);
  const startDate = order["mainInfo.startDate"]
    ? new Date(order["mainInfo.startDate"])
    : new Date();
  const age = way.getRealAge(birthday, startDate);
  let planCode = order["mainInfo.planCode"];
  let insurancePlansInputs = [
    {
      otherInfo: {
        valueEqual: "0",
        inputs: [],
      },
      isWatch: false,
      isCalc: true,
      title: "出生日期",
      tipTitle: "被保人出生日期",
      placeholder: "被保人出生日期",
      inputType: "input",
      dataType: "date",
      fieldName: "insuredInfo.birthday",
      maxlength: 10,
      minlength: 10,
      dateOptions: {
        datepickerMode: "year",
        maxDate: "0d",
      },
      required: true,
      disabled: false,
      options: [],
      preOptions: [],
      optionChanges: [],
    },
    {
      title: '性别',
      inputType: 'radiobutton',
      dataType: 'string',
      fieldName: 'insuredInfo.gender',
      required: true,
      preOptions: [],
      callbackArgs: [],
      options: [
        {
          name: '男',
          value: '1',
        },
        {
          name: '女',
          value: '2',
        },
      ],
      optionChanges: [],
      otherInfo: {
        inputs: [],
        valueEqual: '0',
      },
    },
    {
      required: true,
      fieldName: 'insuredInfo.socialSecurityFlag',
      dataType: 'string',
      inputType: 'select',
      title: '是否有社保',
      preOptions: [],
      callbackArgs: [],
      options: [
        {
          value: '1',
          name: '有社保',
        },
        {
          value: '0',
          name: '无社保',
        },
      ],
      optionChanges: [],
      otherInfo: {
        inputs: [],
        valueEqual: '0',
      },
    },
    {
      options: [
        // {
        //   name: "计划一（1万以下50%赔付）",
        //   value: "A",
        // },
        {
          name: "计划二（1万以下100%赔付）",
          value: "B",
        },
        // {
        //   name: "计划三（1万以下50%赔付+重疾）",
        //   value: "C",
        // },
        {
          name: "计划四（1万以下100%赔付+重疾）",
          value: "D",
        },
      ],
      callbackArgs: [],
      preOptions: [],
      isCalc: true,
      otherInfo: {
        valueEqual: "0",
        inputs: [],
      },
      optionChanges: [],
      disabled: false,
      required: true,
      fieldName: "mainInfo.planCode",
      dataType: "string",
      inputType: "select",
      title: "保障计划",
      defaultVal: "A",
      isWatch: false,
      productFactor: "1",
    },
  ];
  return {
    insurancePlansInputs,
  };
};

exports.getCoverages = function (order) {
  logger.info("安康无忧2023版百万医疗险（互联网专属）_order_保险计划：" + JSON.stringify(order));
  let planCode = order["mainInfo.planCode"];
  let plan = [
    {
      "name": "一般医疗保险金",
      "value": "3000000元",
      "subText": "在保险期间内，被保险人因遭受意外伤害事故或在等待期后罹患疾病，经医院专科医生诊断必须接受治疗的，保险人依照下列约定给付保险金：<br>1.住院医疗保险金：住院期间个人实际支出的合理且必需的住院医疗费用<br>2.特殊门诊医疗保险金<br>特殊门诊治疗包括：（1）门诊肾透析；（2）门诊恶性肿瘤治疗，包括化学疗法、放射疗法、肿瘤免疫疗法、肿瘤内分泌疗法、肿瘤靶向疗法；（3）器官移植后的门诊抗排异治<br>疗。<br>3.门诊手术医疗保险金<br>4.住院前后门急诊医疗保险金：住院前 30 日内（含住院当日）以及出院后 30 日内（含出院当日）因与该次住院相同原因而接受门急诊治疗发生的合理且必要的门急诊医疗费用。<br>计划一：<br>自付金额低于1万部分，50%赔付；超过1万元部分，100%赔付<br>若被保人以有社保身份投保，未以社保身份就诊并结算，自付金额低于1万部分，30%赔付；超过1万元部分，60%赔付；对于超过1万元部分，若异地就诊无法使用参保地社保结算，且被保人已向参保地医保经办机构申请报销，但参保地无法给予社保报销时，80%赔付。"
    },
    {
      "name": "重大疾病医疗保险金",
      "value": "6000000元",
      "subText": "在保险期间内，被保险人因发生意外伤害事故或在等待期后因意外伤害以外的其他原因，经医院专科医生初次确诊罹患本合同所约定的重大疾病（无论一种或者多种），并在医院接受治疗的，保险人首先按照本条第（一）款的约定给付一般医疗保险金，当累计给付金额达到一般医疗保险金的保险金额后，保险人承担以下保险金的赔偿责任：<br>1.重大疾病住院医疗保险金：住院期间个人实际支出的合理且必要的重大疾病住院医疗费用。<br>2.重大疾病特殊门诊医疗保险金<br>重大疾病特殊门诊治疗包括：（1）门诊肾透析；（2）门诊恶性肿瘤治疗，包括化学疗法、放射疗法、肿瘤免疫疗法、肿瘤内分泌疗法、肿瘤靶向疗法；（3）器官移植后的门诊<br>抗排异治疗。<br>3.重大疾病门诊手术医疗保险金<br>4.重大疾病住院前后门急诊医疗保险金：住院前 30 日内（含住院当日）以及出院后 30日内（含出院当日）因与该次住院相同原因而接受门急诊治疗发生的合理且必要的重大疾病门急诊医疗费用。<br>0免赔，100%赔付<br>若被保人以有社保身份投保，未以社保身份就诊并结算，60%赔付；若异地就诊无法使用参保地社保结算，且被保人已向参保地医保经办机构申请报销，但参保地无法给予社保报销时，80%赔付。<br>（与300万一般医疗保险金，400万质子重离子保险金，300万恶性肿瘤特定药品保险金累计保额）"
    },
    { "name": "重大疾病异地转诊公共交通费用保险金", "value": "100000元", "subText": "在保险期间内，被保险人因意外伤害或在等待期后因意外伤害之外的其他原因，经医院专科医生初次确诊罹患本保险合同所定义的重大疾病（无论一种或者多种），因病情需要跨省级行政区（仅限中国大陆境内，含省、自治区、直辖市，但不包括香港、澳门、台湾地区）住院治疗，经被保险人申请，由转出医院开具转院证明，对于被保险人发生的合理且必要的因异地转诊产生的客运公共交通及救护车费用，保险人依照本保险合同的约定，在重大疾病异地转诊公共交通费用保险金额内给付保险金。<br>（限飞机及火车：飞机限经济舱及以下，火车限软卧或高铁动车一等座及以下）" },
    { "name": "重大疾病住院津贴", "value": "150元/天", "subText": "在保险期间内，被保险人因意外伤害或在等待期后非因意外伤害而被专科医生初次确诊罹患主险合同约定的重大疾病（一种或多种），并在二级或二级以上公立医院普通部进行住院治疗的，保险人按被保险人每次实际住院天数扣除本附加险合同约定的每次住院免赔天数后乘以保险单载明的重大疾病住院津贴日保险金额向被保险人给付重大疾病住院津贴保险金。<br>被保险人的每次住院免赔天数为 3 日，保险人对被保险人的每次住院给付天数以 30 日为限；被保险人不论一次或多次住院的，累计给付天数以 90 日为限。" },
    { "name": "质子重离子医疗保险金", "value": "4000000元", "subText": "在保险期间内，被保险人在等待期后经医院或保险单载明的保险人指定医疗机构确诊初次罹患（不间断再次投保的保险合同或者本保险合同另有约定的不受等待期限制）本保险合同释义 17 重大疾病中所定义的“恶性肿瘤-重度”，并在保险人指定医疗机构接受质子重离子治疗，对于被保险人需个人支付的、必需且合理的质子重离子医疗费用，保险人依照本保险合同的约定，在质子重离子医疗保险金额内给付质子重离子医疗保险金。<br>0免赔，100%赔付，床位费每日限额为 1500 元<br>（与重大疾病医疗保险金共享保额）" },
    {
      "name": "恶性肿瘤院外特种药品费用医疗保险金",
      "value": "3000000元",
      "subText": "在保险期间内，被保险人在等待期后出现症状并经医院的专科医生确诊初次罹患主险合同约定的恶性肿瘤（无论一种或多种），且需进行特种药品靶向治疗的，如医院内药房无法提供靶向治疗必需的相关特种药品，对于被保险人因治疗该恶性肿瘤实际支出的、同时满足条件的医院外特种药品费用，保险人按照本附加险合同的约定在保险金额范围内给付保险金。<br>0免赔<br>若被保险人以参加社会基本医疗保险身份或公费医疗身份投保：<br>a.如药品属于社保目录内药品且已经过社会基本医疗保险报销，赔付比例为 100%；<br>b.如药品属于社保目录内药品但未经过社会基本医疗保险报销，赔付比例为 60%；<br>c.如药品属于社保目录外药品，给付比例为 100%。<br>若被保险人以未参加社会基本医疗保险身份或公费医疗身份投保，则社保目录内药品和社保目录外药品给付比例均为 100%<br>海南博鳌乐城国际医疗旅游先行区临床急需进口药品费用保险金给付金额=（发生的临床急需进口药品费用-从其他途径已获得的临床急需进口药品费用补偿）×80%。<br>（含境内上市的靶向药物及海南博鳌乐城国际医疗旅游先行区临床急需进口药品。保额与重大疾病医疗保险金共享保额）"
    },
    { "name": "重大疾病绿色通道服务（增值服务）", "value": "免费", "subText": "服务内容：住院安排、手术安排。等待期后初次确诊罹患重疾客户，为客户协调全国难协调医院科室的手术与住院安排，主要为三甲医院。<br>服务次数：各限 1 次" },
    { "name": "重大疾病住院垫付服务（增值服务）", "value": "免费", "subText": "服务内容：等待期后初次确诊罹患重疾客户住院费用垫付，提供一站式涵院前、院中、院后的全闭环服务，限定本人使用。<br>服务次数：不限次数（单次不超过 5 万，保单年度内、保额下全年不超过 30万）" },
    { "name": "线上问诊+药品折扣（增值服务）", "value": "免费", "subText": "服务内容：全科图文咨询+开方业务+购药折扣，一年不限次<br>服务次数：不限次数" },
    { "name": "护理服务（增值服务）", "value": "免费", "subText": "1、一对一院内护工照护，服务内容：等待期后，客户因意外或疾病，由专业医护团队了解治疗相关信息，制定在院期间的护理计划，指派专属护理服务人员负责客户在院期间的医疗陪护及生活照料。最长不超过 5天 4 夜（须保证连续性，不可拆分使用）。<br>服务次数：限1次<br>2、一对一居家护工照护，服务内容：等待期后，客户因意外或疾病入院治疗后，出院前 1 天，由专业医护团队了解住院相关信息， 制定出院后的的延续护理计划。并按约定时间服务团队前往家中，负责客户出院后的延续护理与生活照料。最长不超过 5 天 4 夜（须保证连续性，不可拆分使用），客户服务申请时间最迟不得超过出院后的 7 个工作日。<br>服务次数：限1次" },
    { "name": "陪诊（增值服务）", "value": "免费", "subText": "服务内容：常规日夜间陪诊，等待期后，服务有效期内初次确诊罹患重疾客户，专业陪诊人员在医院指定地点接送，协助客户进行挂号、排队、付款、心理疏导。一年 2 次，限定本人使用，每次时长不超过4 小时。<br>服务次数：限2次" },
    { "name": "质子重离子绿通（增值服务）", "value": "免费", "subText": "服务内容：质子重离子直通车服务，服务有效期内初次确诊罹患实体肿瘤客户，提供针对性的医<br>学指导意见，方式包括但不局限于上海质子重离子医院协助门诊预约，远程会诊，病理会诊，就诊陪同，协助住院手术的直通车服务。（会诊相关费用由客户自行承担）<br>服务次数：限1次" },
    { "name": "多学科会诊（增值服务）", "value": "免费", "subText": "服务内容：肿瘤 MTB 多学科会诊服务，等待期后，服务有效期内初次确诊罹患重疾客户，提供肿瘤MTB 多学科会诊服务，会诊专家来自全国顶级三甲医院，全面覆盖实体瘤领域，以肿瘤内外科为核心建立影像科、病理科、放射科、化疗科、微创介入科等多科室联动的专家团队，让肿瘤患者在首诊医院，即可得到最先进的多学科诊疗方案。一年 1 次，限定本人使用。<br>服务次数：限1次" },
    {
      "name": "特药服务（增值服务）",
      "value": "免费",
      "subText": "1、国内特药 151种，服务内容：等待期后，初次确诊罹患恶性肿瘤-重度，可提供 151 种国内特药直付服务。针对服务保险责任、通过处方审核的客户，可携带有效药品处方、购药凭证、客户有效身份证件以及社保卡，在指定的药房，购<br>买药品处方中的所列药品或选择送药上门，保险责任范围内的药品费用客户无需自己垫付费用。<br>服务次数：不限次数<br>2、海外特药 15种，服务内容：等待期后，确诊恶性肿瘤-重度，在国内特药不适用的情况下，可为被保险人安排海南博鳌乐城就医服务，提供海外药品，获取全球最好医药资源。<br>服务次数：不限次数<br>3、病程管理，服务内容：医生团队从第一次接触患者起为患者提供病程管理系统，包含治疗方案记录及跟进，用药及不良反应的问询回访、安抚家属。<br>服务次数：不限次数<br>4、慈善赠药，服务内容：根据客户的需求，对客户是否符合慈善赠药的资格进行初步判断，当判断出客户符合慈善机构的援助项目赠药的申请条件时，服务商将协助客户进行慈善赠药申请及材料准备。<br>服务次数：不限次数<br>5、临床试验申请（患者招募）服务，服务内容：为患者提供最新的临床信息和重大疾病医疗咨询服务，旨在帮助重大慢性疾病的患者加入药物研究组，提供免费用药的机会，提高患者的生活质量。<br>服务次数：不限次数"
    },
    { "name": "癌症基因检测补贴（增值服务）", "value": "免费", "subText": "服务内容：<br>为患者提供靶向药物基因检测的优惠补贴服务。患者提前1-3 个工作日提交服务申请，由专属客服提供全程协助服务。根据实际情况，最高可享受 6000 元优惠补贴。<br>服务次数：限1次" }
  ]
  if (planCode == 'B') {
    plan = [
      {
        "name": "一般医疗保险金",
        "value": "3000000元",
        "subText": "在保险期间内，被保险人因遭受意外伤害事故或在等待期后罹患疾病，经医院专科医生诊断必须接受治疗的，保险人依照下列约定给付保险金：<br>1.住院医疗保险金：住院期间个人实际支出的合理且必需的住院医疗费用<br>2.特殊门诊医疗保险金<br>特殊门诊治疗包括：（1）门诊肾透析；（2）门诊恶性肿瘤治疗，包括化学疗法、放射疗法、肿瘤免疫疗法、肿瘤内分泌疗法、肿瘤靶向疗法；（3）器官移植后的门诊抗排异治<br>疗。<br>3.门诊手术医疗保险金<br>4.住院前后门急诊医疗保险金：住院前 30 日内（含住院当日）以及出院后 30 日内（含出院当日）因与该次住院相同原因而接受门急诊治疗发生的合理且必要的门急诊医疗费用。<br>计划二：<br>100%赔付<br>若被保人以有社保身份投保，未以社保身份就诊并结算，60%赔付；若异地就诊无法使用参保地社保结算，且被保人已向参保地医保经办机构申请报销，但参保地无法给予社保报销时，80%赔付。"
      },
      {
        "name": "重大疾病医疗保险金",
        "value": "6000000元",
        "subText": "在保险期间内，被保险人因发生意外伤害事故或在等待期后因意外伤害以外的其他原因，经医院专科医生初次确诊罹患本合同所约定的重大疾病（无论一种或者多种），并在医院接受治疗的，保险人首先按照本条第（一）款的约定给付一般医疗保险金，当累计给付金额达到一般医疗保险金的保险金额后，保险人承担以下保险金的赔偿责任：<br>1.重大疾病住院医疗保险金：住院期间个人实际支出的合理且必要的重大疾病住院医疗费用。<br>2.重大疾病特殊门诊医疗保险金<br>重大疾病特殊门诊治疗包括：（1）门诊肾透析；（2）门诊恶性肿瘤治疗，包括化学疗法、放射疗法、肿瘤免疫疗法、肿瘤内分泌疗法、肿瘤靶向疗法；（3）器官移植后的门诊<br>抗排异治疗。<br>3.重大疾病门诊手术医疗保险金<br>4.重大疾病住院前后门急诊医疗保险金：住院前 30 日内（含住院当日）以及出院后 30日内（含出院当日）因与该次住院相同原因而接受门急诊治疗发生的合理且必要的重大疾病门急诊医疗费用。<br>0免赔，100%赔付<br>若被保人以有社保身份投保，未以社保身份就诊并结算，60%赔付；若异地就诊无法使用参保地社保结算，且被保人已向参保地医保经办机构申请报销，但参保地无法给予社保报销时，80%赔付。<br>（与300万一般医疗保险金，400万质子重离子保险金，300万恶性肿瘤特定药品保险金累计保额）"
      },
      { "name": "重大疾病异地转诊公共交通费用保险金", "value": "100000元", "subText": "在保险期间内，被保险人因意外伤害或在等待期后因意外伤害之外的其他原因，经医院专科医生初次确诊罹患本保险合同所定义的重大疾病（无论一种或者多种），因病情需要跨省级行政区（仅限中国大陆境内，含省、自治区、直辖市，但不包括香港、澳门、台湾地区）住院治疗，经被保险人申请，由转出医院开具转院证明，对于被保险人发生的合理且必要的因异地转诊产生的客运公共交通及救护车费用，保险人依照本保险合同的约定，在重大疾病异地转诊公共交通费用保险金额内给付保险金。<br>（限飞机及火车：飞机限经济舱及以下，火车限软卧或高铁动车一等座及以下）" },
      { "name": "重大疾病住院津贴", "value": "150元/天", "subText": "在保险期间内，被保险人因意外伤害或在等待期后非因意外伤害而被专科医生初次确诊罹患主险合同约定的重大疾病（一种或多种），并在二级或二级以上公立医院普通部进行住院治疗的，保险人按被保险人每次实际住院天数扣除本附加险合同约定的每次住院免赔天数后乘以保险单载明的重大疾病住院津贴日保险金额向被保险人给付重大疾病住院津贴保险金。<br>被保险人的每次住院免赔天数为 3 日，保险人对被保险人的每次住院给付天数以 30 日为限；被保险人不论一次或多次住院的，累计给付天数以 90 日为限。" },
      { "name": "质子重离子医疗保险金", "value": "4000000元", "subText": "在保险期间内，被保险人在等待期后经医院或保险单载明的保险人指定医疗机构确诊初次罹患（不间断再次投保的保险合同或者本保险合同另有约定的不受等待期限制）本保险合同释义 17 重大疾病中所定义的“恶性肿瘤-重度”，并在保险人指定医疗机构接受质子重离子治疗，对于被保险人需个人支付的、必需且合理的质子重离子医疗费用，保险人依照本保险合同的约定，在质子重离子医疗保险金额内给付质子重离子医疗保险金。<br>0免赔，100%赔付，床位费每日限额为 1500 元<br>（与重大疾病医疗保险金共享保额）" },
      {
        "name": "恶性肿瘤院外特种药品费用医疗保险金",
        "value": "3000000元",
        "subText": "在保险期间内，被保险人在等待期后出现症状并经医院的专科医生确诊初次罹患主险合同约定的恶性肿瘤（无论一种或多种），且需进行特种药品靶向治疗的，如医院内药房无法提供靶向治疗必需的相关特种药品，对于被保险人因治疗该恶性肿瘤实际支出的、同时满足条件的医院外特种药品费用，保险人按照本附加险合同的约定在保险金额范围内给付保险金。<br>0免赔<br>若被保险人以参加社会基本医疗保险身份或公费医疗身份投保：<br>a.如药品属于社保目录内药品且已经过社会基本医疗保险报销，赔付比例为 100%；<br>b.如药品属于社保目录内药品但未经过社会基本医疗保险报销，赔付比例为 60%；<br>c.如药品属于社保目录外药品，给付比例为 100%。<br>若被保险人以未参加社会基本医疗保险身份或公费医疗身份投保，则社保目录内药品和社保目录外药品给付比例均为 100%<br>海南博鳌乐城国际医疗旅游先行区临床急需进口药品费用保险金给付金额=（发生的临床急需进口药品费用-从其他途径已获得的临床急需进口药品费用补偿）×80%。<br>（含境内上市的靶向药物及海南博鳌乐城国际医疗旅游先行区临床急需进口药品。保额与重大疾病医疗保险金共享保额）"
      },
      { "name": "重大疾病绿色通道服务（增值服务）", "value": "免费", "subText": "服务内容：住院安排、手术安排。等待期后初次确诊罹患重疾客户，为客户协调全国难协调医院科室的手术与住院安排，主要为三甲医院。<br>服务次数：各限 1 次" },
      { "name": "重大疾病住院垫付服务（增值服务）", "value": "免费", "subText": "服务内容：等待期后初次确诊罹患重疾客户住院费用垫付，提供一站式涵院前、院中、院后的全闭环服务，限定本人使用。<br>服务次数：不限次数（单次不超过 5 万，保单年度内、保额下全年不超过 30万）" },
      { "name": "线上问诊+药品折扣（增值服务）", "value": "免费", "subText": "服务内容：全科图文咨询+开方业务+购药折扣，一年不限次<br>服务次数：不限次数" },
      { "name": "护理服务（增值服务）", "value": "免费", "subText": "1、一对一院内护工照护，服务内容：等待期后，客户因意外或疾病，由专业医护团队了解治疗相关信息，制定在院期间的护理计划，指派专属护理服务人员负责客户在院期间的医疗陪护及生活照料。最长不超过 5天 4 夜（须保证连续性，不可拆分使用）。<br>服务次数：限1次<br>2、一对一居家护工照护，服务内容：等待期后，客户因意外或疾病入院治疗后，出院前 1 天，由专业医护团队了解住院相关信息， 制定出院后的的延续护理计划。并按约定时间服务团队前往家中，负责客户出院后的延续护理与生活照料。最长不超过 5 天 4 夜（须保证连续性，不可拆分使用），客户服务申请时间最迟不得超过出院后的 7 个工作日。<br>服务次数：限1次" },
      { "name": "陪诊（增值服务）", "value": "免费", "subText": "服务内容：常规日夜间陪诊，等待期后，服务有效期内初次确诊罹患重疾客户，专业陪诊人员在医院指定地点接送，协助客户进行挂号、排队、付款、心理疏导。一年 2 次，限定本人使用，每次时长不超过4 小时。<br>服务次数：限2次" },
      { "name": "质子重离子绿通（增值服务）", "value": "免费", "subText": "服务内容：质子重离子直通车服务，服务有效期内初次确诊罹患实体肿瘤客户，提供针对性的医<br>学指导意见，方式包括但不局限于上海质子重离子医院协助门诊预约，远程会诊，病理会诊，就诊陪同，协助住院手术的直通车服务。（会诊相关费用由客户自行承担）<br>服务次数：限1次" },
      { "name": "多学科会诊（增值服务）", "value": "免费", "subText": "服务内容：肿瘤 MTB 多学科会诊服务，等待期后，服务有效期内初次确诊罹患重疾客户，提供肿瘤MTB 多学科会诊服务，会诊专家来自全国顶级三甲医院，全面覆盖实体瘤领域，以肿瘤内外科为核心建立影像科、病理科、放射科、化疗科、微创介入科等多科室联动的专家团队，让肿瘤患者在首诊医院，即可得到最先进的多学科诊疗方案。一年 1 次，限定本人使用。<br>服务次数：限1次" },
      {
        "name": "特药服务（增值服务）",
        "value": "免费",
        "subText": "1、国内特药 151种，服务内容：等待期后，初次确诊罹患恶性肿瘤-重度，可提供 151 种国内特药直付服务。针对服务保险责任、通过处方审核的客户，可携带有效药品处方、购药凭证、客户有效身份证件以及社保卡，在指定的药房，购<br>买药品处方中的所列药品或选择送药上门，保险责任范围内的药品费用客户无需自己垫付费用。<br>服务次数：不限次数<br>2、海外特药 15种，服务内容：等待期后，确诊恶性肿瘤-重度，在国内特药不适用的情况下，可为被保险人安排海南博鳌乐城就医服务，提供海外药品，获取全球最好医药资源。<br>服务次数：不限次数<br>3、病程管理，服务内容：医生团队从第一次接触患者起为患者提供病程管理系统，包含治疗方案记录及跟进，用药及不良反应的问询回访、安抚家属。<br>服务次数：不限次数<br>4、慈善赠药，服务内容：根据客户的需求，对客户是否符合慈善赠药的资格进行初步判断，当判断出客户符合慈善机构的援助项目赠药的申请条件时，服务商将协助客户进行慈善赠药申请及材料准备。<br>服务次数：不限次数<br>5、临床试验申请（患者招募）服务，服务内容：为患者提供最新的临床信息和重大疾病医疗咨询服务，旨在帮助重大慢性疾病的患者加入药物研究组，提供免费用药的机会，提高患者的生活质量。<br>服务次数：不限次数"
      },
      { "name": "癌症基因检测补贴（增值服务）", "value": "免费", "subText": "服务内容：<br>为患者提供靶向药物基因检测的优惠补贴服务。患者提前1-3 个工作日提交服务申请，由专属客服提供全程协助服务。根据实际情况，最高可享受 6000 元优惠补贴。<br>服务次数：限1次" }
    ]
  }
  if (planCode == "C") {
    plan = [
      {
        "name": "一般医疗保险金",
        "value": "3000000元",
        "subText": "在保险期间内，被保险人因遭受意外伤害事故或在等待期后罹患疾病，经医院专科医生诊断必须接受治疗的，保险人依照下列约定给付保险金：<br>1.住院医疗保险金：住院期间个人实际支出的合理且必需的住院医疗费用<br>2.特殊门诊医疗保险金<br>特殊门诊治疗包括：（1）门诊肾透析；（2）门诊恶性肿瘤治疗，包括化学疗法、放射疗法、肿瘤免疫疗法、肿瘤内分泌疗法、肿瘤靶向疗法；（3）器官移植后的门诊抗排异治<br>疗。<br>3.门诊手术医疗保险金<br>4.住院前后门急诊医疗保险金：住院前 30 日内（含住院当日）以及出院后 30 日内（含出院当日）因与该次住院相同原因而接受门急诊治疗发生的合理且必要的门急诊医疗费用。<br>计划三：<br>自付金额低于1万部分，50%赔付；超过1万元部分，100%赔付<br>若被保人以有社保身份投保，未以社保身份就诊并结算，自付金额低于1万部分，30%赔付；超过1万元部分，60%赔付；对于超过1万元部分，若异地就诊无法使用参保地社保结算，且被保人已向参保地医保经办机构申请报销，但参保地无法给予社保报销时，80%赔付。"
      },
      {
        "name": "重大疾病医疗保险金",
        "value": "6000000元",
        "subText": "在保险期间内，被保险人因发生意外伤害事故或在等待期后因意外伤害以外的其他原因，经医院专科医生初次确诊罹患本合同所约定的重大疾病（无论一种或者多种），并在医院接受治疗的，保险人首先按照本条第（一）款的约定给付一般医疗保险金，当累计给付金额达到一般医疗保险金的保险金额后，保险人承担以下保险金的赔偿责任：<br>1.重大疾病住院医疗保险金：住院期间个人实际支出的合理且必要的重大疾病住院医疗费用。<br>2.重大疾病特殊门诊医疗保险金<br>重大疾病特殊门诊治疗包括：（1）门诊肾透析；（2）门诊恶性肿瘤治疗，包括化学疗法、放射疗法、肿瘤免疫疗法、肿瘤内分泌疗法、肿瘤靶向疗法；（3）器官移植后的门诊<br>抗排异治疗。<br>3.重大疾病门诊手术医疗保险金<br>4.重大疾病住院前后门急诊医疗保险金：住院前 30 日内（含住院当日）以及出院后 30日内（含出院当日）因与该次住院相同原因而接受门急诊治疗发生的合理且必要的重大疾病门急诊医疗费用。<br>0免赔，100%赔付<br>若被保人以有社保身份投保，未以社保身份就诊并结算，60%赔付；若异地就诊无法使用参保地社保结算，且被保人已向参保地医保经办机构申请报销，但参保地无法给予社保报销时，80%赔付。<br>（与300万一般医疗保险金，400万质子重离子保险金，300万恶性肿瘤特定药品保险金累计保额）"
      },
      { "name": "重大疾病异地转诊公共交通费用保险金", "value": "100000元", "subText": "在保险期间内，被保险人因意外伤害或在等待期后因意外伤害之外的其他原因，经医院专科医生初次确诊罹患本保险合同所定义的重大疾病（无论一种或者多种），因病情需要跨省级行政区（仅限中国大陆境内，含省、自治区、直辖市，但不包括香港、澳门、台湾地区）住院治疗，经被保险人申请，由转出医院开具转院证明，对于被保险人发生的合理且必要的因异地转诊产生的客运公共交通及救护车费用，保险人依照本保险合同的约定，在重大疾病异地转诊公共交通费用保险金额内给付保险金。<br>（限飞机及火车：飞机限经济舱及以下，火车限软卧或高铁动车一等座及以下）" },
      { "name": "重大疾病住院津贴", "value": "150元/天", "subText": "在保险期间内，被保险人因意外伤害或在等待期后非因意外伤害而被专科医生初次确诊罹患主险合同约定的重大疾病（一种或多种），并在二级或二级以上公立医院普通部进行住院治疗的，保险人按被保险人每次实际住院天数扣除本附加险合同约定的每次住院免赔天数后乘以保险单载明的重大疾病住院津贴日保险金额向被保险人给付重大疾病住院津贴保险金。<br>被保险人的每次住院免赔天数为 3 日，保险人对被保险人的每次住院给付天数以 30 日为限；被保险人不论一次或多次住院的，累计给付天数以 90 日为限。" },
      { "name": "质子重离子医疗保险金", "value": "4000000元", "subText": "在保险期间内，被保险人在等待期后经医院或保险单载明的保险人指定医疗机构确诊初次罹患（不间断再次投保的保险合同或者本保险合同另有约定的不受等待期限制）本保险合同释义 17 重大疾病中所定义的“恶性肿瘤-重度”，并在保险人指定医疗机构接受质子重离子治疗，对于被保险人需个人支付的、必需且合理的质子重离子医疗费用，保险人依照本保险合同的约定，在质子重离子医疗保险金额内给付质子重离子医疗保险金。<br>0免赔，100%赔付，床位费每日限额为 1500 元<br>（与重大疾病医疗保险金共享保额）" },
      {
        "name": "恶性肿瘤院外特种药品费用医疗保险金",
        "value": "3000000元",
        "subText": "在保险期间内，被保险人在等待期后出现症状并经医院的专科医生确诊初次罹患主险合同约定的恶性肿瘤（无论一种或多种），且需进行特种药品靶向治疗的，如医院内药房无法提供靶向治疗必需的相关特种药品，对于被保险人因治疗该恶性肿瘤实际支出的、同时满足条件的医院外特种药品费用，保险人按照本附加险合同的约定在保险金额范围内给付保险金。<br>0免赔<br>若被保险人以参加社会基本医疗保险身份或公费医疗身份投保：<br>a.如药品属于社保目录内药品且已经过社会基本医疗保险报销，赔付比例为 100%；<br>b.如药品属于社保目录内药品但未经过社会基本医疗保险报销，赔付比例为 60%；<br>c.如药品属于社保目录外药品，给付比例为 100%。<br>若被保险人以未参加社会基本医疗保险身份或公费医疗身份投保，则社保目录内药品和社保目录外药品给付比例均为 100%<br>海南博鳌乐城国际医疗旅游先行区临床急需进口药品费用保险金给付金额=（发生的临床急需进口药品费用-从其他途径已获得的临床急需进口药品费用补偿）×80%。<br>（含境内上市的靶向药物及海南博鳌乐城国际医疗旅游先行区临床急需进口药品。保额与重大疾病医疗保险金共享保额）"
      },
      { "name": "重大疾病绿色通道服务（增值服务）", "value": "免费", "subText": "服务内容：住院安排、手术安排。等待期后初次确诊罹患重疾客户，为客户协调全国难协调医院科室的手术与住院安排，主要为三甲医院。<br>服务次数：各限 1 次" },
      { "name": "重大疾病住院垫付服务（增值服务）", "value": "免费", "subText": "服务内容：等待期后初次确诊罹患重疾客户住院费用垫付，提供一站式涵院前、院中、院后的全闭环服务，限定本人使用。<br>服务次数：不限次数（单次不超过 5 万，保单年度内、保额下全年不超过 30万）" },
      { "name": "线上问诊+药品折扣（增值服务）", "value": "免费", "subText": "服务内容：全科图文咨询+开方业务+购药折扣，一年不限次<br>服务次数：不限次数" },
      { "name": "护理服务（增值服务）", "value": "免费", "subText": "1、一对一院内护工照护，服务内容：等待期后，客户因意外或疾病，由专业医护团队了解治疗相关信息，制定在院期间的护理计划，指派专属护理服务人员负责客户在院期间的医疗陪护及生活照料。最长不超过 5天 4 夜（须保证连续性，不可拆分使用）。<br>服务次数：限1次<br>2、一对一居家护工照护，服务内容：等待期后，客户因意外或疾病入院治疗后，出院前 1 天，由专业医护团队了解住院相关信息， 制定出院后的的延续护理计划。并按约定时间服务团队前往家中，负责客户出院后的延续护理与生活照料。最长不超过 5 天 4 夜（须保证连续性，不可拆分使用），客户服务申请时间最迟不得超过出院后的 7 个工作日。<br>服务次数：限1次" },
      { "name": "陪诊（增值服务）", "value": "免费", "subText": "服务内容：常规日夜间陪诊，等待期后，服务有效期内初次确诊罹患重疾客户，专业陪诊人员在医院指定地点接送，协助客户进行挂号、排队、付款、心理疏导。一年 2 次，限定本人使用，每次时长不超过4 小时。<br>服务次数：限2次" },
      { "name": "质子重离子绿通（增值服务）", "value": "免费", "subText": "服务内容：质子重离子直通车服务，服务有效期内初次确诊罹患实体肿瘤客户，提供针对性的医<br>学指导意见，方式包括但不局限于上海质子重离子医院协助门诊预约，远程会诊，病理会诊，就诊陪同，协助住院手术的直通车服务。（会诊相关费用由客户自行承担）<br>服务次数：限1次" },
      { "name": "多学科会诊（增值服务）", "value": "免费", "subText": "服务内容：肿瘤 MTB 多学科会诊服务，等待期后，服务有效期内初次确诊罹患重疾客户，提供肿瘤MTB 多学科会诊服务，会诊专家来自全国顶级三甲医院，全面覆盖实体瘤领域，以肿瘤内外科为核心建立影像科、病理科、放射科、化疗科、微创介入科等多科室联动的专家团队，让肿瘤患者在首诊医院，即可得到最先进的多学科诊疗方案。一年 1 次，限定本人使用。<br>服务次数：限1次" },
      {
        "name": "特药服务（增值服务）",
        "value": "免费",
        "subText": "1、国内特药 151种，服务内容：等待期后，初次确诊罹患恶性肿瘤-重度，可提供 151 种国内特药直付服务。针对服务保险责任、通过处方审核的客户，可携带有效药品处方、购药凭证、客户有效身份证件以及社保卡，在指定的药房，购<br>买药品处方中的所列药品或选择送药上门，保险责任范围内的药品费用客户无需自己垫付费用。<br>服务次数：不限次数<br>2、海外特药 15种，服务内容：等待期后，确诊恶性肿瘤-重度，在国内特药不适用的情况下，可为被保险人安排海南博鳌乐城就医服务，提供海外药品，获取全球最好医药资源。<br>服务次数：不限次数<br>3、病程管理，服务内容：医生团队从第一次接触患者起为患者提供病程管理系统，包含治疗方案记录及跟进，用药及不良反应的问询回访、安抚家属。<br>服务次数：不限次数<br>4、慈善赠药，服务内容：根据客户的需求，对客户是否符合慈善赠药的资格进行初步判断，当判断出客户符合慈善机构的援助项目赠药的申请条件时，服务商将协助客户进行慈善赠药申请及材料准备。<br>服务次数：不限次数<br>5、临床试验申请（患者招募）服务，服务内容：为患者提供最新的临床信息和重大疾病医疗咨询服务，旨在帮助重大慢性疾病的患者加入药物研究组，提供免费用药的机会，提高患者的生活质量。<br>服务次数：不限次数"
      },
      { "name": "癌症基因检测补贴（增值服务）", "value": "免费", "subText": "服务内容：<br>为患者提供靶向药物基因检测的优惠补贴服务。患者提前1-3 个工作日提交服务申请，由专属客服提供全程协助服务。根据实际情况，最高可享受 6000 元优惠补贴。<br>服务次数：限1次" },
      { "name": "重大疾病保险金", "value": "50000元", "subText": "在保险期间内，被保险人因意外伤害或在等待期后专科医生初次确诊罹患主险合同约定的重大疾病（一种或多种），保险人按保险单载明的重大疾病保险金额向被保险人给付重大疾病保险金，给付后本附加险项下重大疾病保险责任终止。<br>给付1次为限" },
      { "name": "中症疾病保险金", "value": "20000元", "subText": "在保险期间内，被保险人因意外伤害或在等待期后非因意外伤害而被专科医生初次确诊罹患本附加险合同约定的中症疾病（一种或多种），保险人按保险单载明的中症疾病保险金额向被保险人给付中症疾病保险金，给付后本附加险项下中症疾病保险责任终止。<br>给付1次为限" },
      { "name": "轻症疾病保险金", "value": "10000元", "subText": "在保险期间内，被保险人因意外伤害或在等待期后非因意外伤害而被专科医生初次确诊罹患本附加险合同约定的轻症疾病（一种或多种），保险人按保险单载明的轻症疾病保险金额向被保险人给付轻症疾病保险金，给付后轻症疾病保险责任终止。<br>给付1次为限" }
    ]
  }
  if (planCode == "D") {
    plan = [
      {
        "name": "一般医疗保险金",
        "value": "3000000元",
        "subText": "在保险期间内，被保险人因遭受意外伤害事故或在等待期后罹患疾病，经医院专科医生诊断必须接受治疗的，保险人依照下列约定给付保险金：<br>1.住院医疗保险金：住院期间个人实际支出的合理且必需的住院医疗费用<br>2.特殊门诊医疗保险金<br>特殊门诊治疗包括：（1）门诊肾透析；（2）门诊恶性肿瘤治疗，包括化学疗法、放射疗法、肿瘤免疫疗法、肿瘤内分泌疗法、肿瘤靶向疗法；（3）器官移植后的门诊抗排异治<br>疗。<br>3.门诊手术医疗保险金<br>4.住院前后门急诊医疗保险金：住院前 30 日内（含住院当日）以及出院后 30 日内（含出院当日）因与该次住院相同原因而接受门急诊治疗发生的合理且必要的门急诊医疗费用。<br>计划三：<br>100%赔付<br>若被保人以有社保身份投保，未以社保身份就诊并结算，60%赔付；若异地就诊无法使用参保地社保结算，且被保人已向参保地医保经办机构申请报销，但参保地无法给予社保报销时，80%赔付。"
      },
      {
        "name": "重大疾病医疗保险金",
        "value": "6000000元",
        "subText": "在保险期间内，被保险人因发生意外伤害事故或在等待期后因意外伤害以外的其他原因，经医院专科医生初次确诊罹患本合同所约定的重大疾病（无论一种或者多种），并在医院接受治疗的，保险人首先按照本条第（一）款的约定给付一般医疗保险金，当累计给付金额达到一般医疗保险金的保险金额后，保险人承担以下保险金的赔偿责任：<br>1.重大疾病住院医疗保险金：住院期间个人实际支出的合理且必要的重大疾病住院医疗费用。<br>2.重大疾病特殊门诊医疗保险金<br>重大疾病特殊门诊治疗包括：（1）门诊肾透析；（2）门诊恶性肿瘤治疗，包括化学疗法、放射疗法、肿瘤免疫疗法、肿瘤内分泌疗法、肿瘤靶向疗法；（3）器官移植后的门诊<br>抗排异治疗。<br>3.重大疾病门诊手术医疗保险金<br>4.重大疾病住院前后门急诊医疗保险金：住院前 30 日内（含住院当日）以及出院后 30日内（含出院当日）因与该次住院相同原因而接受门急诊治疗发生的合理且必要的重大疾病门急诊医疗费用。<br>0免赔，100%赔付<br>若被保人以有社保身份投保，未以社保身份就诊并结算，60%赔付；若异地就诊无法使用参保地社保结算，且被保人已向参保地医保经办机构申请报销，但参保地无法给予社保报销时，80%赔付。<br>（与300万一般医疗保险金，400万质子重离子保险金，300万恶性肿瘤特定药品保险金累计保额）"
      },
      { "name": "重大疾病异地转诊公共交通费用保险金", "value": "100000元", "subText": "在保险期间内，被保险人因意外伤害或在等待期后因意外伤害之外的其他原因，经医院专科医生初次确诊罹患本保险合同所定义的重大疾病（无论一种或者多种），因病情需要跨省级行政区（仅限中国大陆境内，含省、自治区、直辖市，但不包括香港、澳门、台湾地区）住院治疗，经被保险人申请，由转出医院开具转院证明，对于被保险人发生的合理且必要的因异地转诊产生的客运公共交通及救护车费用，保险人依照本保险合同的约定，在重大疾病异地转诊公共交通费用保险金额内给付保险金。<br>（限飞机及火车：飞机限经济舱及以下，火车限软卧或高铁动车一等座及以下）" },
      { "name": "重大疾病住院津贴", "value": "150元/天", "subText": "在保险期间内，被保险人因意外伤害或在等待期后非因意外伤害而被专科医生初次确诊罹患主险合同约定的重大疾病（一种或多种），并在二级或二级以上公立医院普通部进行住院治疗的，保险人按被保险人每次实际住院天数扣除本附加险合同约定的每次住院免赔天数后乘以保险单载明的重大疾病住院津贴日保险金额向被保险人给付重大疾病住院津贴保险金。<br>被保险人的每次住院免赔天数为 3 日，保险人对被保险人的每次住院给付天数以 30 日为限；被保险人不论一次或多次住院的，累计给付天数以 90 日为限。" },
      { "name": "质子重离子医疗保险金", "value": "4000000元", "subText": "在保险期间内，被保险人在等待期后经医院或保险单载明的保险人指定医疗机构确诊初次罹患（不间断再次投保的保险合同或者本保险合同另有约定的不受等待期限制）本保险合同释义 17 重大疾病中所定义的“恶性肿瘤-重度”，并在保险人指定医疗机构接受质子重离子治疗，对于被保险人需个人支付的、必需且合理的质子重离子医疗费用，保险人依照本保险合同的约定，在质子重离子医疗保险金额内给付质子重离子医疗保险金。<br>0免赔，100%赔付，床位费每日限额为 1500 元<br>（与重大疾病医疗保险金共享保额）" },
      {
        "name": "恶性肿瘤院外特种药品费用医疗保险金",
        "value": "3000000元",
        "subText": "在保险期间内，被保险人在等待期后出现症状并经医院的专科医生确诊初次罹患主险合同约定的恶性肿瘤（无论一种或多种），且需进行特种药品靶向治疗的，如医院内药房无法提供靶向治疗必需的相关特种药品，对于被保险人因治疗该恶性肿瘤实际支出的、同时满足条件的医院外特种药品费用，保险人按照本附加险合同的约定在保险金额范围内给付保险金。<br>0免赔<br>若被保险人以参加社会基本医疗保险身份或公费医疗身份投保：<br>a.如药品属于社保目录内药品且已经过社会基本医疗保险报销，赔付比例为 100%；<br>b.如药品属于社保目录内药品但未经过社会基本医疗保险报销，赔付比例为 60%；<br>c.如药品属于社保目录外药品，给付比例为 100%。<br>若被保险人以未参加社会基本医疗保险身份或公费医疗身份投保，则社保目录内药品和社保目录外药品给付比例均为 100%<br>海南博鳌乐城国际医疗旅游先行区临床急需进口药品费用保险金给付金额=（发生的临床急需进口药品费用-从其他途径已获得的临床急需进口药品费用补偿）×80%。<br>（含境内上市的靶向药物及海南博鳌乐城国际医疗旅游先行区临床急需进口药品。保额与重大疾病医疗保险金共享保额）"
      },
      { "name": "重大疾病绿色通道服务（增值服务）", "value": "免费", "subText": "服务内容：住院安排、手术安排。等待期后初次确诊罹患重疾客户，为客户协调全国难协调医院科室的手术与住院安排，主要为三甲医院。<br>服务次数：各限 1 次" },
      { "name": "重大疾病住院垫付服务（增值服务）", "value": "免费", "subText": "服务内容：等待期后初次确诊罹患重疾客户住院费用垫付，提供一站式涵院前、院中、院后的全闭环服务，限定本人使用。<br>服务次数：不限次数（单次不超过 5 万，保单年度内、保额下全年不超过 30万）" },
      { "name": "线上问诊+药品折扣（增值服务）", "value": "免费", "subText": "服务内容：全科图文咨询+开方业务+购药折扣，一年不限次<br>服务次数：不限次数" },
      { "name": "护理服务（增值服务）", "value": "免费", "subText": "1、一对一院内护工照护，服务内容：等待期后，客户因意外或疾病，由专业医护团队了解治疗相关信息，制定在院期间的护理计划，指派专属护理服务人员负责客户在院期间的医疗陪护及生活照料。最长不超过 5天 4 夜（须保证连续性，不可拆分使用）。<br>服务次数：限1次<br>2、一对一居家护工照护，服务内容：等待期后，客户因意外或疾病入院治疗后，出院前 1 天，由专业医护团队了解住院相关信息， 制定出院后的的延续护理计划。并按约定时间服务团队前往家中，负责客户出院后的延续护理与生活照料。最长不超过 5 天 4 夜（须保证连续性，不可拆分使用），客户服务申请时间最迟不得超过出院后的 7 个工作日。<br>服务次数：限1次" },
      { "name": "陪诊（增值服务）", "value": "免费", "subText": "服务内容：常规日夜间陪诊，等待期后，服务有效期内初次确诊罹患重疾客户，专业陪诊人员在医院指定地点接送，协助客户进行挂号、排队、付款、心理疏导。一年 2 次，限定本人使用，每次时长不超过4 小时。<br>服务次数：限2次" },
      { "name": "质子重离子绿通（增值服务）", "value": "免费", "subText": "服务内容：质子重离子直通车服务，服务有效期内初次确诊罹患实体肿瘤客户，提供针对性的医<br>学指导意见，方式包括但不局限于上海质子重离子医院协助门诊预约，远程会诊，病理会诊，就诊陪同，协助住院手术的直通车服务。（会诊相关费用由客户自行承担）<br>服务次数：限1次" },
      { "name": "多学科会诊（增值服务）", "value": "免费", "subText": "服务内容：肿瘤 MTB 多学科会诊服务，等待期后，服务有效期内初次确诊罹患重疾客户，提供肿瘤MTB 多学科会诊服务，会诊专家来自全国顶级三甲医院，全面覆盖实体瘤领域，以肿瘤内外科为核心建立影像科、病理科、放射科、化疗科、微创介入科等多科室联动的专家团队，让肿瘤患者在首诊医院，即可得到最先进的多学科诊疗方案。一年 1 次，限定本人使用。<br>服务次数：限1次" },
      {
        "name": "特药服务（增值服务）",
        "value": "免费",
        "subText": "1、国内特药 151种，服务内容：等待期后，初次确诊罹患恶性肿瘤-重度，可提供 151 种国内特药直付服务。针对服务保险责任、通过处方审核的客户，可携带有效药品处方、购药凭证、客户有效身份证件以及社保卡，在指定的药房，购<br>买药品处方中的所列药品或选择送药上门，保险责任范围内的药品费用客户无需自己垫付费用。<br>服务次数：不限次数<br>2、海外特药 15种，服务内容：等待期后，确诊恶性肿瘤-重度，在国内特药不适用的情况下，可为被保险人安排海南博鳌乐城就医服务，提供海外药品，获取全球最好医药资源。<br>服务次数：不限次数<br>3、病程管理，服务内容：医生团队从第一次接触患者起为患者提供病程管理系统，包含治疗方案记录及跟进，用药及不良反应的问询回访、安抚家属。<br>服务次数：不限次数<br>4、慈善赠药，服务内容：根据客户的需求，对客户是否符合慈善赠药的资格进行初步判断，当判断出客户符合慈善机构的援助项目赠药的申请条件时，服务商将协助客户进行慈善赠药申请及材料准备。<br>服务次数：不限次数<br>5、临床试验申请（患者招募）服务，服务内容：为患者提供最新的临床信息和重大疾病医疗咨询服务，旨在帮助重大慢性疾病的患者加入药物研究组，提供免费用药的机会，提高患者的生活质量。<br>服务次数：不限次数"
      },
      { "name": "癌症基因检测补贴（增值服务）", "value": "免费", "subText": "服务内容：<br>为患者提供靶向药物基因检测的优惠补贴服务。患者提前1-3 个工作日提交服务申请，由专属客服提供全程协助服务。根据实际情况，最高可享受 6000 元优惠补贴。<br>服务次数：限1次" },
      { "name": "重大疾病保险金", "value": "50000元", "subText": "在保险期间内，被保险人因意外伤害或在等待期后专科医生初次确诊罹患主险合同约定的重大疾病（一种或多种），保险人按保险单载明的重大疾病保险金额向被保险人给付重大疾病保险金，给付后本附加险项下重大疾病保险责任终止。<br>给付1次为限" },
      { "name": "中症疾病保险金", "value": "20000元", "subText": "在保险期间内，被保险人因意外伤害或在等待期后非因意外伤害而被专科医生初次确诊罹患本附加险合同约定的中症疾病（一种或多种），保险人按保险单载明的中症疾病保险金额向被保险人给付中症疾病保险金，给付后本附加险项下中症疾病保险责任终止。<br>给付1次为限" },
      { "name": "轻症疾病保险金", "value": "10000元", "subText": "在保险期间内，被保险人因意外伤害或在等待期后非因意外伤害而被专科医生初次确诊罹患本附加险合同约定的轻症疾病（一种或多种），保险人按保险单载明的轻症疾病保险金额向被保险人给付轻症疾病保险金，给付后轻症疾病保险责任终止。<br>给付1次为限" }
    ]
  }
  let sortArr = [
    '一般医疗保险金',
    '重大疾病医疗保险金',
    '质子重离子医疗保险金',
    '重大疾病异地转诊公共交通费用保险金',
    '重大疾病住院津贴',
    '恶性肿瘤院外特种药品费用医疗保险金',
    '重大疾病保险金',
    '中症疾病保险金',
    '轻症疾病保险金',
    '重大疾病绿色通道服务（增值服务）',
    '重大疾病住院垫付服务（增值服务）',
    '线上问诊+药品折扣（增值服务）',
    '护理服务（增值服务）',
    '陪诊（增值服务）',
    '质子重离子绿通（增值服务）',
    '多学科会诊（增值服务）',
    '特药服务（增值服务）',
    '癌症基因检测补贴（增值服务）',
  ]
  plan = plan.sort((a, b) => {
    return sortArr.indexOf(a.name) - sortArr.indexOf(b.name);
  })
  plan.forEach(item => {
    if (item.name == '重大疾病医疗保险金') {
      item.name2 = '120种病种查询'
      item.value2 = 'https://oss.99bx.cn/PDF/LBRSAKWY/%E9%87%8D%E7%96%BE%E4%BE%8B%E8%A1%A8.pdf'
    }
    if (item.name == '重大疾病保险金') {
      item.name2 = '120种病种查询'
      item.value2 = 'https://oss.99bx.cn/PDF/LBRSAKWY/%E9%87%8D%E7%96%BE%E4%BE%8B%E8%A1%A8.pdf'
    }
    if (item.name == '中症疾病保险金') {
      item.name2 = '28种病种查询'
      item.value2 = 'https://oss.99bx.cn/PDF/LBRSAKWY/%E4%B8%AD%E7%97%87%E4%BE%8B%E8%A1%A8.pdf'
    }
    if (item.name == '轻症疾病保险金') {
      item.name2 = '45种病种查询'
      item.value2 = 'https://oss.99bx.cn/PDF/LBRSAKWY/%E8%BD%BB%E7%97%87%E4%BE%8B%E8%A1%A8.pdf'
    }
  })
  return plan;
};

exports.getPremium = function (order) {

  logger.info("安康无忧2023版百万医疗险（互联网专属）_order_保费：" + JSON.stringify(order));
  const gender = order["insuredInfo.gender"] || "1";
  const birthday = new Date(order["insuredInfo.birthday"]);
  const startDate = order["mainInfo.startDate"]
    ? new Date(order["mainInfo.startDate"])
    : new Date();
  const age = way.getRealAge(birthday, startDate);
  let planCode = order["mainInfo.planCode"];
  let socialSecurityFlag = order["insuredInfo.socialSecurityFlag"];
  let premium = 0;
  if ((planCode == 'C' || planCode == 'D') && age > 60) {
    // return { errMsg: "60岁以上不能投保计划三/计划四" }
    return { errMsg: "60岁以上不能投保计划四" }
  }
  return co(function* () {
    if (_.isArray(json[planCode])) {
      json[planCode].forEach(item => {
        let [min, max] = item.age.split(',');
        if (age >= min && age <= max) {
          console.log(item, 'item')
          premium = +item[socialSecurityFlag];
        }
      })
    } else {
      json[planCode][gender].forEach(item => {
        let [min, max] = item.age.split(',');
        if (age >= min && age <= max) {
          console.log(item, 'item')
          premium = +item[socialSecurityFlag];
        }
      })
    }
    console.log(premium, "premium", age, "age");
    return { premium };
  }).catch((e) => {
    console.log(e);
    return { errMsg: "LBRSAKWY_保费计算失败" };
  });
};
let json = {
  A: [
    {
      "0": "1955",
      "1": "1020",
      "age": "0,4"
    },
    {
      "0": "870",
      "1": "472",
      "age": "5,10"
    },
    {
      "0": "528",
      "1": "304",
      "age": "11,15"
    },
    {
      "0": "537",
      "1": "306",
      "age": "16,20"
    },
    {
      "0": "660",
      "1": "357",
      "age": "21,25"
    },
    {
      "0": "793",
      "1": "405",
      "age": "26,30"
    },
    {
      "0": "1038",
      "1": "485",
      "age": "31,35"
    },
    {
      "0": "1405",
      "1": "595",
      "age": "36,40"
    },
    {
      "0": "2030",
      "1": "806",
      "age": "41,45"
    },
    {
      "0": "2926",
      "1": "1103",
      "age": "46,50"
    },
    {
      "0": "4048",
      "1": "1480",
      "age": "51,55"
    },
    {
      "0": "5097",
      "1": "1888",
      "age": "56,60"
    },
    {
      "0": "7655",
      "1": "2914",
      "age": "61,65"
    }
  ],
  B: [
    {
      "0": "2332",
      "1": "1271",
      "age": "0,4"
    },
    {
      "0": "1057",
      "1": "597",
      "age": "5,10"
    },
    {
      "0": "715",
      "1": "429",
      "age": "11,15"
    },
    {
      "0": "699",
      "1": "414",
      "age": "16,20"
    },
    {
      "0": "822",
      "1": "465",
      "age": "21,25"
    },
    {
      "0": "955",
      "1": "513",
      "age": "26,30"
    },
    {
      "0": "1221",
      "1": "593",
      "age": "31,35"
    },
    {
      "0": "1629",
      "1": "727",
      "age": "36,40"
    },
    {
      "0": "2397",
      "1": "1022",
      "age": "41,45"
    },
    {
      "0": "3324",
      "1": "1337",
      "age": "46,50"
    },
    {
      "0": "4655",
      "1": "1837",
      "age": "51,55"
    },
    {
      "0": "5866",
      "1": "2293",
      "age": "56,60"
    },
    {
      "0": "9330",
      "1": "3752",
      "age": "61,65"
    }
  ],
  C: {
    1: [
      {
        "0": "1985",
        "1": "1050",
        "age": "0,4"
      },
      {
        "0": "894",
        "1": "496",
        "age": "5,10"
      },
      {
        "0": "556",
        "1": "332",
        "age": "11,15"
      },
      {
        "0": "573",
        "1": "342",
        "age": "16,20"
      },
      {
        "0": "719",
        "1": "416",
        "age": "21,25"
      },
      {
        "0": "869",
        "1": "481",
        "age": "26,30"
      },
      {
        "0": "1165",
        "1": "612",
        "age": "31,35"
      },
      {
        "0": "1606",
        "1": "796",
        "age": "36,40"
      },
      {
        "0": "2344",
        "1": "1120",
        "age": "41,45"
      },
      {
        "0": "3379",
        "1": "1556",
        "age": "46,50"
      },
      {
        "0": "4612",
        "1": "2044",
        "age": "51,55"
      },
      {
        "0": "5886",
        "1": "2677",
        "age": "56,60"
      }
    ],
    2: [
      {
        "0": "1991",
        "1": "1056",
        "age": "0,4"
      },
      {
        "0": "896",
        "1": "498",
        "age": "5,10"
      },
      {
        "0": "560",
        "1": "336",
        "age": "11,15"
      },
      {
        "0": "581",
        "1": "350",
        "age": "16,20"
      },
      {
        "0": "719",
        "1": "416",
        "age": "21,25"
      },
      {
        "0": "881",
        "1": "493",
        "age": "26,30"
      },
      {
        "0": "1161",
        "1": "608",
        "age": "31,35"
      },
      {
        "0": "1603",
        "1": "793",
        "age": "36,40"
      },
      {
        "0": "2395",
        "1": "1171",
        "age": "41,45"
      },
      {
        "0": "3442",
        "1": "1619",
        "age": "46,50"
      },
      {
        "0": "4742",
        "1": "2174",
        "age": "51,55"
      },
      {
        "0": "6081",
        "1": "2872",
        "age": "56,60"
      }
    ]
  },
  D: {
    1: [
      {
        "0": "2362",
        "1": "1301",
        "age": "0,4"
      },
      {
        "0": "1081",
        "1": "621",
        "age": "5,10"
      },
      {
        "0": "743",
        "1": "457",
        "age": "11,15"
      },
      {
        "0": "735",
        "1": "450",
        "age": "16,20"
      },
      {
        "0": "881",
        "1": "524",
        "age": "21,25"
      },
      {
        "0": "1031",
        "1": "589",
        "age": "26,30"
      },
      {
        "0": "1348",
        "1": "720",
        "age": "31,35"
      },
      {
        "0": "1830",
        "1": "928",
        "age": "36,40"
      },
      {
        "0": "2711",
        "1": "1336",
        "age": "41,45"
      },
      {
        "0": "3777",
        "1": "1790",
        "age": "46,50"
      },
      {
        "0": "5219",
        "1": "2401",
        "age": "51,55"
      },
      {
        "0": "6655",
        "1": "3082",
        "age": "56,60"
      }
    ],
    2: [
      {
        "0": "2368",
        "1": "1307",
        "age": "0,4"
      },
      {
        "0": "1083",
        "1": "623",
        "age": "5,10"
      },
      {
        "0": "747",
        "1": "461",
        "age": "11,15"
      },
      {
        "0": "743",
        "1": "458",
        "age": "16,20"
      },
      {
        "0": "881",
        "1": "524",
        "age": "21,25"
      },
      {
        "0": "1043",
        "1": "601",
        "age": "26,30"
      },
      {
        "0": "1344",
        "1": "716",
        "age": "31,35"
      },
      {
        "0": "1827",
        "1": "925",
        "age": "36,40"
      },
      {
        "0": "2762",
        "1": "1387",
        "age": "41,45"
      },
      {
        "0": "3840",
        "1": "1853",
        "age": "46,50"
      },
      {
        "0": "5349",
        "1": "2531",
        "age": "51,55"
      },
      {
        "0": "6850",
        "1": "3277",
        "age": "56,60"
      }
    ]
  }
}
