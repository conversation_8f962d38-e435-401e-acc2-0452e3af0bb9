'use strict';
const Promise = require('bluebird');
const moment = require('moment');

const products = [
    /**
     * DCRS products
     */
    {
        DCYSGAYSYXBYLNJ70: {
            productName: '鼎诚一生关爱（颐享版）养老年金（0-70周岁）',
            providerCode: 'DCRS',
            providerName: '鼎诚人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        DCYSGAYXBYLNJ80: {
            productName: '鼎诚一生关爱（颐享版）养老年金（71-80周岁）',
            providerCode: 'DCRS',
            providerName: '鼎诚人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        HDCRS2024P00002041: {
            productName: '鼎诚鼎鑫年年养老年金保险（分红型）',
            providerCode: 'DCRS',
            providerName: '鼎诚人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        HDCRS2024P00002042: {
            productName: '鼎诚诚心如意（尊享版）终身寿险',
            providerCode: 'DCRS',
            providerName: '鼎诚人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        HDCRS2024P00002043: {
            productName: '鼎诚一生关爱（福瑞版）养老年金保险',
            providerCode: 'DCRS',
            providerName: '鼎诚人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        HDCRS2024P00002044: {
            productName: '鼎诚诚心如意（庆典版）终身寿险',
            providerCode: 'DCRS',
            providerName: '鼎诚人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        HDCRS2024P00002057: {
            productName: '鼎诚诚心如意（尊享版）终身寿险（分销）',
            providerCode: 'DCRS',
            providerName: '鼎诚人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        HDCRS2025P00002289: {
            productName: '鼎诚诚心如意（臻享版）终身寿险',
            providerCode: 'DCRS',
            providerName: '鼎诚人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        HDCRS2025P00002303: {
            productName: '鼎诚伴无忧护理保险',
            providerCode: 'DCRS',
            providerName: '鼎诚人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        HDCRS2025P00002367: {
            productName: '鼎诚一生关爱（福满满）养老年金保险',
            providerCode: 'DCRS',
            providerName: '鼎诚人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        }
    }];

exports.getProducts = () => {
    return products;
};
