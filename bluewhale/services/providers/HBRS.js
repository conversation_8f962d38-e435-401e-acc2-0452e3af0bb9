'use strict';
const Promise = require('bluebird');
const way = require('../../services/products/way');
const lifeValidate = require('../../utils/commonUtil').lifeValidate;

const products = [
    {
        HBCJMLDBB: {
            productName: '海保超级玛丽多倍版',
            providerCode: 'HBRS',
            providerName: '海保人寿',
            autoComplete: proposal => {
                let riskCode = '';
                let productCode = '';
                let productName = '';
                if (proposal.mainInfo.termCode === '70y') {
                    productCode = '118113';
                    productName = '海保人寿倍加尔保定期重大疾病保险';
                    riskCode = '811301';
                    if (proposal.mainInfo.middleIllness === '1') {
                        riskCode = '811302';
                    }
                    if (proposal.mainInfo.lightDisease === '1') {
                        riskCode = '811303';
                    }
                    if (proposal.mainInfo.middleIllness === '1' && proposal.mainInfo.lightDisease === '1') {
                        riskCode = '811304';
                    }
                }
                if (proposal.mainInfo.termCode === '终身') {
                    productCode = '118112';
                    productName = '海保人寿倍加尔保终身重大疾病保险';
                    riskCode = '811201';
                    if (proposal.mainInfo.middleIllness === '1') {
                        riskCode = '811202';
                    }
                    if (proposal.mainInfo.lightDisease === '1') {
                        riskCode = '811203';
                    }
                    if (proposal.mainInfo.middleIllness === '1' && proposal.mainInfo.lightDisease === '1') {
                        riskCode = '811204';
                    }
                }
                proposal.mainInfo.chargeGap = '1';
                proposal.extInfo.productCode = productCode;
                proposal.extInfo.productName = productName;
                proposal.extInfo.riskCode = riskCode;
                if (proposal.mainInfo.appliRemit === '1') {
                    proposal.addRiskList = [{}];
                    proposal.addRiskList[0].productId = '5d3962f4b0a86d9e268f86e1';
                    proposal.addRiskList[0].productCode = 'HBRSFJTBRHMBXFZDJBBX';
                    proposal.addRiskList[0].productName = '海保人寿附加投保人豁免保险费重大疾病保险';
                    proposal.addRiskList[0].chargeCode = parseInt(proposal.mainInfo.chargeCode) - 1;
                    proposal.addRiskList[0].termCode = proposal.mainInfo.termCode;
                    proposal.addRiskList[0].premium = proposal.mainInfo.additionalPremium;
                }
                proposal.dlFlag = '1';
                return Promise.resolve(proposal);
            },
            validate: lifeValidate,
        },
        HBRSXXYZSSXFX: {
            productName: '海保人寿鑫玺越终身寿险（分销）',
            providerCode: 'HBRS',
            providerName: '海保人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            }
        },
        HHBRS2025P00002208: {
            productName: '海保人寿增多多8号增额终身寿险(分销）',
            providerCode: 'HBRS',
            providerName: '海保人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            }
        },
        HHBRS2025P00002315: {
            productName: '康乾7号-海保人寿互联网瑞享生活特定疾病保险（分销）',
            providerCode: 'HBRS',
            providerName: '海保人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            }
        },
        HHBRS2025P00002368: {
            productName: '海保人寿增多多8号增额终身寿险(庆典版)（分销）',
            providerCode: 'HBRS',
            providerName: '海保人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            }
        },
        HHBRS2025P00002369: {
            productName: '海保人寿金管家2025终身寿险(万能型)（绑定增多多8号及增多多8号庆典版）（分销）',
            providerCode: 'HBRS',
            providerName: '海保人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            }
        }
    },
];

exports.getProducts = () => {
    return products;
};
