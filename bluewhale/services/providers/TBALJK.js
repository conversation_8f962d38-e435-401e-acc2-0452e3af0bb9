"use strict";
const Promise = require("bluebird");
const moment = require("moment");
const logger = require("@huibao/logger").logger();

const PropertyProposal = require("../../models/PropertyProposal");
const commonValidate = require("../../utils/commonUtil").commonValidate;

//验证保额限额
let amountMaxValidate = proposal => {
    let condition = {
        "mainInfo.validInd": "1",
        productCode: proposal.productCode,
        "insuredInfo.idNo": proposal.insuredInfo.idNo,
        "mainInfo.startDate": { $lt: proposal.mainInfo.endDate },
        "mainInfo.endDate": { $gt: proposal.mainInfo.startDate },
        $or: [
            { status: "2" }, //active
            {
                $and: [
                    { status: { $in: ["0", "1", "12"] } },
                    { "mainInfo.startDate": { $gt: new Date() } } //not overdue for paying
                ]
            }
        ]
    };
    logger.info("condition:", condition);

    return PropertyProposal.aggregate([
        { $match: condition },
        { $group: { _id: null, totalSum: { $sum: "$mainInfo.sumAmount" } } }
    ]).then(sum => {
        var totalAmount = (sum[0] && sum[0]["totalSum"]) || 0;
        let currentAmount = proposal.mainInfo.sumAmount;
        logger.info(`从数据库中取出的总份数为：${totalAmount}`);

        if (+currentAmount + +totalAmount > 300000) {
            return Promise.reject(
                `此产品被保险人最高保障额度为30万元，您的此产品额度已超过限额。`
            );
        } else {
            return Promise.resolve(proposal);
        }
    });
};
const products = [
    /**
     * TBALJK products
     * 全民百万医疗 TBALQMBWYL
     * 四季宝贝 TBALSJBB
     */
    {
        JL1HZEZSTDJBXHLW: {
            productName: "锦鲤1号增额终身特定疾病险（互联网）",
            providerCode: "TBALJK",
            providerName: "太平洋健康保险",
            autoComplete: proposal => {
                proposal.dlFlag = "1";
                return Promise.resolve(proposal);
            }
        },
        TBJKXSZSSX46FX: {
            productName: "太保智相守终身护理险（被保险人46岁及以上）(分销）",
            providerCode: "TBALJK",
            providerName: "太平洋健康保险",
            autoComplete: proposal => {
                proposal.dlFlag = "1";
                return Promise.resolve(proposal);
            }
        },
        TPYJKZXSZSHLXFX: {
            productName: "太保智相守终身护理险（被保险人45岁及以下）(分销）",
            providerCode: "TBALJK",
            providerName: "太平洋健康保险",
            autoComplete: proposal => {
                proposal.dlFlag = "1";
                return Promise.resolve(proposal);
            }
        },
        GRZDJBBXHLWFX: {
            productName: "太保个人重大疾病保险（互联网）（分销）",
            providerCode: "TBALJK",
            providerName: "太平洋健康保险",
            autoComplete: proposal => {
                proposal.dlFlag = "1";
                return Promise.resolve(proposal);
            }
        },
        HTBALJK2024P002005: {
            productName: "e亨护-医享无忧百万医疗20年（分销）",
            providerCode: "TBALJK",
            providerName: "太平洋健康保险",
            autoComplete: proposal => {
                proposal.dlFlag = "1";
                return Promise.resolve(proposal);
            }
        },
        TBJKLYBCQYLX: {
            productName: "蓝医保·长期医疗险（分销）",
            providerCode: "TBALJK",
            providerName: "太平洋健康保险",
            autoComplete: proposal => {
                proposal.dlFlag = "1";
                return Promise.resolve(proposal);
            }
        },
        HTBALJK2024P002031: {
            productName: "蓝医保终身防癌医疗险（税优版）（分销）",
            providerCode: "TBALJK",
            providerName: "太平洋健康保险",
            autoComplete: proposal => {
                proposal.dlFlag = "1";
                return Promise.resolve(proposal);
            }
        },
        HTBALJK2024P002032: {
            productName: "蓝医保住院医疗险（税优版）（分销）",
            providerCode: "TBALJK",
            providerName: "太平洋健康保险",
            autoComplete: proposal => {
                proposal.dlFlag = "1";
                return Promise.resolve(proposal);
            }
        },
        HTBALJK2025P002371: {
            productName: "太保互联网（2024）终身B款重大疾病保险（锦鲤3号）（分销）",
            providerCode: "TBALJK",
            providerName: "太平洋健康保险",
            autoComplete: proposal => {
                proposal.dlFlag = "1";
                return Promise.resolve(proposal);
            }
        }
    }
];

exports.getProducts = () => {
    return products;
};
