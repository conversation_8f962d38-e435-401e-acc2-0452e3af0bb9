// test by tantan
'use strict';
const Promise = require('bluebird');
const moment = require('moment');
const { commonValidate } = require('../../utils/commonUtil');

const planMap = {
    'A':'基础版',
    'B':'升级版',
    'C':'精英版',
    'D':'尊享版',
    'E':'至尊版',
}
const planMap2 = {
    'A':'10万意外身故伤残/1万意外医疗/100元意外住院津贴',
    'B':'20万意外身故伤残/2万意外医疗/100元意外住院津贴',
    'C':'30万意外身故伤残/3万意外医疗/100元意外住院津贴',
    'D':'40万意外身故伤残/4万意外医疗/100元意外住院津贴',
    'E':'50万意外身故伤残/5万意外医疗/100元意外住院津贴',
}


const products = [
    // YGCX insure product
    {
        YGBBXSPABX: {//
            productName: '阳光宝贝学生平安保险',
            providerCode: 'YGCX',
            providerName: '阳光财险',
            autoComplete: proposal => {
                proposal.extInfo = (proposal.extInfo == null ? {} : proposal.extInfo);
                proposal.mainInfo.endDate = moment(proposal.mainInfo.startDate).add(1, 'y').add(-1, 's')
                    .toDate();
                proposal.mainInfo.planName = planMap[proposal.mainInfo.planCode];
                // proposal.mainInfo.sumAmount = 38000 * proposal.mainInfo.unitCount;
                proposal.extInfo.productCode = '';
                proposal.extInfo.riskCode = 'SV000223';
                proposal.extInfo.productName = '';
                return Promise.resolve(proposal);
            },
            validate: commonValidate,
        },
        YGDGRTTYWX: {//
            productName: '阳光打工人团体意外险',
            providerCode: 'YGCX',
            providerName: '阳光财险',
            autoComplete: proposal => {
                proposal.extInfo = (proposal.extInfo == null ? {} : proposal.extInfo);
                proposal.mainInfo.endDate = moment(proposal.mainInfo.startDate).add(proposal.mainInfo.termCode, 'months').add(-1, 's')
                    .toDate();
                proposal.mainInfo.planName = planMap2[proposal.mainInfo.planCode];
                // proposal.mainInfo.sumAmount = 38000 * proposal.mainInfo.unitCount;
                proposal.extInfo.productCode = '';
                // proposal.extInfo.riskCode = 'SV000223';
                proposal.extInfo.productName = '';
                return Promise.resolve(proposal);
            },
        },
        YGCXLYX: {
            productName: '旅意险',
            providerCode: 'YGCX',
            providerName: '阳光财险',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        }
    }];

exports.getProducts = () => {
    return products;
};
