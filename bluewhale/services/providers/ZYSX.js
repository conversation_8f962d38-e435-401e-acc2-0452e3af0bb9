'use strict';
const _ = require('lodash');
const moment = require('moment');
const Promise = require('bluebird');
const logger = require('@huibao/logger').logger();

const products = [
    {
        ZYYSZYZSSXFHX: {
            productName: '中意一生中意终身寿险（分红型）',
            providerCode: 'ZYSX',
            providerName: '中意人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        ZYYXAKZDJBBXQDB: {
            productName: '中意悦享安康重大疾病保险（庆典版）',
            providerCode: 'ZYSX',
            providerName: '中意人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        ZYYXAKZDJBBXQNB: {
            productName: '中意悦享安康重大疾病保险（全能版）',
            providerCode: 'ZYSX',
            providerName: '中意人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        ZYZXYSXXBZSSXFHX: {
            productName: '中意臻享一生（鑫玺版）终身寿险（分红型）',
            providerCode: 'ZYSX',
            providerName: '中意人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        ZYYSZYLXBZSSXFHX: {
            productName: '中意一生中意（龙玺版）终身寿险（分红险）',
            providerCode: 'ZYSX',
            providerName: '中意人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        ZYYSZYLXBZSSXFHXFX: {
            productName: '中意一生中意（龙玺版）终身寿险（分红险）（分销）',
            providerCode: 'ZYSX',
            providerName: '中意人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        ZYYXAKZDJBBXHXB: {
            productName: '中意悦享安康重大疾病保险（惠选版）',
            providerCode: 'ZYSX',
            providerName: '中意人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        ZYYSZYZXBZSSXFHX: {
            productName: '中意一生中意（尊享版）终身寿险（分红型）',
            providerCode: 'ZYSX',
            providerName: '中意人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        },
        HZYSX2025P00002370: {
            productName: '中意人寿擎天柱10号定期寿险（互联网）（分销）',
            providerCode: 'ZYSX',
            providerName: '中意人寿',
            autoComplete: proposal => {
                return Promise.resolve(proposal);
            },
        }
    }
];

exports.getProducts = () => {
    return products;
};
