'use strict';
const moment = require('moment');
const _ = require('lodash');
/**
 * load one client for this transformer
 */
const client = require('../clients/HTCXClient');
exports.getClient = () => {
    return client;
};

const config = {
    test: {
        channelCode: 'HT101456'
    },
    prod:{
        channelCode: 'HT101276'
    }
}

const idTypeMap = {
    1: '01', // 身份证
    14: '02', // 军官证
    2: '03', // 护照
    11: '04', // 驾驶证
    4: '14', // 出生证
    13: '13', // 户口本
    5: '10', // 港澳证
    9: '97', // 统一社会信用代码
};


/**
 * 00 本人
01 配偶
02 父母
03 子女
05 兄弟姐妹
06 雇主
07 雇员
08 祖父母、外祖父母
09 祖孙、外祖孙
10 监护人
11 被监护人
12 朋友
17 雇佣
98 未知
99 其他
 */
// 被保人与投保人关系 （受益人与被保人关系）
const relMap = {
    2: '00', // 本人
    4: '01', // 配偶
    3: '02', // 父母
    5: '03', // 子女
    7: '05', // 兄弟姐妹
    71: '17', // 雇佣
};

const occupLevel = {
    '1':'HT3001241',
    '2':'HT3001242',
    '3':'HT3001243',
    '4':'HT3001244',
    '5':'HT3001245',
    '6':'HT3001246',
}


/**
 * 我方地址转换为保司地址
 * @param {*} addr 地址
 * @example 北京市 / 市辖区 / 东城区
 * @return {*} 北京市市辖区东城区
 */
const toAddress = addr => {
    return addr && addr.replace(/( |\/)/g, '');
};

exports.proposalOut = proposal => {
    let cfg = config.test;
    if (global.env === 'production') {
        cfg = config.prod;
    }

    const appliInfoAddress = toAddress(proposal.appliInfo.address);

    let dutys = getDutys(proposal);
    let categoryVos = getCategoryVos(proposal);
    const reqObj = {
        requestHeadVo: {
            channelCode: cfg.channelCode,
            requestType: 'HTIC002',
        },
        publicOrderVo: {
            // 投保人
            policyApplicantVo: {
                appAddr: appliInfoAddress,
                appContact: proposal.appliInfo.cntctName,
                appEmail: proposal.appliInfo.email,
                appName: proposal.appliInfo.name,
                appNum: proposal.appliInfo.idNo,
                appType: proposal.gFlag == '1' ? '2': "1",
                appidType: idTypeMap[proposal.appliInfo.idType],
                apptelNum: proposal.appliInfo.mobile,
                appGender: proposal.appliInfo.gender ? proposal.appliInfo.gender : "",
                appBirthday: proposal.appliInfo.birthday ? moment(proposal.appliInfo.birthday).format('YYYY-MM-DD') : "",
                isTaxInvoice: "0",
                visaType: "1"
            },
            policyBeneficiaryVos: [],
            policyInsuredVos: [],

            // 动态数据
            policyDynamicVos: [],

            policyMainVo: {
                amount: proposal.mainInfo.sumAmount,
                amtCur: "01",
                chlCode: cfg.channelCode,
                chlName: "上海汇中保险经纪有限责任公司",
                chlOrderNo: `${cfg.channelCode}${+moment()}`,
                copy: 1,
                dataProducer: "AP",
                effectiveTm: moment(proposal.mainInfo.startDate).format('YYYY-MM-DD HH:mm:ss'),
                openId: "",
                paymentWayCode: "1",
                premium: proposal.mainInfo.sumPremium,
                prmCur: "01",
                proTm: `${+moment()}`,
                productCode: proposal.extInfo.productCode,
                terminalTm: moment(proposal.mainInfo.endDate).format('YYYY-MM-DD HH:mm:ss'),
            },
            policyProgrammeVos: [{
                amount: proposal.mainInfo.sumAmount,
                chlPolicyNo: "",
                groupProductCodeNo: "",
                isAutorenewal: "0",
                isGroup: proposal.gFlag, // 是否团单 默认0,可用值:1:是,0:否
                isRenewal: "1", // 是否续保字段 1: 正常投保；可用值:1:正常投保,2:续保
                srcPlyNo: "",   // 续保保单号
                paymentWayCode: "1",
                plyNo: "",
                policyRdrCategoryVos: categoryVos,
                policyRdrVos: dutys
            }]
        }
    }

    let insuredList = [];
    if (proposal.insuredList && proposal.insuredList.length > 0) {
        insuredList = proposal.insuredList;
    } else {
        insuredList.push(proposal.insuredInfo);
    }

    let insuredObject = [], count = 1;
    for (let i = 0; i < insuredList.length; i++) {
        const insuredInfo = insuredList[i];
        const insured = {
            insuredBirthday: moment(insuredInfo.birthday).format('YYYY-MM-DD'),
            insuredEmail: insuredInfo.email,
            insuredGender: insuredInfo.gender,
            insuredIdType: idTypeMap[insuredInfo.idType],
            insuredName: insuredInfo.name,
            insuredNo: (i + 1),
            insuredNum: insuredInfo.idNo,
            insuredPremium: insuredInfo.premium,
            insuredTelNum: insuredInfo.mobile,
            creditLevel: occupLevel[insuredInfo.occType.lvl],
            insuredType: "1",
            isHolder: "0",
            islegal: proposal.mainInfo.benType == '2' ? '0' : '1',
            itemNo: +insuredInfo.code,
            relationship: relMap[insuredInfo.insAppRel], // 投被保人关系
        }
        insuredObject.push(insured);
        // 社保通过动态数据传
        if(insuredInfo.socialSecurityFlag) {
            reqObj.publicOrderVo.policyDynamicVos.push({
                dynamicId: `${count}`,
                dynamicKey: "socialFlag",
                dynamicMean: "有无社保",
                dynamicValue: insuredInfo.socialSecurityFlag,
                insuredNo: insured.insuredNo,
                itemNo: "1"
            })
            count++;
        }
        // 驾乘险产品动态参数
        if(['CXHAXJCRYYWXYYB', 'CXHAXJCRYYWXFYYB'].includes(proposal.productCode)) {
            reqObj.publicOrderVo.policyDynamicVos.push({
                dynamicId: `${count}`,
                dynamicKey: "vehicleNum",
                dynamicMean: "（驾乘）车牌号",
                dynamicValue: proposal.otherInfo.address,
                insuredNo: insured.insuredNo,
                itemNo: "1"
            });
            count++;
            reqObj.publicOrderVo.policyDynamicVos.push({
                dynamicId: `${count}`,
                dynamicKey: "vehicleSeatCcount",
                dynamicMean: "（驾乘）座位数",
                dynamicValue: proposal.otherInfo.architecture,
                insuredNo: insured.insuredNo,
                itemNo: "1"
            });
        }
    }
    reqObj.publicOrderVo.policyInsuredVos = insuredObject;

    if(proposal.renewalInd == '1') {
        reqObj.publicOrderVo.policyProgrammeVos[0].isRenewal = '2';
        reqObj.publicOrderVo.policyProgrammeVos[0].srcPlyNo = proposal.migratedPolicyNo;
    }
    return reqObj;
};


exports.proposalIn = resObj => {
    if (resObj.status != 0) {
        return { code: '999', msg: resObj.statusText };
    }
    let main = resObj.data.contractMainList[0];
    const wrappedResObj = {
        code: '000',
        proposalNo: main.orderNo,
        tradeId: resObj.data.transid,
    };
    return wrappedResObj;
};


exports.getPayUrlOut = proposal => {
    let cfg = config.test;
    if (global.env === 'production') {
        cfg = config.prod;
    }

    let redirectUrl = '';
    if (global.env == 'production') {
        redirectUrl = 'https://spa.hizom.cn/appui/#/pay/success';
    } else {
        redirectUrl = 'https://t.99bx.cn/appui/#/pay/success';
    }

    const reqObj = {
        requestHeadVo: {
            channelCode: cfg.channelCode,
            requestType: "HTIC031",
            thirdTransId: `${cfg.channelCode}${+moment()}`
        },
        paysigningVo: {
            appId: "",
            browserLogo: "1",
            chlCde: cfg.channelCode,
            contractInfoList: {
                contractNo: proposal.mainInfo.proposalNo,
                periodNo: 1
            },
            openId: "",
            isApi: 0,
            signType: 2,
            browserLogo: 1,
            prePaymentMark: 0,
            payAmount: proposal.mainInfo.sumPremium,
            remark: "",
            settleMode: "WXJSAPI,WXWap,ZFBJSAPI,ZFBWap", // WXNative,ZFBNative
            successfulJumpAddress: redirectUrl
        },

    };
    return reqObj;
};

exports.getPayUrlIn = resObj => {
    if (resObj.status != 0) {
        return { code: '999', msg: '保司支付链接返回失败' + resObj.statusText };
    }

    let payInfo = resObj.data.OUT[0];
    let wrappedResObj = {
        code: '000',
        url: payInfo.PayURL,
        msg: '获取支付链接成功',
    };
    return wrappedResObj;
};

exports.policyOut = proposal => {
    return {
        proposalNo: proposal.mainInfo.proposalNo,
        policyNo: proposal.mainInfo.policyNo,
    };
};

exports.policyIn = resObj => {
    return resObj;
};

exports.cancelOut = proposal => {
    const reqObj = {
        bizCode: '103',
        channelCode: '100181',
        applyRefundTime: moment().format('YYYY-MM-DD hh:mm:ss'),
        orderId: proposal.extInfo.tradeId,
        channelName: '华泰',
        policyNo: proposal.mainInfo.policyNo,
    };
    return reqObj;
};

exports.cancelIn = resObj => {
    if (resObj.responseCode == '0') return { code: '999', msg: resObj.responseInfo };
    return {
        code: '000',
        msg: resObj.responseInfo,
        policyNo: resObj.policyNO,
    };
};

exports.downloadInvoiceOut = proposal => {
    const reqObj = {
        policyNo: proposal.mainInfo.policyNo,
        invoiceTitle: proposal.appliInfo.name,
        creditCode: proposal.appliInfo.idNo,
        companyAddress: "",
        companyPhone: "",
        bankName: "",
        bankAccount: "",
        telephone: proposal.appliInfo.mobile,
        email: proposal.appliInfo.email,
        periodNo: "1",
        applicantDate: moment().format('YYYY-MM-DD HH:mm:ss'),
    };
    return reqObj;
};

exports.downloadInvoiceIn = resObj => {
    if (resObj.status != 0) {
        return { code: '999', msg: '保司支付链接返回失败' + resObj.statusText };
    }
    let data = resObj.data;
    return {
        code: '000',
        evUrl: data.downloadLink,
        msg: resObj.statusText,
    };
};


// 处理责任列表
function getDutys(proposal) {
    let all_list = [];
    if(proposal.productCode == 'JDLZHJTYWX') {
        return all_list;
    }
    let glist = _.groupBy(proposal.insuredList, 'code');
    for(let key in glist) {
        let itemNo = +key;
        // 必选责任
        let list = [
            {
                itemNo: itemNo,
                rdrCode: "4000000",
                rdrName: "",
                rdrProperty: "1"
            },
            {
                itemNo: itemNo,
                maxDayCount: 180,
                perDayAmount: 100,
                rdrCode: "4000004",
                rdrName: "",
                rdrProperty: "2"
            },
            {
                itemNo: itemNo,
                rdrCode: "4000003",
                rdrName: "",
                rdrProperty: "1"
            }
        ];

        // 扩展自费药
        if (proposal.otherInfo.accident == "1") {
            list = list.concat([
                {
                    itemNo: itemNo,
                    rdrCode: "4029325",
                    rdrName: "",
                    rdrProperty: "1"
                }
            ]);
        }
        // 猝死责任保险金
        if (proposal.otherInfo.duty == "1") {
            list = list.concat([
                {
                    itemNo: itemNo,
                    rdrCode: "5000215",
                    rdrName: "",
                    rdrProperty: "1"
                }
            ]);
        }
        // 公共交通工具意外身故伤残保险金
        if (proposal.otherInfo.traffic == "1") {
            list = list.concat([
                {
                    itemNo: itemNo,
                    rdrCode: "4000016",
                    rdrName: "",
                    rdrProperty: "1"
                },
                {
                    itemNo: itemNo,
                    rdrCode: "4000017",
                    rdrName: "",
                    rdrProperty: "1"
                },
                {
                    itemNo: itemNo,
                    rdrCode: "4000018",
                    rdrName: "",
                    rdrProperty: "1"
                },
                {
                    itemNo: itemNo,
                    rdrCode: "4000019",
                    rdrName: "",
                    rdrProperty: "1"
                }
            ]);
        }

        // 法定传染病保险金
        if (proposal.otherInfo.legal == "1") {
            list = list.concat([
                {
                    itemNo: itemNo,
                    rdrCode: "8001091",
                    rdrName: "",
                    rdrProperty: "1"
                }
            ]);
        }
        all_list = all_list.concat(list);
    }
    return all_list;;
}

// 责任方案
function getCategoryVos(proposal) {
    let list = []
    if(proposal.productCode == 'YGDGRTTYWXPLUS') {
        let glist = _.groupBy(proposal.insuredList, 'code');
        for(let key in glist) {
            // console.log(key);
            // console.log(glist[key]);
            list.push({
                itemNo: +key,
                personDecimal: `${glist[key].length}`,
                plan: proposal.extInfo.termCode
            })
        }
    } else {
        list.push({
            itemNo: 1,
            personDecimal: `1`,
            plan: proposal.extInfo.termCode
        })
    }
    return list;
}
