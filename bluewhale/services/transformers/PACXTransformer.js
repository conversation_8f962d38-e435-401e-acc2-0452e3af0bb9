"use strict";
const moment = require("moment");

const logger = require("@huibao/logger").logger();

const codeUtil = require("../../utils/codeUtil");

const way = require("../products/way");

/**
 * load one client for this transformer
 */
const client = require("../clients/PACXClient");
exports.getClient = () => {
    return client;
};

// 合伙伙伴账号
const accountCode = "P_SHHZ_PE_22177_0001";

// 平安财险：（非空）与投保人关系。1:本人 2:配偶 3 :父子 4:父女 5:受益人 6:被保人 7:投保人 A:母子 B:母女 C:兄弟 D:姊妹 E:兄妹 F:姐弟 G:祖孙 H:雇佣 I:子女 9:其他 8:转换不详
const relMap = {
    "1-1": "9", // 法定
    "1-2": "9", // 法定
    "2-1": "1", // 本人
    "2-2": "1", // 本人
    "3-1-1": "3", // 父母
    "3-1-2": "4", // 父母
    "3-2-1": "A", // 父母
    "3-2-2": "B", // 父母
    "4-1": "2", // 配偶
    "4-2": "2", // 配偶
    "5-1": "I", // 子女
    "5-2": "I", // 子女
    "99-1": "9", // 其他
    "99-2": "9", // 其他
    "12-1": "G", // 祖孙
    "12-2": "G" // 祖孙
};

// 平安财险：01:身份证、02：护照、03：军人证、04：港澳通行证，05：驾驶证、06：港澳回乡证或台胞证，07：临时身份证、99：其他",
const idTypeMap = {
    1: "01", // 身份证
    12: "01", // 临时身份证
    2: "02", // 护照
    3: "03", // 士兵证
    4: "99", // 出生证
    5: "06", // 港澳证
    6: "99", // 组织机构代码证
    7: "06", // 台胞证
    8: "99", // 外国人居留证
    11: "05", // 驾驶证
    99: "99" // 其它
};

const genderMap = {
    1: "F",
    2: "M"
};

const mapHouse = {
    A: "01",
    B: "04",
    C: "05",
    D: "02"
};

exports.proposalOut = proposal => {
    // 提取URL配置到常量，便于维护
    const isProduction = global.env === 'production';
    const RETURN_URL = isProduction ? 'https://bw.hizom.cn/api/endor/pacx/callback' : 'https://testbw.99bx.cn/api/endor/pacx/callback';
    const FRONT_URL = isProduction ?
        `https://spa.hizom.cn/appui/#/pay/success?hideRecommend=1&oid=${proposal.oid}` :
        `https://t.99bx.cn/appui/#/pay/success?hideRecommend=1&oid=${proposal.oid}`;

    // 创建被保人对象
    const createInsuredObject = (insuredInfo) => ({
        // 个团标志 1个人 0团体 默认1
        personnelType: "1",
        birthday: moment(insuredInfo.birthday).format("YYYY-MM-DD"),
        certificateNo: insuredInfo.idNo,
        sexCode: genderMap[insuredInfo.gender],
        mobileTelephone: insuredInfo.mobile,
        name: insuredInfo.name,
        age: insuredInfo.age,
        certificateType: idTypeMap[insuredInfo.idType],
        riskInfo: {
            professionCode: "05",
            professionClass: "3",
            isSociaSecurity: "1"
        }
    });

    // 创建基础信息对象
    const createBaseInfo = (mainInfo, extInfo, renewalInd, migratedPolicyNo) => {
        const baseInfo = {
            insuranceBeginDate: moment(mainInfo.startDate).format("YYYY-MM-DD HH:mm:ss"),
            insuranceEndDate: moment(mainInfo.endDate).format("YYYY-MM-DD HH:mm:ss"),
            // 产品编码
            productCode: extInfo.productCode,
            // 保费
            totalActualPremium: +mainInfo.sumPremium,
            // 续保类型 0-新保 1-续保
            renewalType: "0",
            // 续保上次保单号
            lastPolicyNo: "",
        };

        // 处理续保信息
        if (renewalInd == "1") {
            baseInfo.lastPolicyNo = migratedPolicyNo;
            baseInfo.renewalType = "1";
        }

        return baseInfo;
    };

    // 创建投保人信息对象
    const createApplicantInfo = (appliInfo) => ({
        // 个团标志 1个人 0团体 默认1
        personnelType: "1",
        name: appliInfo.name,
        certificateType: idTypeMap[appliInfo.idType],
        certificateNo: appliInfo.idNo,
        birthday: moment(appliInfo.birthday).format("YYYY-MM-DD"),
        // (F-女 M-男)
        sexCode: genderMap[appliInfo.gender],
        mobileTelephone: appliInfo.mobile,
        email: appliInfo.email,
    });

    // 创建标的组信息
    const createRiskGroupInfo = (mainInfo, extInfo, insuredInfo) => [{
        applyNum: mainInfo.unitCount || 1,
        productPackageType: extInfo.riskCode,
        insurantInfoList: [createInsuredObject(insuredInfo)]
    }];

    // 构建最终请求对象
    const reqObj = {
        isFee: "1", // 0:非见费 1:见费
        // pms出单账号
        pmsAccountCode: accountCode,
        // 交易流水
        transactionNo: "hz-" + proposal._id,
        // 支付信息
        prepaymentInfoList: [{
            // 返回地址
            returnUrl: RETURN_URL,
            // 支付结果前端通知地址
            frontNotifyUrl: FRONT_URL
        }],
        // 产品列表
        productInfoList: [{
            // 基础信息
            baseInfo: createBaseInfo(
                proposal.mainInfo,
                proposal.extInfo,
                proposal.renewalInd,
                proposal.migratedPolicyNo
            ),
            // 投保人
            applicantInfo: createApplicantInfo(proposal.appliInfo),
            // 扩展信息
            extendInfo: {},
            // 标的组
            riskGroupInfoList: createRiskGroupInfo(
                proposal.mainInfo,
                proposal.extInfo,
                proposal.insuredInfo
            )
        }]
    };

    return reqObj;
};

exports.proposalIn = resObj => {
    let wrappedResObj = {};
    if(resObj.ret != '0') {
        return { code: '999', msg: resObj.msg };
    }
    wrappedResObj = {
        code: resObj.ret == "0" ? "000" : "999",
        msg: resObj.msg,
        proposalNo: resObj.data.result.applyPolicyNo,
        forwardUrl: resObj.data.result.payUrl,
    };
    return wrappedResObj;
};


exports.policyOut = proposal => {
    let reqObj = {
        proposalNo: proposal.mainInfo.proposalNo,
        policyNo: proposal.mainInfo.policyNo
    }
    return reqObj;
};

exports.policyIn = resObj => {
    return resObj
};

exports.cancelOut = proposal => {
    const reqObj = {
        policyNo: proposal.mainInfo.policyNo,
        requestId: "hz" + new Date().getTime()
    };
    return reqObj;
};

exports.cancelIn = resObj => {
    let wrappedResObj = {};
    if (resObj.ret !== "0") {
        return { code: "999", msg: resObj.data };
    }
    wrappedResObj = {
        code: resObj.data.resultCode == "Y" ? "000" : "999",
        msg: resObj.data.resultMessage
    };
    return wrappedResObj;
};

exports.downloadPolicyOut = proposal => {
    const reqObj = {
        pmsAccountCode: accountCode,
        requestId: "hz" + new Date().getTime(),
        policyNo: proposal.mainInfo.policyNo,
        productClass: proposal.extInfo.tradeId, // 02-个财 03-个意
        name: proposal.appliInfo.name,
        certificateType: idTypeMap[proposal.appliInfo.idType],
        certificateNo: proposal.appliInfo.idNo
    };
    return reqObj;
};

exports.downloadPolicyIn = resObj => {
    let wrappedResObj = {};
    if (resObj.ret != "0") {
        return { code: "999", msg: resObj.msg };
    }
    wrappedResObj = {
        code: resObj.ret == "0" ? "000" : "999",
        msg: resObj.msg,
        evUrl: resObj.data.url,
        eData: new Buffer(resObj.data.result.returnPdfValue, "base64")
    };
    return wrappedResObj;
};

exports.downloadInvoiceOut = proposal => {
    const reqObj = {
         pmsAccountCode: accountCode,
        partnerCode: "P_SHHZ",
        invoiceList: [
            {
                transactionNo: "hz" + new Date().getTime(),
                invoiceCreater: proposal.appliInfo.name, // 开票人
                invoiceEmail: proposal.appliInfo.email, // 电子发票接收邮箱
                invoicePhone: proposal.appliInfo.mobile, // 电子发票接收联系电话
                remark: proposal.taxpayerInfo.remark || "", // 备注
                invoiceOwner: proposal.taxpayerInfo.owner, // 发票抬头
                taxPayerNO: proposal.taxpayerInfo.number || "", // 税号 如果发票抬头是个人的话不用填
                taxPayerTel: proposal.taxpayerInfo.tel || "", // 纳税人电话
                taxPayerAddress: proposal.taxpayerInfo.address || "", // 纳税人地址
                taxPayerBankName: proposal.taxpayerInfo.bankName || "", // 纳税人银行
                taxPayerBankAccount: proposal.taxpayerInfo.bankAccount || "", // 纳税人账户
                policyList: [
                    {
                        policyNo: proposal.mainInfo.policyNo,
                        personnelName: proposal.appliInfo.name,
                        certificateType: idTypeMap[proposal.appliInfo.idType],
                        certificateNo: proposal.appliInfo.idNo
                    }
                ]
            }
        ]
    };
    return reqObj;
};

exports.downloadInvoiceIn = resObj => {
    let wrappedResObj = {};
    if (global.env == "production") {
        if (resObj.ret !== "0") {
            return { code: "999", msg: resObj.msg };
        }
        const data = resObj.data[0];
        wrappedResObj = {
            code: data.resultCode == "999999" ? "000" : "999",
            msg: data.resultMessage,
            evUrl: data.url
        };
    } else {
        wrappedResObj = {
            code: "000",
            msg: "PACX fake",
            evUrl:
                "http://a.hiphotos.baidu.com/image/pic/item/10dfa9ec8a136327f216788d9d8fa0ec09fac791.jpg"
        };
    }
    return wrappedResObj;
};
