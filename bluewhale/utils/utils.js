
'use strict';
const co = require('co');
const _ = require('lodash');
const crypto = require('crypto');
const moment = require('moment');
const logger = require('@huibao/logger').logger();

const DEFAULT_CHARSET = 'utf8';

/**
 * 构造定时任务时间（node-cron style）
 * @param {*} string
 */
exports.cronTime = function (mdate) {

    if (!mdate) return;
    const year = mdate.get('year');
    const month = mdate.get('month'); // 0 to 11
    const date = mdate.get('date');
    const hour = mdate.get('hour');
    const minute = mdate.get('minute');
    const second = mdate.get('second');

    return `${second} ${minute} ${hour} ${date} ${month} ${year}`;
};

/**
 * 对特殊符号进行转义
 * @param {*} string
 */
exports.escapeRegExp = function (string) {
    // $&表示整个被匹配的字符串
    return string.replace(/([.*+?^=!:${}()（）|[\]\/\\])/g, '\\$&');
};


/**
 *  数据脱敏处理
 * @param {*} value 值
 * @param {*} style 预留参数
 */
exports.hideMiddle = function (num, type) {
    if (!num) return '';
    let length = num.length;
    switch (length) {
        case 11:
            // if (num.length < 11) return num;
            return num.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
        case 18:
            // if (num.length < 18) return num;
            return num.replace(/(\d{6})\d{8}(\d{4})/, '$1****$2');
    }
    return num;
}


/**
 * 银行卡脱敏 （截取后4位）
 * @param cardNo 卡号
 * @return 脱敏后的卡号
 */
exports.bankCard = function(cardNo) {
    if (typeof cardNo === 'string' && cardNo) {
        return cardNo.replace(/(\d{4})\d+(\d{4})/, '$1****$2');
    }
    return cardNo;
};

/**
 *  数据脱敏处理 (只保留后4位)
 * @param {*} value 值
 * @param {*} style 预留参数
 */
exports.hide = function (value, style) {
    if (!value) return '';
    return value.replace(/(\w)/g,
        // 匹配到的字符串、变量值、匹配到的字符串在原字符串中的位置（从0开始）、原字符串
        function (a, b, c, d) {
            // logger.info(a, b, c, d)
            return (c < (value.length - 4)) ? '*' : a;
        });
};


/**
 * excel中读入的日期处理
 * @param {*} value 值
 */
exports.handleDate = function (value) {
    if (typeof value === 'string') {
        value = value.replace(/\r\n|\n/g, '');
        value = value.trim().split(' ')[0].replace(/\D/g, '-');
        if (value.charAt(value.length - 1) == '-') {
            value = value.slice(0, -1);
        }
        return moment(new Date(value)).format('YYYY-MM-DD');
    } else if (typeof value === 'number') {
        return moment(new Date(1900, 0, value - 1)).format('YYYY-MM-DD');
    }
};

/**
 * 去除空格与换行
 * @param {*} value
 */
exports.replace = function (value) {
    if (!value) return '';
    if (typeof value === 'string') {
        // 去掉所有的换行符
        value = value.replace(/\r\n|\n|\u200E/g, '');
        return value.trim();
    }
    return value;

};


function formatDate(date) {
    return moment(date).format('YYYY-MM-DD HH:mm:ss');
}
exports.formatDate = formatDate;

/**
 * 车险计算订单过期时间
 * 1.当天0点起保，17点前支付
 * 2.明天之后起保，第二天17点前支付
 * @param {*} startDate
 */
function calPayOutTime(startDate) {
    const time = moment(startDate).add(-1, 'd').format('YYYYMMDD');
    const today = moment().format('YYYYMMDD');
    let payOutTime = '';
    if (time == today) {
        payOutTime = moment().set('h', 17).set('m', 0);
    } else {
        payOutTime = moment().add(1, 'd').set('h', 17)
            .set('m', 0);
    }
    logger.info('calPayOutTime: ' + formatDate(startDate) + ' before is ' + formatDate(payOutTime));

    return payOutTime;
}

exports.calPayOutTime = calPayOutTime;

/**
 * 根据身份证取 生日，性别，年龄
 * 同时支持15位和18位长度
 * @param {*} idNumber
 * @returns
 */
exports.getInfo = function(idNumber) {
    if(!idNumber) return {};
    idNumber = idNumber.toString();
    const length = idNumber.length;
    let birthDateStr, genderNum;

    if (length === 18) {
        birthDateStr = idNumber.substring(6, 14);
        genderNum = parseInt(idNumber.substring(16, 17), 10);
    } else if (length === 15) {
        birthDateStr = '19' + idNumber.substring(6, 12);
        genderNum = parseInt(idNumber.substring(14, 15), 10);
    } else {
        return { };
    }

    const birthDate = new Date(birthDateStr.substring(0, 4), birthDateStr.substring(4, 6) - 1, birthDateStr.substring(6, 8));
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    if (today.getMonth() < birthDate.getMonth() || (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate())) {
        age--;
    }

    const sex = genderNum % 2 ? '1' : '2';

    return { birth: moment(birthDate).format('YYYY-MM-DD'), sex, age };
}

// 去除特殊符号
exports.replaceAll = function (value) {
    if (!value) return '';
    if (typeof value === 'string') {
        // 去掉所有的特殊符号
        value = value.replace(/\r\n|\n|\u200E/g, '');
        value = value.replace(/[*!@$^&:"<>?/《》：；;“”‘'~`]/g, '');
        return value.trim();
    }
    return value;
};


exports.getAge = function (dateString) {
    const today = new Date();
    const birthDate = new Date(dateString);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }
    return age;
};


exports.isNumber = function (num) {
    if (typeof num === 'number') {
        return num - num === 0;
    }
    if (typeof num === 'string' && num.trim() !== '') {
        return Number.isFinite ? Number.isFinite(+num) : isFinite(+num);
    }
    return false;
};

// sha1
exports.sha1 = str => {
    logger.info('sha1 before=', str);
    const md5sum = crypto.createHash('sha1');
    str = md5sum.update(str, 'utf8').digest('hex');
    logger.info('sha1 after=', str);
    return str;
};

// md5
function md5(str) {
    logger.info('md5 before=', str);
    const md5sum = crypto.createHash('md5');
    str = md5sum.update(str, 'utf8').digest('hex');
    logger.info('md5 after=', str);
    return str;
}
exports.md5 = md5;

/**
* 取给定位数的随机整数 by zhbiao 20150514
* @len 位数
*/
exports.genRandom = function(len) {
    var chars = ['0','1','2','3','4','5','6','7','8','9'];
    var res = "";
    for(var i = 0; i < len ; i ++) {
        var id = Math.floor(Math.random()*10);
        res += chars[id];
    }
    return res;
}

 /**
 * 先用Object内置类的keys方法获取要排序对象的属性名，再利用Array原型上的sort方法对获取的属性名进行排序，newkey是一个数组
 * @param {Object} obj 待排序的值
 * @return {Object}
 */
exports.ksort = function(obj) {
    const newkey = Object.keys(obj).sort();

    const newObj = {}; // 创建一个新的对象，用于存放排好序的键值对
    for (let i = 0; i < newkey.length; i++) {
        newObj[newkey[i]] = obj[newkey[i]]; // 向新创建的对象中按照排好的顺序依次增加键值对
    }
    return newObj; // 返回排好序的新对象
}

/**
 * 更精确的SHA1PRNG密钥生成 (如果默认方法不兼容时使用)
 * @param {string} seed 种子字符串
 * @returns {Buffer} 16字节密钥
 */
function generateKeyWithSHA1PRNG(seed) {
    // 这是一个更接近Java SHA1PRNG的实现
    const seedBytes = Buffer.from(seed, 'utf8');
    let state = Buffer.alloc(20); // SHA1输出20字节

    // 初始化状态
    const sha1 = crypto.createHash('sha1');
    sha1.update(seedBytes);
    state = sha1.digest();

    // 生成16字节密钥
    const keyBytes = Buffer.alloc(16);
    let offset = 0;

    while (offset < 16) {
        const sha1Gen = crypto.createHash('sha1');
        sha1Gen.update(state);
        const generated = sha1Gen.digest();

        const copyLen = Math.min(16 - offset, generated.length);
        generated.copy(keyBytes, offset, 0, copyLen);
        offset += copyLen;

        // 更新状态
        state = generated;
    }

    return keyBytes;
}

/**
 * 加解密核心方法 - 完全兼容Java实现
 * @param {string} data 数据
 * @param {string} key 密钥
 * @param {boolean} encrypt true为加密，false为解密
 * @returns {string} 处理后的数据
 */
function doAESForOpen(data, key, encrypt) {
    try {
        if (data === null || data === undefined || !key) {
            return null;
        }

        let content;
        if (encrypt) {
            content = Buffer.from(data, DEFAULT_CHARSET);
        } else {
            content = Buffer.from(data, 'base64');
        }

        // 生成与Java相同的密钥
        const secretKey = generateKeyWithSHA1PRNG(key);

        if (encrypt) {
            // Java使用的是AES/ECB/PKCS5Padding
            const cipher = crypto.createCipheriv('aes-128-ecb', secretKey, null);
            let encrypted = cipher.update(content);
            encrypted = Buffer.concat([encrypted, cipher.final()]);
            return encrypted.toString('base64');
        } else {
            // 使用createDecipheriv而不是deprecated的createDecipher
            const decipher = crypto.createDecipheriv('aes-128-ecb', secretKey, null);
            let decrypted = decipher.update(content);
            decrypted = Buffer.concat([decrypted, decipher.final()]);
            return decrypted.toString(DEFAULT_CHARSET);
        }
    } catch (error) {
        console.error('AES密文处理异常:', error);
        return null;
    }
}

/**
 * 加密 (对应Java的encryptForOpen方法)
 * @param {string} data 需要加密的内容
 * @param {string} key 加密密码
 * @returns {string} 加密后的Base64字符串
 */
exports.encryptForOpen = function(data, key) {
    return doAESForOpen(data, key, true);
}

/**
 * 解密 (对应Java的decryptForOpen方法)
 * @param {string} data 待解密内容
 * @param {string} key 解密密钥
 * @returns {string} 解密后的明文
 */
exports.decryptForOpen = function(data, key) {
    return doAESForOpen(data, key, false);
}
