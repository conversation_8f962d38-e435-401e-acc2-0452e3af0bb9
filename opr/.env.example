# ==================================
# 环境变量配置模板
# 复制此文件为 .env 并填入实际值
# ==================================

# 应用基础配置
NODE_ENV=development
PORT=8000
LOGGER_LEVEL=info
BRANCH=SLBX

# MongoDB 数据库配置
DB_HOST=mongodb://your-mongodb-host:port/database
DB_NAME=oprDB
DB_USER=your-db-user
DB_PASS=your-db-password

# Redis 缓存配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=2

# Session 配置
SESSION_SECRET=your-session-secret-key-here
SESSION_KEY=oprsid

# OAuth 认证配置
OAUTH_CLIENT_ID=your-oauth-client-id
OAUTH_CLIENT_SECRET=your-oauth-client-secret
OAUTH_AUTHORIZATION_URL=https://um.hizom.cn/auth/authorize
OAUTH_TOKEN_URL=https://um.hizom.cn/auth/token
OAUTH_CALLBACK_URL=https://opr.hizom.cn/auth/callback

# 用户管理服务配置
UM_HOST=um.hizom.cn
UM_PORT=443
UM_LOGOUT_URL=https://um.hizom.cn/logout?redirect=https://opr.hizom.cn/
UM_BRANCH_URL=https://um.hizom.cn/auth/api/branchInfo
UM_PROV_URL=https://um.hizom.cn/auth/api/provInfo

# 外部服务 REST API 地址
ESB_REST_URL=https://esb.hizom.cn
PANDA_REST_URL=http://************:9100
APIS_REST_URL=https://esb.hizom.cn
BW_REST_URL=http://************:8866
UM_REST_URL=https://um.hizom.cn
PIGEON_REST_URL=http://************:8833
CAMEL_REST_URL=http://************:8834
RACOON_REST_URL=http://************:8836
TIGER_REST_URL=http://************:8800
UDESK_URL=https://huizhong.udesk.cn

# Web URL 配置
OPR_REST_URL=https://opr.hizom.cn
BW_WEB_URL=https://bw.hizom.cn
SPA_URL=https://spa.hizom.cn
ESB_URL=https://esb.99bx.cn
PUPP_URL=https://pupp.hizom.cn

# 客户端认证信息
CLIENT_SECRET=your-client-secret
CLIENT_ID=oprApp

UDESK_NAME=your-udesk-username
UDESK_PASS=your-udesk-password
