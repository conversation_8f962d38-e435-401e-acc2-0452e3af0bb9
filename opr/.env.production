# 开发环境配置文件
NODE_ENV=production

# 应用基础配置
PORT=8000
LOGGER_LEVEL=info
BRANCH=SLBX

# MongoDB 数据库配置
DB_HOST=mongodb://dds-bp16fd1e01cf3c841529.mongodb.rds.aliyuncs.com:3717,dds-bp16fd1e01cf3c842770.mongodb.rds.aliyuncs.com:3717/oprDB?replicaSet=mgset-1257429
DB_NAME=oprDB
DB_USER=oprnew
DB_PASS=hu1ba0opr

# Redis 缓存配置
REDIS_HOST=b8409cdcac014b03553.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_PASSWORD=b8409cdcac014b03:Redis1qaz12346
REDIS_DB=2

# Session 配置
SESSION_SECRET=20648005b69c4b15bdebe919f336dfbfc29453db
SESSION_KEY=oprsid

# OAuth 认证配置
OAUTH_AUTHORIZATION_URL=https://um.hizom.cn/auth/authorize
OAUTH_TOKEN_URL=https://um.hizom.cn/auth/token
OAUTH_CLIENT_ID=oprApp
OAUTH_CLIENT_SECRET=5733f75990a4d51f52d33332
OAUTH_CALLBACK_URL=https://opr.hizom.cn/auth/callback

# 用户管理服务配置
UM_HOST=um.hizom.cn
UM_PORT=443
UM_LOGOUT_URL=https://um.hizom.cn/logout?redirect=https://opr.hizom.cn/
UM_BRANCH_URL=https://um.hizom.cn/auth/api/branchInfo
UM_PROV_URL=https://um.hizom.cn/auth/api/provInfo

# 外部 REST API 配置
ESB_REST_URL=http://************:18003
PANDA_REST_URL=http://************:9100
APIS_REST_URL=https://esb.hizom.cn
BW_REST_URL=http://************:8866
UM_REST_URL=https://um.hizom.cn
PIGEON_REST_URL=http://************:8833
CAMEL_REST_URL=http://************:8834
RACOON_REST_URL=http://************:8836
TIGER_REST_URL=http://************:8800
UDESK_URL=https://huizhong.udesk.cn

# Web URL 配置
OPR_REST_URL=https://opr.hizom.cn
BW_WEB_URL=https://bw.hizom.cn
SPA_URL=https://spa.hizom.cn
ESB_URL=https://esb.99bx.cn
PUPP_URL=https://pupp.hizom.cn

# 客户端信息
CLIENT_SECRET=5733f75990a4d51f52d33332
CLIENT_ID=oprApp

# Udesk 客服系统配置
UDESK_NAME=<EMAIL>
UDESK_PASS=Hz123456
