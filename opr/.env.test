# 开发环境配置文件
NODE_ENV=test

# 应用基础配置
PORT=8000
LOGGER_LEVEL=debug
BRANCH=SLBX

# MongoDB 数据库配置
DB_HOST=************:27077
DB_NAME=huibaoDB
DB_USER=huibao
DB_PASS=FsdoX001$DT112lC

# Redis 缓存配置
REDIS_HOST=r-uf60bkkcit2uxrnxmp.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_PASSWORD=780fdbad2df545ae_Redisat2016
REDIS_DB=2

# Session 配置
SESSION_SECRET=20648005b69c4b15bdebe919f336dfbfc29453db
SESSION_KEY=oprsid

# OAuth 认证配置
OAUTH_AUTHORIZATION_URL=http://testum.99bx.cn/auth/authorize
OAUTH_TOKEN_URL=http://testum.99bx.cn/auth/token
OAUTH_CLIENT_ID=huibaoApp
OAUTH_CLIENT_SECRET=53d9e5ba5cc6abd836106f23
OAUTH_CALLBACK_URL=https://testopr.99bx.cn/auth/callback

# 用户管理服务配置
UM_HOST=testum.99bx.cn
UM_PORT=443
UM_LOGOUT_URL=https://testum.99bx.cn/logout?redirect=https://testopr.99bx.cn/
UM_BRANCH_URL=https://testum.99bx.cn/auth/api/branchInfo
UM_PROV_URL=https://testum.99bx.cn/auth/api/provInfo

# 外部 REST API 配置
ESB_REST_URL=http://************:18002
PANDA_REST_URL=https://testpanda.99bx.cn
APIS_REST_URL=https://testesb.99bx.cn
BW_REST_URL=https://testbw.99bx.cn
UM_REST_URL=https://testum.99bx.cn
PIGEON_REST_URL=http://**************:8833
CAMEL_REST_URL=https://testcamel.99bx.cn
RACOON_REST_URL=http://**************:8838
TIGER_REST_URL=https://testpay.99bx.cn
UDESK_URL=https://huizhong.udesk.cn

# Web URL 配置
OPR_REST_URL=https://testopr.99bx.cn
BW_WEB_URL=https://testbw.99bx.cn
SPA_URL=https://t.99bx.cn
ESB_URL=https://testesb.99bx.cn
PUPP_URL=https://testpupp.99bx.cn

# 客户端信息
CLIENT_SECRET=53d9e5ba5cc6abd836106f23
CLIENT_ID=oprApp

# Udesk 客服系统配置
UDESK_NAME=<EMAIL>
UDESK_PASS=A1234567
