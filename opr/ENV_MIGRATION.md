# 环境变量迁移指南

## 概述

本项目已完成从硬编码配置到环境变量管理的迁移，提高了应用的安全性和部署灵活性。本文档描述了迁移后的配置架构和使用方法。

## 🏗️ 配置架构

### 架构原理

采用**双轨制配置系统**：
- **Kraken.js 配置** - 负责框架和中间件配置
- **环境变量配置** - 负责敏感信息和业务逻辑配置

```
┌─────────────────┐    ┌─────────────────┐
│   Kraken.js     │    │   Environment   │
│   Config Files  │    │   Variables     │
├─────────────────┤    ├─────────────────┤
│ • 框架配置      │    │ • 敏感信息      │
│ • 中间件配置    │ ┌─▶│ • 数据库连接    │
│ • 静态配置      │ │  │ • API 密钥      │
│ • 引用外部模块  │ │  │ • 服务地址      │
└─────────────────┘ │  └─────────────────┘
         │          │           │
         │          │           │
         ▼          │           ▼
┌─────────────────────────────────────────┐
│           lib/spec.js                   │
│        配置整合和初始化                  │
└─────────────────────────────────────────┘
```

## 📁 文件结构

### 环境变量文件
```
.env.development     # 开发环境变量
.env.example        # 环境变量模板
```

### 配置文件
```
config/
├── config.json           # 主配置文件（框架配置）
├── development.json      # 开发环境覆盖配置
├── test.json            # 测试环境覆盖配置
└── lib/
    └── session-config.js # Session 中间件配置模块
```

### 核心模块
```
lib/
├── env-loader.js        # 环境变量加载器
└── spec.js             # 配置整合模块
```

## 🔧 核心组件

### 1. 环境变量加载器 (`lib/env-loader.js`)

负责加载和验证环境变量，为业务逻辑提供配置：

```javascript
// 加载环境变量文件
function loadEnvironment() {
    const envFiles = [
        `.env.${nodeEnv}.local`,
        `.env.${nodeEnv}`,
        '.env.local',
        '.env'
    ];
    // 按优先级加载...
}

// 生成业务配置
function getEnvConfig() {
    return {
        loggerLevel: process.env.LOGGER_LEVEL || 'info',
        databaseConfig: {
            host: process.env.DB_HOST,
            // ...
        }
        // ...
    };
}
```

### 2. Session 配置模块 (`lib/session-config.js`)

将敏感的中间件配置独立到外部 JS 模块：

```javascript
module.exports = function() {
    return [
        {
            secret: process.env.SESSION_SECRET,
            key: process.env.SESSION_KEY || 'oprsid',
            // ...
        },
        {
            host: process.env.REDIS_HOST,
            port: parseInt(process.env.REDIS_PORT, 10) || 6379,
            // ...
        }
    ];
};
```

### 3. 配置整合 (`lib/spec.js`)

整合所有配置并初始化各个服务：

```javascript
onconfig(config, next) {
    // 获取环境变量配置用于业务逻辑
    const envConfig = getEnvConfig();
    
    // 使用环境配置初始化各个服务
    logger.config(envConfig.loggerLevel);
    db.config(envConfig.databaseConfig);
    // ...
}
```

## 📋 环境变量清单

### 必需变量
```bash
# 应用基础配置
NODE_ENV=development
LOGGER_LEVEL=debug
BRANCH=SLBX

# 数据库配置
DB_HOST=your-database-host
DB_NAME=your-database-name
DB_USER=your-database-user
DB_PASS=your-database-password

# Redis 配置
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=2

# Session 配置
SESSION_SECRET=your-session-secret
SESSION_KEY=oprsid

# OAuth 认证配置
OAUTH_CLIENT_ID=your-oauth-client-id
OAUTH_CLIENT_SECRET=your-oauth-client-secret
OAUTH_AUTHORIZATION_URL=https://um.hizom.cn/auth/authorize
OAUTH_TOKEN_URL=https://um.hizom.cn/auth/token
OAUTH_CALLBACK_URL=https://opr.hizom.cn/auth/callback

# 用户管理服务配置
UM_HOST=um.hizom.cn
UM_PORT=443
UM_LOGOUT_URL=https://um.hizom.cn/logout?redirect=https://opr.hizom.cn/
```

### 外部服务配置
```bash
# REST API 服务
ESB_REST_URL=https://esb.hizom.cn
PANDA_REST_URL=http://************:9100
APIS_REST_URL=https://esb.hizom.cn
BW_REST_URL=http://************:8866
UM_REST_URL=https://um.hizom.cn
PIGEON_REST_URL=http://************:8833
CAMEL_REST_URL=http://************:8834
RACOON_REST_URL=http://************:8836
TIGER_REST_URL=http://************:8800
UDESK_URL=https://huizhong.udesk.cn

# Web URL 配置
OPR_REST_URL=https://opr.hizom.cn
BW_WEB_URL=https://bw.hizom.cn
SPA_URL=https://spa.hizom.cn
ESB_URL=https://esb.99bx.cn
PUPP_URL=https://pupp.hizom.cn

# 客户端认证
CLIENT_SECRET=your-client-secret
CLIENT_ID=oprApp
UDESK_NAME=your-udesk-username
UDESK_PASS=your-udesk-password
```

## 🚀 部署指南

### 1. 开发环境部署

```bash
# 1. 复制环境变量模板
cp .env.example .env.development

# 2. 编辑环境变量
vim .env.development

# 3. 启动应用
npm start
```

### 2. 生产环境部署

```bash
# 1. 设置环境变量（推荐使用容器编排工具）
export NODE_ENV=production
export DB_HOST=prod-database-host
export REDIS_HOST=prod-redis-host
# ... 其他环境变量

# 2. 启动应用
npm start
```

### 3. Docker 部署

```dockerfile
# Dockerfile 示例
FROM node:16-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .

# 环境变量通过 docker-compose 或 k8s 注入
ENV NODE_ENV=production

CMD ["npm", "start"]
```

```yaml
# docker-compose.yml 示例
version: '3.8'
services:
  app:
    build: .
    environment:
      - NODE_ENV=production
      - DB_HOST=database
      - REDIS_HOST=redis
      # ... 其他环境变量
    env_file:
      - .env.production
```

## 🔒 安全最佳实践

### 1. 环境变量管理
- ✅ **永远不要提交** `.env.*` 文件到版本控制
- ✅ **使用强随机密钥** 生成 SESSION_SECRET
- ✅ **定期轮换敏感密钥** 如数据库密码、API 密钥
- ✅ **最小权限原则** 数据库用户只授予必要权限

### 2. 生产环境安全
- ✅ **使用密钥管理服务** AWS Secrets Manager、Azure Key Vault 等
- ✅ **加密传输** 所有 API 通信使用 HTTPS
- ✅ **监控和审计** 记录配置变更和访问日志
- ✅ **环境隔离** 开发/测试/生产环境完全隔离

### 3. Git 配置
确保 `.gitignore` 包含：
```gitignore
# 环境变量文件
.env*
!.env.example

# 敏感配置
config/local.json
config/production.json
```

## 🔄 配置更新流程

### 1. 添加新的环境变量

```bash
# 1. 更新 .env.example
echo "NEW_CONFIG_VAR=default-value" >> .env.example

# 2. 更新 lib/env-loader.js
# 在 getEnvConfig() 中添加新配置

# 3. 更新文档
# 在此文档中记录新变量的用途
```

### 2. 添加新的中间件配置

```javascript
// 1. 创建配置模块 config/your-middleware-config.js
module.exports = function() {
    return {
        apiKey: process.env.YOUR_API_KEY,
        endpoint: process.env.YOUR_ENDPOINT
    };
};

// 2. 在 config.json 中引用
{
    "middleware": {
        "yourMiddleware": {
            "module": {
                "name": "your-middleware",
                "arguments": "path:./config/your-middleware-config"
            }
        }
    }
}
```

## 🐛 故障排除

### 常见问题

1. **应用启动失败：缺少环境变量**
   ```
   ❌ Missing required environment variables:
      - DB_HOST
      - REDIS_HOST
   ```
   解决：检查 `.env.development` 文件是否存在并包含所有必需变量

2. **数据库连接失败**
   检查数据库相关环境变量：
   ```bash
   echo $DB_HOST $DB_NAME $DB_USER
   ```

3. **Redis 连接失败**
   检查 Redis 相关配置：
   ```bash
   echo $REDIS_HOST $REDIS_PORT $REDIS_PASSWORD
   ```

4. **Session 中间件错误**
   确保 SESSION_SECRET 已设置：
   ```bash
   echo $SESSION_SECRET
   ```

### 调试技巧

1. **启用配置调试日志**
   ```bash
   DEBUG=config:* npm start
   ```

2. **验证环境变量加载**
   ```javascript
   // 在 lib/spec.js 中临时添加
   console.log('Loaded env vars:', Object.keys(process.env).filter(k => k.startsWith('DB_')));
   ```

3. **检查配置合并结果**
   查看应用启动日志中的配置信息

## 📝 版本历史

- **v2.0** - 采用外部 JS 模块架构，移除 config-processor.js
- **v1.1** - 引入 config-processor.js 处理环境变量引用
- **v1.0** - 初始环境变量迁移

## 🤝 贡献指南

1. **添加新配置** 时，请同时更新 `.env.example` 和本文档
2. **修改配置结构** 时，请确保向后兼容
3. **提交前检查** 确保没有敏感信息泄露

---

> 📚 更多信息请参考：
> - [Kraken.js 配置文档](https://krakenjs.com/docs)
> - [Node.js 环境变量最佳实践](https://nodejs.org/en/learn/command-line/how-to-read-environment-variables-from-nodejs)
> - [12-Factor App 配置原则](https://12factor.net/config)