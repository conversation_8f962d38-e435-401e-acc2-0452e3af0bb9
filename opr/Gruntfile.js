"use strict";

module.exports = function (grunt) {
    // 启用详细日志
    grunt.option('verbose', true);
    grunt.option('debug', true);
    grunt.option('stack', true);

    // 加载package.json信息，用于banner
    grunt.initConfig({
        pkg: grunt.file.readJSON("package.json")
    });

    // 尝试加载项目的grunt任务
    try {
        require('grunt-config-dir')(grunt, {
            configDir: require('path').resolve('tasks'),
        });
        console.log('Successfully loaded tasks from directory');
    } catch (e) {
        console.error('Error loading tasks from directory:', e);
    }

    // 备用任务加载方式
    grunt.registerTask('loadTasks', 'Load tasks from directory', function() {
        var path = require('path');
        var fs = require('fs');
        var tasksDir = path.resolve('tasks');

        try {
            fs.readdirSync(tasksDir).forEach(function(file) {
                if (file.endsWith('.js')) {
                    try {
                        var taskName = file.replace('.js', '');
                        var taskConfig = require('../tasks/' + file)(grunt);
                        grunt.config.set(taskName, taskConfig);
                        console.log('Loaded task:', taskName);
                    } catch (e) {
                        console.error('Error loading task ' + file + ':', e);
                    }
                }
            });
        } catch (e) {
            console.error('Error reading tasks directory:', e);
        }
    });


    // 开发/测试环境构建任务 - 基本优化
    grunt.registerTask("build", [
        "loadTasks",      // 加载任务
        "clean:build",    // 清理构建目录
        "clean:tmp",      // 清理临时目录
        "localizr",       // 国际化处理
        "less:build",     // 编译Less
        "copyto:build",   // 复制文件
        "uglify:build",   // 压缩JS
        "uglify:vendor",  // 压缩第三方JS
        "cssmin:build",   // 压缩CSS（单独文件）
        "cssmin:combine", // 合并核心CSS文件
        "filerev:assets", // 文件版本化
        "generateAssetMap", // 生成资产映射文件
        "processTemplateAssets", // 处理模板中的资源路径
        "dustjs:build",   // 编译Dust模板（使用处理后的模板）
        "usemin:html",    // 更新HTML引用（处理.build中的html文件）
        "usemin:css",     // 更新CSS引用
        "clean:tmp"       // 清理临时目录
    ]);

    // 测试任务
    grunt.registerTask("test", ["jshint", "mochacli"]);

    // 生产环境构建任务 - 完整优化
    grunt.registerTask("build:prod", [
        "loadTasks",      // 加载任务
        "clean:build",    // 清理构建目录
        "clean:tmp",      // 清理临时目录
        "localizr",       // 国际化处理
        "less:build",     // 编译Less
        "copyto:build",   // 复制文件
        "uglify:build",   // 压缩JS
        "uglify:vendor",  // 压缩第三方JS
        "cssmin:build",   // 压缩CSS（单独文件）
        "cssmin:combine", // 合并并压缩关键CSS
        "filerev:assets", // 文件版本化
        "generateAssetMap", // 生成资产映射文件
        "processTemplateAssets", // 处理模板中的资源路径
        "dustjs:build",   // 编译Dust模板（使用处理后的模板）
        "usemin:html",    // 更新HTML引用
        "usemin:css",     // 更新CSS引用
        "clean:tmp"       // 清理临时目录
    ]);

    // 快速构建任务 - 不压缩
    grunt.registerTask("build:fast", [
        "loadTasks",     // 加载任务
        "clean:build",   // 清理构建目录
        "less:build",    // 编译Less
        "copyto:build"   // 复制文件
    ]);

    // 开发监控任务
    grunt.registerTask("dev", ["build:fast", "watch"]);

    // 仅前端资源打包
    grunt.registerTask("frontend", ["build:prod"]);
};
