# 保险核心业务管理系统构建指南

## 构建系统概述

保险核心业务管理系统使用 Grunt 作为构建工具，提供了多种构建模式以满足不同的开发和部署需求。构建系统主要完成以下工作：

- 编译 Less 文件为 CSS
- 压缩和混淆 JavaScript 代码
- 压缩 CSS 和 HTML
- 优化图片资源
- 文件版本化和引用更新
- 国际化处理
- 打包和压缩

## 安装依赖

在开始构建之前，请确保已安装所有必要的依赖：

```bash
npm install --save-dev grunt-contrib-uglify grunt-contrib-cssmin grunt-filerev grunt-usemin grunt-contrib-concat grunt-contrib-htmlmin grunt-contrib-compress grunt-concurrent grunt-contrib-watch grunt-hash grunt-babel @babel/core @babel/preset-env grunt-postcss autoprefixer cssnano grunt-modernizr --legacy-peer-deps
```

## 构建命令

### 开发构建

```bash
grunt build
```

开发构建提供基本的优化，适合日常开发使用。它包括：
- Less 编译
- 基本的 JS/CSS 压缩
- 文件版本化
- 引用更新

### 生产构建

```bash
grunt build:prod
```

生产构建提供完整的优化，适合部署到生产环境。它包括：
- Less 编译和压缩
- CSS 后处理（添加浏览器前缀、优化）
- ES6 转 ES5
- 代码压缩和混淆
- 图片优化
- 文件版本化
- 引用更新
- 打包压缩

### 快速构建

```bash
grunt build:fast
```

快速构建跳过压缩和优化步骤，适合快速开发迭代。它包括：
- Less 编译
- 文件复制
- 国际化处理

### 开发监控

```bash
grunt dev
```

开发监控模式会监控文件变化并自动重新构建，适合开发过程中使用。

### 前端资源打包

```bash
grunt frontend
```

只打包前端资源，不包括服务端代码。

## 构建输出

构建结果将输出到 `.build` 目录，生产环境构建还会在 `dist` 目录生成压缩包：

- `.build/` - 构建输出目录
  - `css/` - 编译和压缩后的 CSS 文件
  - `js/` - 压缩和混淆后的 JS 文件
  - `images/` - 优化后的图片
  - `templates/` - 处理后的模板文件
  - `html/` - 压缩后的 HTML 文件

- `dist/` - 打包输出目录
  - `huibao-x.x.x.zip` - 完整应用打包
  - `frontend-x.x.x.zip` - 仅前端资源打包

## 自定义构建

如需自定义构建流程，可以修改以下文件：

- `Gruntfile.js` - 主构建配置
- `tasks/` 目录下的各个任务配置文件

## 常见问题

### 构建失败

如果构建失败，请检查以下几点：

1. 确保所有依赖已正确安装
2. 检查 Node.js 版本是否兼容（推荐使用 v12.x）
3. 检查错误日志，解决相关问题

### 文件未更新

如果修改的文件未在构建结果中更新，请尝试：

1. 清理构建目录：`grunt clean`
2. 重新执行完整构建：`grunt build`

### 性能问题

如果构建过程太慢，可以尝试：

1. 使用 `grunt build:fast` 进行快速构建
2. 使用 `grunt concurrent` 并行执行任务
3. 升级硬件或使用更快的磁盘
