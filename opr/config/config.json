{"port": 8000, "authConfig": {"permitApps": {"report": {"env": "production", "url": "https://report.hizom.cn", "name": "报表系统"}}}, "express": {"view cache": false, "view engine": "js", "views": "path:./.build/templates"}, "param": {"warningList": ["17602118819"], "baoelite": "shortterm"}, "view engines": {"js": {"module": "engine-munger", "renderer": {"method": "js", "arguments": [{"cache": true}, {"views": "config:express.views", "view engine": "config:express.view engine", "specialization": "config:specialization", "i18n": "config:i18n"}]}}}, "i18n": {"contentPath": "path:./locales", "fallback": "en-US"}, "middleware": {"static": {"module": {"arguments": ["path:./.build"]}}, "router": {"module": {"arguments": [{"directory": "path:./controllers"}]}}, "expressView": {"priority": 100, "enabled": true, "module": {"name": "engine-munger/lib/expressView"}}, "json": {"enabled": true, "priority": 70, "module": {"name": "body-parser", "method": "json", "arguments": [{"extended": true, "limit": 5242880}]}}, "urlencoded": {"enabled": true, "priority": 70, "module": {"name": "body-parser", "method": "u<PERSON><PERSON><PERSON>", "arguments": [{"extended": true, "limit": 5242880}]}}, "fileNotFound": {"enabled": true, "priority": 130, "module": {"name": "kraken-js/middleware/404", "arguments": ["errors/404"]}}, "serverError": {"enabled": true, "priority": 140, "module": {"name": "path:./lib/middlewear/500", "arguments": ["errors/500"]}}, "logger": {"enabled": true, "priority": 50, "module": {"name": "path:./lib/middlewear/accessLogger", "arguments": ["combined"]}}, "session": {"enabled": true, "priority": 100, "module": {"name": "path:./lib/middlewear/redis-session"}}, "appsec": {"enabled": true, "priority": 110, "module": {"name": "lusca", "arguments": [{"xframe": "SAMEORIGIN", "p3p": false, "csp": {"policy": {"default-src": "'self'", "style-src": "'self' 'unsafe-inline' fonts.googleapis.com", "script-src": "'self' 'unsafe-inline'", "img-src": "'self' data: https:", "font-src": "'self' fonts.gstatic.com", "object-src": "'none'"}}}]}}, "csrf": {"enabled": true, "priority": 111, "route": "/((?!tpi|weixin|api|level|data|agent))*", "module": {"name": "lusca", "method": "csrf", "arguments": [{"angular": true, "cookie": {"httpOnly": true, "secure": true, "sameSite": "strict"}}]}}, "flash": {"priority": 91, "enabled": true, "module": {"name": "connect-flash", "method": "flash"}}}}