'use strict';
const co = require('co');
const _ = require('lodash');
const cors = require('cors');
const moment = require('moment');
const rp = require('request-promise');
const auth = require('../../../lib/auth');
const hideTools = require('../../../lib/utils');
const logger = require('@huibao/logger').logger();
const { getEnvConfig } = require('../../../lib/env-loader');


module.exports = function(app) {

    // 允许跨域请求访问
    app.options('*', cors());

    function getRacoonUrl() {
        let envConfig = getEnvConfig();
        return envConfig.restUrl.racoonRestUrl;
    }

    // 我的团队
    app.post('/myTeam', cors(), auth.isAuthenticated(), function(req, res) {
        logger.info('myTeam:' + JSON.stringify(req.body));
        req.body.branch = req.user.oprBranches;
        let racoonUrl = getRacoonUrl();
        console.log('myTeam:', racoonUrl);
        co(function* () {
            const repos = yield rp({
                method: 'POST',
                uri: `${racoonUrl}/panda/myTeam`,
                body: req.body,
                json: true,
            });
            logger.info(repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });

    app.get('/getAgentInfo', cors(), auth.isAuthenticated(), function(req, res) {

        logger.info('getAgentInfo:' + JSON.stringify(req.query));
        let racoonUrl = getRacoonUrl();
        co(function* () {
            const repos = yield rp({
                uri: `${racoonUrl}/opr/getAgentInfo`,
                qs: req.query,
                json: true,
            });
            // logger.info('repos:    ' + JSON.stringify(repos));
            hideData(req, repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });

    app.get('/pointDetail', cors(), auth.isAuthenticated(), function(req, res) {
        const obj = req.query;
        logger.info('pointDetail:', JSON.stringify(obj));
        let racoonUrl = getRacoonUrl();
        co(function* () {
            const repos = yield rp({
                uri: `${racoonUrl}/opr/pointDetail`,
                qs: obj,
                json: true,
            });
            // logger.info('repos: ' + JSON.stringify(repos));
            hideData(req, repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });

    app.get('/expDetail', cors(), auth.isAuthenticated(), function(req, res) {
        logger.info('expDetail:' + JSON.stringify(req.query));
        let racoonUrl = getRacoonUrl();
        co(function* () {
            const repos = yield rp({
                uri: `${racoonUrl}/opr/expDetail`,
                qs: req.query,
                json: true,
            });
            hideData(req, repos);
            logger.info(repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });

    app.get('/levelDetail', cors(), auth.isAuthenticated(), function(req, res) {
        logger.info('levelDetail:' + JSON.stringify(req.query));
        let racoonUrl = getRacoonUrl();
        co(function* () {
            const repos = yield rp({
                uri: `${racoonUrl}/opr/levelDetail`,
                qs: req.query,
                json: true,
            });
            hideData(req, repos);
            logger.info(repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });

    app.post('/factorList', cors(), auth.isAuthenticated(), function(req, res) {
        logger.info('factorList:' + JSON.stringify(req.body));
        let racoonUrl = getRacoonUrl();
        co(function* () {
            const repos = yield rp({
                method: 'POST',
                uri: `${racoonUrl}/opr/factorList`,
                body: req.body,
                json: true,
            });
            logger.info(repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });

    app.post('/addFactor', cors(), auth.isAuthenticated(), function(req, res) {
        logger.info('addFactor:' + JSON.stringify(req.body));
        let racoonUrl = getRacoonUrl();
        co(function* () {
            const repos = yield rp({
                method: 'POST',
                uri: `${racoonUrl}/opr/addFactor`,
                body: req.body,
                json: true,
            });
            logger.info(repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });

    app.get('/removeFactor', cors(), auth.isAuthenticated(), function(req, res) {
        logger.info('removeFactor:' + JSON.stringify(req.query));
        let racoonUrl = getRacoonUrl();
        co(function* () {
            const repos = yield rp({
                uri: `${racoonUrl}/opr/removeFactor`,
                qs: req.query,
                json: true,
            });
            logger.info(repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });

    app.get('/findFactorByTime', cors(), auth.isAuthenticated(), function(req, res) {
        logger.info('findFactorByTime:' + JSON.stringify(req.query));
        let racoonUrl = getRacoonUrl();
        co(function* () {
            const repos = yield rp({
                uri: `${racoonUrl}/opr/findFactorByTime`,
                qs: req.query,
                json: true,
            });
            logger.info(repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });

    app.get('/getLevels', cors(), auth.isAuthenticated(), function(req, res) {
        logger.info('getLevels:' + JSON.stringify(req.query));
        let racoonUrl = getRacoonUrl();
        co(function* () {
            const repos = yield rp({
                uri: `${racoonUrl}/opr/getLevels`,
                qs: req.query,
                json: true,
            });
            logger.info(repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });

    app.post('/updateMaxEXP', cors(), auth.isAuthenticated(), function(req, res) {
        logger.info('updateMaxEXP:' + JSON.stringify(req.body));
        let racoonUrl = getRacoonUrl();
        co(function* () {
            const repos = yield rp({
                method: 'POST',
                uri: `${racoonUrl}/opr/updateMaxEXP`,
                body: req.body,
                json: true,
            });
            logger.info(repos);
            res.json(repos);
        }).catch(err => {
            logger.info(err);
            res.json({ code: '999', err });
        });
    });


};

function hideData(req, repos) {
    repos.data.forEach(e => {
        if (req.user.roles.indexOf(global.USER_INFO_ROLE_LIST) < 0) {
            e.salemanRename = hideTools.hide(e.saleman);
            e.agentId.telephone = hideTools.hide(e.agentId.telephone);
            e.agentId.saleman = hideTools.hide(e.agentId.saleman);
            e.agentId.idNo = hideTools.hide(e.agentId.idNo);
            if (e.agentId.recommName && e.agentId.recommName.length === 11) {
                e.agentId.recommName = hideTools.hide(e.agentId.recommName);
            }
            if (e.agentId.agentName && e.agentId.agentName.length === 11) {
                e.agentId.agentName = hideTools.hide(e.agentId.agentName);
            }
        } else {
            e.salemanRename = e.saleman;
        }
    });
}
