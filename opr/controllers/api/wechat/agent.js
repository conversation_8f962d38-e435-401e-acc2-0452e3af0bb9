'use strict';

const cors = require('cors');
const _ = require('lodash');
const moment = require('moment');
const auth = require('../../../lib/auth');
const logger = require('@huibao/logger').logger();
const AgentBroker = require('../../../models/agent/AgentBroker');
const MiniAgentInfo = require('../../../models/wechat/MiniAgentInfo');
const weixinService = require('../../../services/weixinService');
const restful = require('../../../controllers/util/restful');

/**
 * 业务员对C端产品的费率配置
 * @param {*} router
 */
module.exports = function (router) {

    router.options('*', cors());

    // 初始化配置
    router.post('/init/config', cors(), auth.isAuthenticatedByToken(), async (req, res) => {
        try {
            let { userCode, agentId } = req.user;

            // 查询是否存在记录
            let agentInfo = await MiniAgentInfo.findOne({ userCode });
            if(!agentInfo) {
                // 首次创建
                agentInfo = new MiniAgentInfo({ userCode, agentId });
                await agentInfo.save();
            }
            const res_obj = _.pick(agentInfo, ['promotionLevel', 'isRateConfig', 'userCode']);
            res.json({ code: '000', data: res_obj });
        } catch (error) {
            console.error(error);
            res.json({ code: '999', msg: error.message });
        }
    });

    // 生成邀请推广二维码
    router.post('/invite/qrcode', cors(), auth.isAuthenticatedByToken(), async (req, res) => {
        try {
            let { userCode } = req.user;
            // 查询
            let agentInfo = await MiniAgentInfo.findOne({ userCode });
            if(!agentInfo) throw new Error('未找到用户信息');

            let agent = await AgentBroker.findOne({ userCode });
            if(agent.agentStatus != '2') {
                throw new Error('请先完成业务员签约后，再获取二维码');
            }

            // 无邀请二维码时，生成
            if(!agentInfo.qrcodeUrl) {
                let result = await weixinService.generateInviteQrcode(userCode, agentInfo.promotionCode);
                agentInfo.qrcodeUrl = result.qrcodeUrl;
                agentInfo.promotionCode = result.promotionCode;
                await agentInfo.save();
            }
            const res_obj = _.pick(agentInfo, ['promotionLevel', 'isRateConfig', 'userCode', 'qrcodeUrl', 'promotionCode']);
            res.json({ code: '000', data: res_obj });
        } catch (error) {
            console.error(error);
            res.json({ code: '999', msg: error.message });
        }
    });


    // 推广订单
    router.get('/order/list', cors(), auth.isAuthenticatedByToken(), async (req, res) => {
        try {
            let { userCode } = req.user;
            let obj = req.query;
            let reqObj = {
                userCode,
                page: obj.currentPage,
                limit: obj.pageSize,
                productType: obj.tag,
                createdAt: obj.searchDate
            }
            console.log('c_order_list:', JSON.stringify(obj), JSON.stringify(reqObj));
            let result = await restful.restClient.shortInsureOrderListAsync(reqObj);
            console.log('c_order_result', JSON.stringify(result));
            const { list = [], total = 0, totalPremium = 0, count = 0 } = result.data;

            const formatOrderList = (orders) => {
                return orders.map(item => ({
                policyNo: item.policyNo,
                appliName: item.appliName,
                amount: item.premium,
                productName: item.productName,
                applyDate: moment(item.inputDate).format('YYYY-MM-DD HH:mm:ss'),
                createdAt: moment(item.createdAt).format('YYYY-MM-DD HH:mm:ss'),
                status: item.isValid
                }));
            };
            res.json({ code: '000', data: {
                list: formatOrderList(list),
                quantity: count,
                totalAmount: totalPremium,
                total
            }});
        } catch (error) {
            console.error(error);
            res.json({ code: '999', msg: error.message });
        }
    });

    // 邀请列表
    router.get('/invite/list', cors(), auth.isAuthenticatedByToken(), async (req, res) => {
        try {
            let { userCode } = req.user;
            let obj = req.query;
            let reqObj = {
                userCode,
                page: obj.currentPage,
                limit: obj.pageSize,
                sortType: obj.sortType,
                name: obj.name
            }
            console.log('c_invite_list:', JSON.stringify(obj), JSON.stringify(reqObj));
            let result = await restful.restClient.shortInsureInviteListAsync(reqObj);
            console.log('c_invite_result', JSON.stringify(result));
            let { list, total, lv1, lv2 } = result.data;
            const formatInviteList = (invites) => {
                return invites.map(item => ({
                    name: item.nickName,
                    joinDate: moment(item.createdAt).format('YYYY-MM-DD'),
                    inviteCount: item.count
                }));
            };
            res.json({ code: '000', data: {
                list: formatInviteList(list),
                invite1Count: lv1 || 0,
                invite2Count: lv2 || 0 ,
                total
            }});
        } catch (error) {
            console.error(error);
            res.json({ code: '999', msg: error.message });
        }
    });

};
