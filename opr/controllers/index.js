'use strict';
const co = require('co');
const cors = require('cors');
const _ = require('lodash');
const path = require('path');
const moment = require('moment');
const Promise = require('bluebird');
const auth = require('../lib/auth');
const cache = require('../lib/cacheHelper');
const umClient = require('../lib/umClient');
const logger = require('@huibao/logger').logger();
const Agenda = require("../lib/agenda");
const Agendash = require("agendash");
const commonService = require('../services/commonService');

Promise.config({
    // Enables all warnings except forgotten return statements.
    warnings: {
        wForgottenReturn: false,
    },
});

module.exports = function(app) {

    app.options('*', cors());

    app.head('/health/check/status', (req, res, next) => {
        res.json('ha ha OK');
    });

    app.get('/', auth.isAuthenticated(), function(req, res) {
        logger.info('token:' + JSON.stringify(req.user.token));
        const model = { title: '金融保险服务云平台' };
        let branchName = '汇中经纪';
        co(function* () {
            try {
                if (res.locals.logedInUser.title == 'ALL') {
                    res.locals.branchName = branchName;
                    res.render('index', model);
                } else {
                    branchName = yield commonService.getBranchName({ code: res.locals.logedInUser.title, token: req.user.jwtToken });
                    res.locals.branchName = branchName;
                    res.render('index', model);
                }
            } catch (error) {
                logger.error('Error rendering index page:', error);
                res.status(500).send('Internal Server Error');
            }
        });
    });

    app.get('/login', auth.authenticate());

    app.get('/logout', auth.isAuthenticated('ROLE_USER'), function(req, res) {
        let user = req.user;
        req.logout();
        req.session.roleMenuTree = null;
        if(user.accessToken) {
            cache.del('token', user.accessToken, function (err, result) { console.log('del_token:', err, result) })
        }
        res.redirect(umClient.getLogoutUrl());
    });

    app.get('/theme/:theme', auth.isAuthenticatedByToken(), function(req, res) {
        req.session.theme = req.params.theme;
        res.redirect('/');
    });

    app.get('/download/:id', auth.isAuthenticated(), function(req, res, next) {
        const id = req.params.id;
        logger.info('download_id:' + id);
        if (id == '2') {
            var targetFile = path.join(global.ROOT_PATH + '/public/mb/', 'carRate.xlsx');
            res.download(targetFile, 'car_rate.xlsx');
        } else if (id == '3') {
            var targetFile = path.join(global.ROOT_PATH + '/public/mb/', 'relations.xlsx');
            res.download(targetFile, 'relations.xlsx');
        } else if (id == '4') {
            var targetFile = path.join(global.ROOT_PATH + '/public/mb/', 'non_auto_list.xlsx');
            res.download(targetFile, 'non_auto_list.xlsx');
        } else if (id == '5') {
            var targetFile = path.join(global.ROOT_PATH + '/public/mb/', 'policy_list_nodocking.xlsx');
            res.download(targetFile, 'policy_list_nodocking.xlsx');
        } else if (id == '6') {
            res.download(global.ROOT_PATH + '/public/mb/account_agencyfee_bxtemplate.xlsx', 'account_agencyfee_bxtemplate.xlsx');
        } else if (id == '9') {
            res.download(global.ROOT_PATH + '/public/mb/broker_handleSeq.xlsx', 'broker_handleSeq.xlsx');
        } else if (id == '10') {
            res.download(global.ROOT_PATH + '/public/mb/account_handleSeq.xlsx', 'account_handleSeq.xlsx');
        } else if (id == '11') {
            res.download(global.ROOT_PATH + '/public/mb/licensedNo.xlsx', 'licensedNo.xlsx');
        } else if (id == '12') {
            res.download(global.ROOT_PATH + '/public/mb/qualificationNo.xlsx', 'qualificationNo.xlsx');
        } else if (id == '13') {
            res.download(global.ROOT_PATH + '/public/mb/batch_policy_car_template.xlsx', 'batch_policy_car_template.xlsx');
        } else if (id == '14') {
            res.download(global.ROOT_PATH + '/public/mb/batch_policy_prop_template.xlsx', 'batch_policy_prop_template.xlsx');
        } else if (id == '15') {
            res.download(global.ROOT_PATH + '/public/mb/batch_policy_mhealth_template.xlsx', 'batch_policy_mhealth_template.xlsx');
        } else if (id == '16') {
            res.download(global.ROOT_PATH + '/public/mb/payout_template.xlsx', 'payout_template.xlsx');
        } else if (id == '17') {
            res.download(global.ROOT_PATH + '/public/mb/lifeAgencyRate.xlsx', 'life_agency_rate.xlsx');
        } else if (id == '18') {
            res.download(global.ROOT_PATH + '/public/mb/visitFail.xlsx', 'visit_fail.xlsx');
        } else if (id == '18bs') {
            res.download(global.ROOT_PATH + '/public/mb/bs_accept_visit_date.xlsx', 'bs_accept_visit_date.xlsx');
        } else if (id == '19') {
            res.download(global.ROOT_PATH + '/public/mb/agent_profit_add.xlsx', 'agent_profit_add.xlsx');
        } else if (id == '20') {
            res.download(global.ROOT_PATH + '/public/mb/renewal_batch_module.xlsx', 'renewal_batch_module.xlsx');
        } else if (id == '21') {
            res.download(global.ROOT_PATH + '/public/mb/agent_updown_batch.xlsx', 'agent_updown_batch.xlsx');
        } else if (id == '22') {
            res.download(global.ROOT_PATH + '/public/mb/renewal_batch_import.xlsx', 'renewal_batch_import.xlsx');
        } else if (id == '23') {
            res.download(global.ROOT_PATH + '/public/mb/other_settle_batch.xlsx', 'other_settle_batch.xlsx');
        } else if (id == '24') {
            res.download(global.ROOT_PATH + '/public/mb/renewal_deduct.xlsx', 'renewal_deduct.xlsx');
        } else if (id == '25') {
            res.download(global.ROOT_PATH + '/public/mb/endor_batch_import.xlsx', 'endor_batch_import.xlsx');
        } else if (id == '26') {
            res.download(global.ROOT_PATH + '/public/mb/lifeCommissionRate.xlsx', 'life_commission_rate.xlsx');
        } else if (id == '27') {
            res.download(global.ROOT_PATH + '/public/mb/proMapList.xlsx', 'proMapList.xlsx');
        } else if (id == '28') {
            res.download(global.ROOT_PATH + '/public/mb/agentMessagePush.xlsx', 'agentMessagePush.xlsx');
        } else if (id == '29') {
            res.download(global.ROOT_PATH + '/public/mb/no_car_rate.xlsx', 'noCarRate.xlsx');
        } else if (id == '30') {
            res.download(global.ROOT_PATH + '/public/mb/batch_policy_group_insurances_template.xlsx', 'batch_policy_group_insurances_template.xlsx');
        } else if (id == '31') {
            res.download(global.ROOT_PATH + '/public/mb/channel_commission_rate.xlsx', 'channelCommissionRate.xlsx');
        } else if (id == '40') {
            res.download(global.ROOT_PATH + '/public/mb/channel_commission_import.xlsx', 'channel_commission_import.xlsx');
        } else if (id == '41') {
            res.download(global.ROOT_PATH + '/public/mb/lifeAgencyRate1.xlsx', 'life_agency_rate1.xlsx');
        }else if (id == '42') {
            res.download(global.ROOT_PATH + '/public/mb/batch_life_import.xlsx', 'batch_life_import.xlsx');
        }else if (id == '43') {
            res.download(global.ROOT_PATH + '/public/mb/channel_commission_import.xlsx', '渠道结算导入模板.xlsx');
        }else if (id == '44') {
            res.download(global.ROOT_PATH + '/public/mb/deliver_import.xlsx', 'deliver_import.xlsx');
        }else if (id == '45') {
            res.download(global.ROOT_PATH + '/public/mb/accendance_import.xlsx', 'accendance_import.xlsx');
        }else if (id == '46') {
            res.download(global.ROOT_PATH + '/public/mb/settle_summary_import.xlsx', 'settle_summary_import.xlsx');
        }else if (id == '47') {
            res.download(global.ROOT_PATH + '/public/mb/register_add_import.xlsx', 'register_add_import.xlsx');
        }else if (id == '48') {
            res.download(global.ROOT_PATH + '/public/mb/maintenance_import.xlsx', 'maintenance_import.xlsx');
        }else if (id == '49') {
            res.download(global.ROOT_PATH + '/public/mb/settle_summaryApply_import.xlsx', 'settle_summaryApply_import.xlsx');
        }else if (id == '50') {
            res.download(global.ROOT_PATH + '/public/mb/import_train_record.xlsx', 'import_train_record.xlsx');
        }else if (id == '51') {
            res.download(global.ROOT_PATH + '/public/mb/import_leave_job.xlsx', 'import_leave_job.xlsx');
        }
    });

    // agenda ui
    app.use('/dash', cors(), auth.isAuthenticated(), Agendash(Agenda.getAganda()));
};
