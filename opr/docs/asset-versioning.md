# 静态资源版本化指南

## 概述

静态资源版本化是一种通过在文件名中添加哈希值来实现浏览器缓存控制的技术。当文件内容发生变化时，文件名也会随之变化，从而确保浏览器加载最新版本的资源。

本项目使用 Grunt 的 filerev 和 usemin 任务来实现静态资源版本化，并提供了一个资源助手来在模板中引用这些版本化的资源。

## 工作原理

1. **构建过程**：
   - `grunt-contrib-uglify` 压缩 JavaScript 文件
   - `grunt-contrib-cssmin` 压缩 CSS 文件
   - `grunt-filerev` 为文件名添加哈希值
   - `grunt-usemin` 更新 HTML 和 CSS 中的引用

2. **资源映射**：
   - 构建过程会生成一个 `assets.json` 文件，包含原始文件路径到版本化文件路径的映射

3. **资源助手**：
   - 提供了一个 Dust.js 助手 `{@asset path="/path/to/file.min.js"/}`
   - 助手会查找资源映射，返回正确的版本化路径
   - 支持文件名冲突处理（如多个同名 app.js 文件）
   - 自动回退机制：如果 .min 版本不存在，自动查找非 .min 版本
   - 支持基于文件系统的直接查找，包括带 hash 的文件名匹配

## 使用方法

### 在模板中引用静态资源

```html
<!-- JavaScript 文件 -->
<script src="{@asset path="/js/app.min.js"/}"></script>

<!-- CSS 文件 -->
<link rel="stylesheet" href="{@asset path="/css/app.min.css"/}">

<!-- HTML 目录下的 JS 文件 -->
<script src="{@asset path="/html/quotation/js/router.min.js"/}"></script>
```

### 运行构建

```bash
# 开发环境构建
grunt build

# 生产环境构建
grunt build:prod
```

## 注意事项

1. **文件命名**：
   - 确保引用的是 `.min.js` 或 `.min.css` 文件
   - 原始文件应该是 `.js` 或 `.css`

2. **目录结构**：
   - JS 文件应放在 `public/js` 或 `public/html` 目录下
   - CSS 文件应放在 `public/css` 目录下

3. **模板更新**：
   - 使用 `node scripts/update-templates.js` 批量更新模板中的资源引用

4. **调试**：
   - 在开发环境中，如果没有运行构建过程，将使用原始文件路径
   - 可以通过检查 `.build/assets.json` 文件来验证资源映射

## 故障排除

### 资源助手返回空路径

**问题**: `{@asset path="..."/}` 返回空字符串，导致 script 或 link 标签没有 src/href 属性。

**解决方案**:
1. 检查是否已运行构建命令：`grunt build`
2. 验证 asset helper 是否正确注册到 Dust 模板引擎
3. 检查 `.build/assets.json` 文件是否存在
4. 确认请求的文件路径在构建输出中存在

### 找不到模块文件

**问题**: 浏览器控制台显示 404 错误，无法加载模块文件。

**解决方案**:
1. 检查 asset helper 是否正确处理文件名冲突
2. 验证构建过程是否正确生成了 hash 文件名
3. 检查静态文件服务器配置是否正确
4. 确认文件路径大小写匹配

### 资源未版本化

如果资源未被正确版本化，请检查：

1. 文件是否被正确复制到 `.build` 目录
2. 文件是否匹配 filerev 任务中的 `src` 模式
3. 是否运行了完整的构建过程

### 模板中的引用未更新

如果模板中的引用未被更新，请检查：

1. 是否正确使用了资源助手
2. 资源助手是否被正确注册
3. 资源映射文件是否存在并包含正确的映射关系

### 浏览器仍加载旧版本

如果浏览器仍然加载旧版本的资源，请尝试：

1. 清除浏览器缓存
2. 检查服务器缓存配置
3. 验证资源的 URL 是否正确包含哈希值
