# 更新日志

本文档记录了保险核心业务管理系统构建工具的所有重要变更。

## [1.0.1] - 2025-07-25

### 修复

- **Asset Helper**: 修复了 `{@asset path="..."/}` 标签生成空路径的问题
  - 正确注册 asset helper 到 Dust 模板引擎
  - 实现文件名冲突处理逻辑（如多个 app.js 文件）
  - 添加自动回退机制：.min 文件不存在时自动查找非 .min 版本
  - 支持基于文件系统的直接查找，包括带 hash 的文件名匹配

- **AngularJS 模块加载**: 修复了 `homeApp` 模块无法实例化的问题
  - 调整脚本加载顺序，确保公用模块在 home 模块之前加载
  - 修复 `systemServices`, `systemConstants`, `commonDirectives`, `commonFilters` 模块依赖问题

- **JavaScript 依赖**: 修复了 `cooperationControllers is not defined` 错误
  - 解决 `inputcontrollers.js` 中的循环依赖问题
  - 将 `CoopInputDetailCtrl` 控制器从 `cooperationControllers` 模块移动到正确的 `inputCooperationControllers` 模块

### 改进

- **文档**: 更新故障排除指南，添加新修复问题的解决方案
- **文档**: 更新资源版本化指南，完善 asset helper 说明

## [1.0.0] - 2025-07-18

### 新增

- 完整的 Grunt 构建系统
  - 基本构建任务 (`build`)
  - 生产环境构建任务 (`build:prod`)
  - 快速构建任务 (`build:fast`)
  - 开发监控任务 (`dev`)
  - 前端资源打包任务 (`frontend`)
- JavaScript 优化
  - 代码压缩和混淆 (uglify)
  - 代码合并 (concat)
  - ES6 转 ES5 (babel)
- CSS 优化
  - Less 编译
  - CSS 压缩 (cssmin)
  - CSS 后处理 (postcss)
- 资源优化
  - 图片优化 (imagemin)
  - HTML 压缩 (htmlmin)
  - 文件版本化 (filerev)
  - 引用更新 (usemin)
- 构建效率
  - 并行任务执行 (concurrent)
  - 文件监控 (watch)
- 现代化特性
  - 浏览器特性检测 (modernizr)
  - 响应式支持
- 文档
  - 构建指南
  - 安全指南
  - 优化指南
  - 任务参考
  - 故障排除指南

### 变更

- 更新构建流程，提高效率和可维护性
- 优化资源加载策略
- 增强代码安全性

### 修复

- 解决构建过程中的路径问题
- 修复文件编码问题
- 解决依赖冲突

## 版本号说明

版本号遵循 [语义化版本 2.0.0](https://semver.org/lang/zh-CN/) 规范。

格式：主版本号.次版本号.修订号

- 主版本号：当做了不兼容的 API 修改
- 次版本号：当做了向下兼容的功能性新增
- 修订号：当做了向下兼容的问题修正
