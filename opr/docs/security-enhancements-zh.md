# 安全增强文档

本文档概述了为提高 Hizom 应用程序安全性而实施的安全增强措施，以防范常见的 Web 漏洞。

## 已实施的安全措施

### 1. Helmet.js 集成

已集成 Helmet.js 以设置各种 HTTP 头，增强安全性：

- **内容安全策略 (CSP)**：控制浏览器可以加载的资源
- **X-Frame-Options**：防止点击劫持攻击
- **X-XSS-Protection**：启用浏览器的 XSS 过滤功能
- **X-Content-Type-Options**：防止 MIME 类型嗅探
- **严格传输安全 (HSTS)**：强制使用 HTTPS 连接
- **DNS 预取控制**：控制 DNS 预取
- **引用者策略**：控制包含的引用者信息量

### 2. 速率限制

已实现 Express-rate-limit 以防止暴力破解和 DDoS 攻击：

- 限制单个 IP 地址每 15 分钟 100 个请求
- 当超过限制时提供可定制的错误消息
- 帮助防止对登录和注册端点的滥用

### 3. CSRF 防护

增强了跨站请求伪造防护：

- **CSURF 中间件**：为表单生成和验证 CSRF 令牌
- **自动令牌生成**：自动为响应本地变量生成令牌
- **令牌验证**：在 POST/PUT/DELETE 请求上验证令牌
- **安全 Cookie 设置**：令牌存储在 HTTP-only、安全、同站点 Cookie 中

### 4. 输入验证和净化

增强了输入验证和净化机制：

- **Express-validator**：验证和净化用户输入
- **Sanitize-html**：移除潜在的恶意 HTML 内容
- **自定义净化**：递归净化所有对象字段的辅助函数

### 5. 会话安全改进

加强了会话管理：

- **安全会话配置**：会话使用 HTTP-only、安全、同站点 Cookie
- **会话超时**：可配置的会话过期时间
- **会话存储**：使用 Redis 存储会话并设置 TTL

## 实现细节

### 修改的文件

1. `package.json` - 添加了安全包：
   - helmet
   - express-rate-limit
   - csurf
   - express-validator
   - sanitize-html

2. `lib/spec.js` - 集成安全中间件：
   - 添加了带 CSP 配置的 Helmet 中间件
   - 添加了限速中间件
   - 添加了 CSRF 保护中间件

3. `lib/middlewear/security.js` - 创建安全中间件配置：
   - 带 CSP 策略的 Helmet 配置
   - 速率限制规则
   - CSRF 保护设置

4. `lib/middlewear/redis-session.js` - 增强会话安全性：
   - 改进了会话 Cookie 设置
   - 添加了 Redis TTL 配置

5. `lib/helpers/securityHelper.js` - 创建安全辅助函数：
   - 输入净化工具
   - 表单验证辅助函数
   - 对象字段净化
   - 安全随机字符串生成

6. `config/config.json` - 更新中间件配置：
   - 启用了带严格策略的 CSP
   - 启用了带安全设置的 CSRF 保护
   - 配置了安全头

## 使用指南

### 开发人员指南

1. **表单中的 CSRF 令牌**
   - 所有 POST/PUT/DELETE 表单必须包含 CSRF 令牌：
   ```html
   <input type="hidden" name="_csrf" value="{_csrf}" />
   ```

2. **输入验证**
   - 在控制器中使用验证中间件：
   ```javascript
   const { validateInputs, validationResult } = require('../lib/helpers/securityHelper');
   
   app.post('/some-route', validateInputs, (req, res) => {
     const errors = validationResult(req);
     if (!errors.isEmpty()) {
       return res.status(400).json({ errors: errors.array() });
     }
     // 处理验证后的输入
   });
   ```

3. **输入净化**
   - 在存储或显示前净化用户输入：
   ```javascript
   const { sanitizeUserInput } = require('../lib/helpers/securityHelper');
   const cleanInput = sanitizeUserInput(req.body.userInput);
   ```

4. **对象字段净化**
   - 净化对象中的所有字段：
   ```javascript
   const { sanitizeObjectFields } = require('../lib/helpers/securityHelper');
   const cleanData = sanitizeObjectFields(req.body);
   ```

## 测试安全功能

### 手动测试步骤

1. **CSRF 保护**：
   - 提交没有 CSRF 令牌的表单 - 应该被拒绝
   - 提交带有无效 CSRF 令牌的表单 - 应该被拒绝

2. **速率限制**：
   - 在 15 分钟内从同一 IP 发出超过 100 个请求
   - 超过限制后应收到"请求过多"错误

3. **内容安全策略**：
   - 尝试加载 CSP 中不允许的外部脚本
   - 浏览器应该阻止加载未经授权的资源

### 自动测试

在您的测试套件中添加面向安全的测试：

1. 测试所有受保护的路由都需要有效的 CSRF 令牌
2. 测试速率限制行为与多次快速请求
3. 测试输入净化移除恶意内容
4. 验证安全头是否正确设置

## 未来增强

考虑实施以下附加安全措施：

1. **双因素认证 (2FA)**：添加基于 TOTP 或短信的 2FA
2. **安全日志**：实施详细的安全事件日志
3. **API 安全**：为 API 端点添加 JWT 令牌验证
4. **密码强度**：实施密码复杂度要求
5. **安全头**：根据需要添加额外的安全头
6. **依赖扫描**：定期扫描易受攻击的依赖项

## 参考资料

- [Helmet.js 文档](https://helmetjs.github.io/)
- [Express Rate Limit 文档](https://www.npmjs.com/package/express-rate-limit)
- [CSURF 文档](https://www.npmjs.com/package/csurf)
- [Express Validator 文档](https://express-validator.github.io/)
- [OWASP 安全指南](https://owasp.org/www-project-top-ten/)