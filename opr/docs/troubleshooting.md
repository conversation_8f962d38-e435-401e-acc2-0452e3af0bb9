# 故障排除指南

本文档提供了在使用保险核心业务管理系统构建和运行过程中可能遇到的常见问题及其解决方案。

## 构建问题

### 依赖安装失败

**问题**: 安装依赖时出现错误，如 `ENOENT`, `ETIMEDOUT` 或 `ECONNREFUSED`。

**解决方案**:

1. 检查网络连接
2. 尝试使用国内镜像源
   ```bash
   npm config set registry https://registry.npmmirror.com
   ```
3. 使用 `--legacy-peer-deps` 标志
   ```bash
   npm install --legacy-peer-deps
   ```
4. 清除 npm 缓存
   ```bash
   npm cache clean --force
   ```

### Grunt 任务失败

**问题**: 执行 Grunt 任务时出现错误。

**解决方案**:

1. 确保已全局安装 Grunt CLI
   ```bash
   npm install -g grunt-cli
   ```
2. 检查 Grunt 版本兼容性
   ```bash
   grunt --version
   ```
3. 使用 `--verbose` 标志获取详细错误信息
   ```bash
   grunt build --verbose
   ```
4. 检查任务配置文件是否正确

### Less 编译错误

**问题**: Less 文件编译失败，出现语法错误。

**解决方案**:

1. 检查 Less 文件语法
2. 确保 Less 版本兼容性
   ```bash
   npm list less
   ```
3. 尝试单独编译问题文件
   ```bash
   lessc public/css/problematic-file.less
   ```

### Asset Helper 路径问题

**问题**: `{@asset path="..."/}` 标签生成空的 script 或 link 标签。

**解决方案**:

1. 确保已运行构建命令生成资源文件
   ```bash
   grunt build
   ```
2. 检查 `.build/assets.json` 文件是否存在
3. 验证 asset helper 是否正确注册到 Dust 模板引擎
4. 检查 asset helper 的路径映射逻辑是否处理文件名冲突

### AngularJS 模块加载问题

**问题**: `Error: $injector:modulerr` 或 `Module 'homeApp' is not available`。

**解决方案**:

1. 检查脚本加载顺序，确保依赖模块在主模块之前加载
   - 公用模块 (`systemServices`, `systemConstants`, `commonDirectives`, `commonFilters`) 必须在 `homeApp` 之前加载
2. 验证模块定义是否正确
   ```javascript
   angular.module('homeApp', ['依赖模块列表'])
   ```
3. 检查是否存在循环依赖
4. 确保所有依赖的模块都已正确定义

### JavaScript 模块依赖错误

**问题**: `ReferenceError: cooperationControllers is not defined`。

**解决方案**:

1. 检查模块定义和引用是否一致
2. 确保没有循环依赖问题
3. 验证控制器注册到正确的模块
   ```javascript
   // 错误
   cooperationControllers.controller('MyCtrl', ...)
   
   // 正确
   inputCooperationControllers.controller('MyCtrl', ...)
   ```
4. 重新构建受影响的 JavaScript 文件

## 运行问题

### 服务器启动失败

**问题**: 应用服务器无法启动，出现端口占用或权限错误。

**解决方案**:

1. 检查端口是否被占用
   ```bash
   lsof -i :8080
   ```
2. 修改配置文件中的端口设置
3. 确保有足够的权限运行服务
4. 检查日志文件获取详细错误信息

### 数据库连接问题

**问题**: 无法连接到 MongoDB 数据库。

**解决方案**:

1. 确保 MongoDB 服务正在运行
   ```bash
   mongod --version
   ```
2. 检查数据库连接字符串是否正确
3. 验证数据库用户名和密码
4. 检查网络连接和防火墙设置

### 静态资源加载失败

**问题**: 浏览器无法加载 CSS、JS 或图片文件。

**解决方案**:

1. 检查浏览器控制台错误信息
2. 验证文件路径是否正确
3. 确保文件权限设置正确
4. 检查服务器静态文件配置

### 内存溢出

**问题**: 应用运行时出现内存溢出错误。

**解决方案**:

1. 增加 Node.js 内存限制
   ```bash
   NODE_OPTIONS=--max_old_space_size=4096 npm start
   ```
2. 检查代码中的内存泄漏
3. 优化大数据处理逻辑
4. 使用流式处理代替一次性加载大数据

## 性能问题

### 构建速度慢

**问题**: Grunt 构建过程耗时过长。

**解决方案**:

1. 使用 `time` 命令分析任务耗时
   ```bash
   time grunt build
   ```
2. 使用 `concurrent` 任务并行执行
3. 减少不必要的任务
4. 使用 `build:fast` 任务进行快速开发

### 页面加载缓慢

**问题**: 网页加载时间过长。

**解决方案**:

1. 使用浏览器开发工具分析加载性能
2. 优化图片和静态资源
3. 实施代码分割和懒加载
4. 使用 CDN 加速静态资源加载

### API 响应慢

**问题**: 后端 API 响应时间过长。

**解决方案**:

1. 添加性能日志记录关键操作耗时
2. 优化数据库查询
3. 实施缓存策略
4. 使用数据库索引

## 环境问题

### 开发与生产环境差异

**问题**: 在生产环境中出现的问题在开发环境中无法重现。

**解决方案**:

1. 确保环境配置一致
2. 使用环境变量区分配置
3. 创建与生产环境相似的测试环境
4. 详细记录生产环境错误日志

### Node.js 版本兼容性

**问题**: 不同 Node.js 版本导致的兼容性问题。

**解决方案**:

1. 使用 `.nvmrc` 文件固定 Node.js 版本
   ```
   12.22.12
   ```
2. 使用 Volta 或 nvm 管理 Node.js 版本
3. 在 `package.json` 中指定引擎要求
   ```json
   "engines": {
     "node": ">=12.0.0 <14.0.0"
   }
   ```

### 依赖冲突

**问题**: 依赖包之间的版本冲突。

**解决方案**:

1. 使用 `npm ls` 检查依赖树
2. 使用 `npm dedupe` 减少重复依赖
3. 更新 `package.json` 中的依赖版本
4. 使用 `npm-check-updates` 检查可更新的依赖

## 安全问题

### 依赖漏洞

**问题**: npm audit 报告依赖存在安全漏洞。

**解决方案**:

1. 运行 `npm audit fix` 自动修复
2. 手动更新有漏洞的依赖
3. 评估漏洞风险，决定是否需要立即修复
4. 添加安全监控和定期审计

### 敏感信息泄露

**问题**: 配置文件中包含敏感信息。

**解决方案**:

1. 使用环境变量存储敏感信息
2. 创建 `.env.example` 文件作为模板
3. 确保敏感文件已添加到 `.gitignore`
4. 使用加密工具保护敏感配置

## 获取帮助

如果您遇到无法解决的问题，请尝试以下方式获取帮助：

1. 查阅项目文档
2. 搜索相关错误信息
3. 检查项目 issue 跟踪器
4. 联系系统管理员或开发团队

提问时，请提供以下信息：
- 详细的错误信息
- 复现步骤
- 环境信息（操作系统、Node.js 版本等）
- 相关配置文件
