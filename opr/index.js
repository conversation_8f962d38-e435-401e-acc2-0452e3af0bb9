'use strict';

// 首先加载环境变量
const { loadEnvironment } = require('./lib/env-loader');
loadEnvironment();

const kraken = require('kraken-js'),
    app = require('express')(),
    options = require('./lib/spec')(app);
const sio = require('./lib/sio');
const http = require('http');
const socket = require('./lib/socket');
const logger = require('@huibao/logger').logger();
const dust = require('dustjs-linkedin');
const assetHelper = require('./lib/helpers/assetHelper');

global.env = app.settings.env;

// 注册资源助手到 Dust 模板引擎
assetHelper(dust);

app.use(kraken(options));

const port = process.env.PORT || 8080;
const host = process.env.HOST || '0.0.0.0';
global.port = port;
global.ROOT_PATH = __dirname;

const server = http.createServer(app);
const io = require('socket.io')(server, {
    pingTimeout: 20000,
    pingInterval: 1000,
});

socket(io, server);
server.listen(port, host);
server.on('listening', () => {
    logger.info('[%s] Listening on http://%s:%d', app.settings.env, host, port);
    // ec9fd2aee4e21df1dc30ebf06a4018dd
    // sio.init(server);
    // if(app.settings.env == 'test') {
    //     console.log('xprofiler start');
    //     require('xprofiler').start();
    // }
});
