/**
 * Module that will handle our authentication tasks
 */
'use strict';

const fs = require('fs');
let http = require('https');
const async = require('async');
const jwt = require('express-jwt');
const passport = require('passport');
let User = require('../models/system/User');
const OAuth2Strategy = require('passport-oauth').OAuth2Strategy;
let authConfig = {};

const cache = require('../lib/cacheHelper');
const restClient = require('../lib/restClient/restClient');
const logger = require('@huibao/logger').logger();

exports.config = function(config) {
    authConfig = config;
};
exports.setNodeEnv = function(env) {
    authConfig.env = env;
};

exports.authenticate = function() {
    return function(req, res, next) {
        passport.authenticate('oauth2')(req, res, next);
    };
};

exports.getToken = function(cb) {
    cache.get('appToken', 'opr_app', function(token) {
        logger.info('appToken:' + token);
        if (!token) {
            restClient.getClientToken({}, function(err, result) {
                logger.info('OPR_getClientToken:' + JSON.stringify(err) + '|| ' + JSON.stringify(result));
                cache.put('appToken', 'opr_app', result.data.clientToken);
                cb(result.data.clientToken);
            });
        } else {
            cb(token);
        }
    });
};


exports.oauth2 = function() {
    return new OAuth2Strategy({
        authorizationURL: authConfig.oauth.authorizationURL,
        tokenURL: authConfig.oauth.tokenURL,
        clientID: authConfig.oauth.clientID,
        clientSecret: authConfig.oauth.clientSecret,
        callbackURL: authConfig.oauth.callbackURL,
    },
    function(accessToken, refreshToken, profile, done) {
        logger.info('aaaaaaaaa');
        const option = authConfig.getUserInfoOption;
        option.headers = {
            Authorization: 'Bearer ' + accessToken,
        };
        option.rejectUnauthorized = false;
        if (authConfig.env == 'development') {
            http = require('http');
        }
        logger.info('login_option:', JSON.stringify(option));
        const req = http.request(option, function(response) {
            let _buffer = '';
            response.on('data', function(chunk) {
                _buffer += chunk;
            });
            response.on('end', function() {
                const result = _buffer;

                const data = JSON.parse(result);
                logger.debug('login_user===', JSON.stringify(data.user));
                const user = new User(data.user);
                user.menuTree = data.menuTree;
                user.userInfo = data.userInfo;
                user.authUrls = data.authUrls;
                user.permitApps = data.apps;
                // oauth2 client token
                user.token = accessToken;
                // user access token
                user.jwtToken = data.user.accessToken;
                done(null, user);
            });
        });
        req.end();
    }
    );
};

/**
 * A helper method to determine if a user has been authenticated, and if they have the right role.
 * If the user is not known, redirect to the login page. If the role doesn't match, show a 403 page.
 * @param role The role that a user should have to pass authentication.
 */
exports.isAuthenticated = function(roles) {

    return function(req, res, next) {

        if (!req.isAuthenticated()) {

            // If the user is not authorized, save the location that was being accessed so we can redirect afterwards.
            req.session.goingTo = req.url;
            res.redirect('/login');
            return;
        }

        // If a role was specified, make sure that the user has it.
        let hasRole = true;
        const user = req.user;
        if (roles && req.baseUrl) {
            hasRole = false;

            // 开发环境 clientID 不做权限控制
            if (authConfig.oauth.clientID == 'huibaoApp1') {
                hasRole = true;
            } else {
                // if (user.authUrls && user.authUrls.indexOf(req.baseUrl)) {
                //     hasRole = true;
                // }
                hasRole = true;
            }
        }
        // 只有基本用户权限的用户不能访问
        if(user.roles.includes['ROLE_USER'] && user.roles.length == 1){
            hasRole = false;
        }
        if (!hasRole) {
            res.status(401);
            res.render('errors/401');
            return;
        }
        next();

    };
};

/**
 * A helper method to add the user to the response context so we don't have to manually do it.
 * @param req
 * @param res
 * @param next
 */
exports.injectUser = function() {
    return function(req, res, next) {
        if (req.isAuthenticated()) {
            const user = req.user;
            const branch = user.branch;
            if (branch && branch != 'ALL') {
                user.title = branch.substr(0, 4);
            } else {
                user.title = 'ALL';
            }
            res.locals.logedInUser = user;
            res.locals.roleMenuTree = JSON.parse(user.menuTree);
            return next();
        }
        return next();

    };
};

exports.hostUrl = function() {
    return authConfig.oprURL;
};

// 判断是否是可操作机构,增加机构权限
exports.availableBranch = function(condition, field, user, branch) {
    const oprBranch = user.oprBranches;
    if (oprBranch.indexOf('ALL') >= 0) return;

    var branch_str = oprBranch.toString();
    let match_branch;
    if(branch_str == 'SLBX') {
        match_branch = `^${branch_str}`
    } else {
        match_branch = `^(${branch_str.replace(/,/g, '|')})$`
    }
    console.log(match_branch);
    if (branch) {
        let branchCode;
        oprBranch.forEach(e => {
            if (branch.startsWith(e)) {
                branchCode = branch;
            }
        });
        return condition[field] = branchCode || new RegExp(match_branch, 'i');
    }
    condition[field] = new RegExp(match_branch);
};
// 判断是否有渠道小类权限
exports.availableChannel = function(condition, field, user, agentType) {
    // 用户有权限的渠道
    const channels = user.channels || [];
    let channelCode,
        channelObj = global.allChannel;
    const channelArr = [];

    // 如果拥有全部渠道权限
    if (channels.includes('ALL')) {
        if (!agentType) return ;
        channelCode = agentType;
    } else {
        channels.forEach(e => {
            if (agentType && agentType.startsWith(e)) {
                channelCode = agentType;
            }
            channelObj[e] && channelArr.push(channelObj[e]);
        });
    }
    return condition[field] = channelCode || { $in: channelArr };
};

exports.orBranchCondition = function(condition, user, field) {
    const oprBranches = user.oprBranches;
    if (oprBranches.includes('ALL')) return;

    let str = oprBranches.toString();
    let match_branch;
    if(str == 'SLBX') {
        match_branch = `^${str}`
    } else {
        match_branch = `^(${str.replace(/,/g, '|')})$`
    }
    const reg = new RegExp(match_branch);
    const orlist = [];
    const fieldObj = {};
    fieldObj[field] = reg;
    orlist.push(fieldObj);
    orlist.push({ branch: reg });
    condition.$and = [{ $or: orlist }];
};


/**
 * 保单机构权限控制
 * modity by HB-883
 * @param {*} user
 */
exports.poyBranchCondition = function(condition, user, branch, issueBranch) {
    const oprBranch = user.oprBranches;
    if (oprBranch.includes('ALL')) return;

    var branch_str = oprBranch.toString();
    let match_branch;
    if(branch_str == 'SLBX') {
        match_branch = `^${branch_str}`
    } else {
        match_branch = `^(${branch_str.replace(/,/g, '|')})$`
    }
    console.log(match_branch);

    // 根据brightType匹配操作机构
    if (user.brightType == '1') {
        condition.$and = [{ branch: new RegExp(match_branch, 'i') }];
    } if (user.brightType == '2') {
        condition.$and = [{ issueBranch: new RegExp(match_branch, 'i') }];
    } else if (user.brightType == '3') {
        var reg = new RegExp(match_branch);
        condition.$and = [{ $or: [{ issueBranch: reg }, { branch: reg }] }];
    } else if (user.brightType == '4') {
        var reg = branch || new RegExp(match_branch);
        condition.$and = [{ branch: reg }, { issueBranch: reg }];
    }

    if (branch) condition.$and.push({ branch });
    if (issueBranch) condition.$and.push({ issueBranch });
    logger.debug('user.brightType:', user.brightType);
    return user.bshowType || '1';
};

// 特殊字符处理
exports.replaces = function(name) {
    if (name != '') {
        return name.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
    }
};

exports.isAuthenticatedByToken = function(roles, systems) {
    const bearerStrategy = function(accessToken, done) {
        if (!accessToken) {
            logger.error('access token is null');
            return done(null, false);
        }
        cache.get('token', accessToken, function(user) {
            if (!user) {
                logger.error('can not find user by token');
                return done(null, false);
            }
            done(null, user);
        });
    };
    const fromHeaderOrQuerystring = function(req) {
        const parts = req.headers.authorization ? req.headers.authorization.split(' ') : 0;
        if (parts.length == 2) {
            let scheme = parts[0],
                credentials = parts[1];

            if (/^Bearer$/i.test(scheme)) {
                return credentials;
            }
            return null;

        }
        return req.headers.token || req.query.token;

    };
    return function(req, res, next) {
        const token = fromHeaderOrQuerystring(req);
        logger.info('token:' + token);
        async.waterfall([ function(cb) {
            try {
                jwt({
                    secret: process.env.secret || 'huibaokejium1qaz2wsx',
                    userProperty: 'payload',
                    getToken: fromHeaderOrQuerystring,
                    isRevoked(req, payload, done) {
                        bearerStrategy(token, function(err, user) {
                            if (err) {
                                return done(err);
                            }
                            if (!user) {
                                return done(null, true);
                            }
                            req.user = user;
                            done(null, false);
                        });
                    },
                })(req, res, function(err) {
                    if (err) {
                        return cb('token not a valied jwt token' + err);
                    }
                    cb(null);
                });
            } catch (e) {
                cb('Unexpected token');
            }
        }, function(cb) {
            let hasRole = true;
            const user = req.user;
            if (roles) {
                hasRole = false;
                logger.debug('roles', roles);
                for (let i = 0, l = user.roles.length; i < l; i++) {
                    if (roles.indexOf(user.roles[i]) >= 0) {
                        hasRole = true;
                        break;
                    }
                }
            }
            // 只有基本用户权限的用户不能访问
            if(!systems && user.roles.includes['ROLE_USER'] && user.roles.length == 1){
                hasRole = false;
            }
            if (!hasRole) {
                res.status(401);
                return cb('not Authorized');
            }
            if (systems) {
                logger.debug('systems:', systems);
                if (systems.indexOf(user.clientID) < 0) {
                    return cb('not Authorized system');
                }
            }
            cb(null, user);
        } ], function(err, user) {
            if (err) {
                return res.json({
                    code: 400,
                    err,
                });
            }
            res.locals.user = user;
            next();
        });
    };
};
