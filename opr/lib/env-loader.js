/**
 * 环境变量加载器
 * 负责根据环境加载对应的 .env 文件
 */
'use strict';

const path = require('path');
const fs = require('fs');

/**
 * 加载环境变量配置
 */
function loadEnvironment() {
    const nodeEnv = process.env.NODE_ENV || 'development';
    const rootDir = path.resolve(__dirname, '..');

    // 定义可能的环境配置文件路径
    const envFiles = [
        path.join(rootDir, `.env.${nodeEnv}.local`),
        path.join(rootDir, `.env.${nodeEnv}`),
        path.join(rootDir, '.env.local'),
        path.join(rootDir, '.env')
    ];

    // 按优先级加载环境文件
    envFiles.forEach(filePath => {
        if (fs.existsSync(filePath)) {
            console.log(`Loading environment from: ${filePath}`);
            require('dotenv').config({ path: filePath });
        }
    });

    // 验证必需的环境变量
    validateRequiredEnvVars();
}

/**
 * 验证必需的环境变量
 */
function validateRequiredEnvVars() {
    const requiredVars = [
        'NODE_ENV',
        'PORT',
        'DB_HOST',
        'DB_NAME',
        'REDIS_HOST',
        'REDIS_PORT',
        'SESSION_SECRET',
        'OAUTH_CLIENT_ID',
        'OAUTH_CLIENT_SECRET',
        'UM_HOST',
        'UM_PORT'
    ];

    const missingVars = requiredVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
        console.error('❌ Missing required environment variables:');
        missingVars.forEach(varName => {
            console.error(`   - ${varName}`);
        });
        process.exit(1);
    }

    console.log('✅ All required environment variables are set');
}

/**
 * 获取环境变量配置对象
 */
function getEnvConfig() {
    return {
        // 应用基础配置
        loggerLevel: process.env.LOGGER_LEVEL || 'info',
        branch: process.env.BRANCH || 'SLBX',

        // 数据库配置
        databaseConfig: {
            host: process.env.DB_HOST,
            database: process.env.DB_NAME,
            options: {
                useNewUrlParser: true,
                useUnifiedTopology: true,
                useCreateIndex: true,
                user: process.env.DB_USER,
                pass: process.env.DB_PASS
            }
        },

        // Redis 配置
        cacheConfig: {
            port: parseInt(process.env.REDIS_PORT, 10) || 6379,
            host: process.env.REDIS_HOST,
            options: {},
            password: process.env.REDIS_PASSWORD
        },

        // OAuth 认证配置
        authConfig: {
            oauth: {
                authorizationURL: process.env.OAUTH_AUTHORIZATION_URL,
                tokenURL: process.env.OAUTH_TOKEN_URL,
                clientID: process.env.OAUTH_CLIENT_ID,
                clientSecret: process.env.OAUTH_CLIENT_SECRET,
                callbackURL: process.env.OAUTH_CALLBACK_URL
            },
            getUserInfoOption: {
                host: process.env.UM_HOST,
                port: process.env.UM_PORT || '443',
                path: '/auth/api/userinfo',
                method: 'get'
            },
            oprURL: process.env.OPR_REST_URL
        },

        // 用户管理配置
        umConfig: {
            host: process.env.UM_HOST,
            port: process.env.UM_PORT || '443',
            method: 'get',
            logoutUrl: process.env.UM_LOGOUT_URL,
            branch_url: process.env.UM_BRANCH_URL || `https://${process.env.UM_HOST}/auth/api/branchInfo`,
            prov_url: process.env.UM_PROV_URL || `https://${process.env.UM_HOST}/auth/api/provInfo`
        },

        // oss配置
        ossConfig: {
            policy: {
                type: {
                    resource: "policy",
                },
            },
            invoice: {
                type: {
                    resource: "invoice",
                },
            },
            activity: {
                type: {
                    resource: "activity",
                    filetype: "jpg",
                },
            },
            junziqian: {
                type: {
                    resource: "junziqian",
                    filetype: "pdf",
                },
            },
            opr: {
                type: {
                    resource: "opr",
                },
            },
            report: {
                type: {
                    resource: "report",
                    type: "xlsx",
                },
            },
            camelUrl: process.env.CAMEL_REST_URL,
            clientId: process.env.CLIENT_ID
        },

        // 外部服务 REST URL
        restUrl: {
            esbRestUrl: process.env.ESB_REST_URL,
            pandaRestUrl: process.env.PANDA_REST_URL,
            apisRestUrl: process.env.APIS_REST_URL,
            bwRestUrl: process.env.BW_REST_URL,
            umRestUrl: process.env.UM_REST_URL,
            pigeonRestUrl: process.env.PIGEON_REST_URL,
            camelRestUrl: process.env.CAMEL_REST_URL,
            racoonRestUrl: process.env.RACOON_REST_URL,
            tigerRestUrl: process.env.TIGER_REST_URL,
            udeskUrl: process.env.UDESK_URL
        },

        // 客户端信息
        clientInfo: {
            secret: process.env.CLIENT_SECRET,
            clientId: process.env.CLIENT_ID,
            udesk_name: process.env.UDESK_NAME,
            udesk_pass: process.env.UDESK_PASS
        },

        // Web URL 配置
        webUrl: {
            oprRestUrl: process.env.OPR_REST_URL,
            bwRestUrl: process.env.BW_WEB_URL,
            spaUrl: process.env.SPA_URL,
            esbUrl: process.env.ESB_URL,
            puppUrl: process.env.PUPP_URL
        },
    };
}

module.exports = {
    loadEnvironment,
    getEnvConfig
};
