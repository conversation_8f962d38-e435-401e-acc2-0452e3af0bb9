'use strict';

const fs = require('fs');
const path = require('path');

// 加载资源映射文件（如果存在）
let assetMap = {};
try {
    const manifestPath = path.join(process.cwd(), '.build', 'assets.json');
    if (fs.existsSync(manifestPath)) {
        assetMap = require(manifestPath);
    } else {
        // 如果资源映射文件不存在，则尝试通过扫描 .build 目录来构建映射
        console.log('Asset manifest not found, building mapping from .build directory');
        const buildDir = path.join(process.cwd(), '.build');
        
        // 扫描 JS 文件
        scanDirectory(buildDir, 'js', '.min.js');
        
        // 扫描 CSS 文件
        scanDirectory(buildDir, 'css', '.min.css');
        
        // 扫描 HTML 目录下的 JS 文件
        if (fs.existsSync(path.join(buildDir, 'html'))) {
            scanDirectory(path.join(buildDir, 'html'), 'html', '.min.js');
        }
        
        console.log(`Built asset map with ${Object.keys(assetMap).length} entries`);
    }
} catch (e) {
    console.warn('Error building asset map, using original file paths:', e);
}

// 扫描目录并构建资源映射
function scanDirectory(dir, subdir, ext) {
    if (!fs.existsSync(dir)) return;
    
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
            // 递归扫描子目录
            scanDirectory(filePath, path.join(subdir, file), ext);
        } else if (file.endsWith(ext)) {
            // 构建资源映射
            const originalName = file.replace(ext, '.js').replace(ext.replace('.min', ''), '');
            const relativePath = path.join(subdir, file);
            assetMap[originalName] = relativePath;
        }
    });
}

// 获取资源的真实路径
function getAssetPath(originalPath) {
    // 移除开头的斜杠
    const cleanPath = originalPath.replace(/^\/+/, '');
    const fileName = cleanPath.split('/').pop();
    
    // 最后检查是否有带hash的版本存在
    const fs = require('fs');
    const path = require('path');
    const dirPath = path.join(process.cwd(), '.build', path.dirname(cleanPath));
    
    if (fs.existsSync(dirPath)) {
        const files = fs.readdirSync(dirPath);
        const baseName = fileName.replace('.min.', '.').replace(/\.[^.]+$/, '');
        const extension = fileName.split('.').pop();
        
        const hashFile = files.find(f => 
            f.startsWith(baseName) && 
            f.endsWith('.' + extension) && 
            f.match(/\.[a-f0-9]{8}\.[^.]+$/)
        );
        
        if (hashFile) {
            return '/' + path.join(path.dirname(cleanPath), hashFile).replace(/\\/g, '/');
        }
    }
    
    // 如果映射中没有，检查文件是否直接存在于.build目录中
    const directPath = path.join(process.cwd(), '.build', cleanPath);
    
    if (fs.existsSync(directPath)) {
        return '/' + cleanPath;
    }
    
    // 如果请求的是.min文件但不存在，尝试非.min版本
    if (fileName.includes('.min.')) {
        const nonMinPath = cleanPath.replace('.min.', '.');
        const nonMinDirectPath = path.join(process.cwd(), '.build', nonMinPath);
        if (fs.existsSync(nonMinDirectPath)) {
            return '/' + nonMinPath;
        }
    }
    
    // 如果有映射，使用映射后的路径（不包含 .build 前缀）
    if (assetMap[fileName]) {
        return '/' + assetMap[fileName];
    }
    
    // 如果没有找到.min版本，尝试查找非.min版本
    if (fileName.includes('.min.')) {
        const nonMinFileName = fileName.replace('.min.', '.');
        if (assetMap[nonMinFileName]) {
            return '/' + assetMap[nonMinFileName];
        }
    }
    
    // 否则返回原始路径
    return originalPath;
}

module.exports = function(dust) {
    // 注册 asset helper
    dust.helpers.asset = function(chunk, context, bodies, params) {
        var path = params.path;
        if (!path) {
            return chunk.write('');
        }
        
        var assetPath = getAssetPath(path);
        return chunk.write(assetPath);
    };
    
    return {
        getAssetPath
    };
};