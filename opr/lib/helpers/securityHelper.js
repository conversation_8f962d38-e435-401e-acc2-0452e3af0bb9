'use strict';

const { sanitizeInput, validateInputs, validationResult } = require('../middlewear/security');

/**
 * Security helper functions for use in controllers and services
 */

/**
 * Sanitize user inputs to prevent XSS attacks
 * @param {string} input - The input string to sanitize
 * @returns {string} - The sanitized string
 */
function sanitizeUserInput(input) {
    if (typeof input !== 'string') {
        return input;
    }
    return sanitizeInput(input);
}

/**
 * Validate form inputs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Promise} - Resolves if validation passes, sends error response if not
 */
async function validateFormInputs(req, res, next) {
    await Promise.all(validateInputs.map(validation => validation.run(req)));
    
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }
    
    next();
}

/**
 * Sanitize all fields in an object
 * @param {Object} obj - Object containing fields to sanitize
 * @returns {Object} - New object with sanitized fields
 */
function sanitizeObjectFields(obj) {
    const sanitized = {};
    
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            if (typeof obj[key] === 'string') {
                sanitized[key] = sanitizeUserInput(obj[key]);
            } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                sanitized[key] = sanitizeObjectFields(obj[key]);
            } else {
                sanitized[key] = obj[key];
            }
        }
    }
    
    return sanitized;
}

/**
 * Generate a secure random string
 * @param {number} length - Length of the string to generate
 * @returns {string} - Secure random string
 */
function generateSecureRandomString(length = 32) {
    return require('crypto').randomBytes(length).toString('hex');
}

module.exports = {
    sanitizeUserInput,
    validateFormInputs,
    sanitizeObjectFields,
    generateSecureRandomString
};