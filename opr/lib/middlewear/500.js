'use strict';

const logger = require('@huibao/logger').logger();
const crypto = require('crypto');

/**
 * 生成唯一错误ID用于追踪
 */
function generateErrorId() {
    return crypto.randomBytes(8).toString('hex');
}

/**
 * 定义安全的错误消息映射
 */
const safeErrorMessages = {
    400: '请求参数错误',
    401: '身份验证失败',
    403: '权限不足',
    404: '资源未找到',
    429: '请求过于频繁',
    500: '服务器内部错误',
    502: '网关错误',
    503: '服务暂时不可用'
};

/**
 * 检查是否为敏感错误信息
 */
function containsSensitiveInfo(message) {
    const sensitivePatterns = [
        /password/i,
        /token/i,
        /secret/i,
        /key/i,
        /connection string/i,
        /database/i,
        /mongo/i,
        /redis/i
    ];
    
    return sensitivePatterns.some(pattern => pattern.test(message));
}

module.exports = function(template) {
    return function serverError(err, req, res, next) {
        const errorId = generateErrorId();
        const errorCode = err.statusCode || err.status || 500;
        const isDevelopment = process.env.NODE_ENV === 'development';
        
        // 记录完整错误信息（仅服务器日志）
        logger.error('Server Error:', {
            errorId,
            error: err.message,
            stack: err.stack,
            url: req.url,
            method: req.method,
            userAgent: req.get('User-Agent'),
            ip: req.ip,
            user: req.user ? req.user.id : 'anonymous',
            timestamp: new Date().toISOString(),
            statusCode: errorCode
        });

        // 设置错误状态码
        res.status(errorCode);
        
        // 确定是否显示错误详情
        let errorMessage = safeErrorMessages[errorCode] || '未知错误';
        let errorDetails = null;
        
        if (isDevelopment && !containsSensitiveInfo(err.message)) {
            errorDetails = err.message;
        }
        
        try {
            // 尝试渲染错误页面
            const model = {
                header: '系统错误',
                description: errorMessage,
                errorId: errorId,
                details: errorDetails
            };
            
            res.render(template, model);
        } catch (renderError) {
            // 如果模板渲染失败，发送简单的错误响应
            logger.error('Error rendering error template:', {
                errorId,
                renderError: renderError.message,
                originalError: err.message
            });
            
            res.setHeader('Content-Type', 'text/html; charset=utf-8');
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>服务器错误</title>
                    <meta charset="utf-8">
                    <style>
                        body { 
                            font-family: Arial, sans-serif; 
                            margin: 40px; 
                            background-color: #f5f5f5; 
                        }
                        .error-container { 
                            max-width: 600px; 
                            margin: 0 auto; 
                            text-align: center; 
                            background: white; 
                            padding: 40px; 
                            border-radius: 8px; 
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
                        }
                        h1 { color: #d32f2f; margin-bottom: 20px; }
                        p { color: #666; line-height: 1.6; margin-bottom: 15px; }
                        .error-id { 
                            color: #999; 
                            font-size: 12px; 
                            margin-top: 20px; 
                            font-family: monospace; 
                        }
                        .details { 
                            background: #f8f8f8; 
                            padding: 15px; 
                            border-radius: 4px; 
                            margin-top: 20px; 
                            font-family: monospace; 
                            font-size: 14px; 
                            color: #333; 
                        }
                    </style>
                </head>
                <body>
                    <div class="error-container">
                        <h1>${errorMessage}</h1>
                        <p>抱歉，服务器出现了一个错误。请稍后再试。</p>
                        ${errorDetails ? `<div class="details"><strong>错误详情:</strong><br>${errorDetails}</div>` : ''}
                        <div class="error-id">错误ID: ${errorId}</div>
                    </div>
                </body>
                </html>
            `);
        }
    };
};