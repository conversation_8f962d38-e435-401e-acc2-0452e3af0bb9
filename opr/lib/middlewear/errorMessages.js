'use strict';
const logger = require('@huibao/logger').logger();

module.exports = function() {
    return function(req, res, next) {
    	logger.info(res.locals.err);
        const view = res.locals.view;
        const model = res.locals.model;
    	const err = res.locals.err || {};
    	let errorMessages = {};
    	const showErrorMessage = [];
    	if (err.name === 'ValidationError') {
        	const errors = err.errors;
        	for (var p in errors) {
        		errorMessages[p + ''] = errors[p].message;
        	}
    	} else if (err.name === 'MongooseError') {
    		errorMessages = JSON.parse(err.message);
    	} else if (err.name === 'MongoError') {
    		errorMessages.err = err.err;
    	}

    	for (var p in errorMessages) {
    		showErrorMessage.push(errorMessages[p]);
    	}
    	if (err && view) {
        	res.locals.errorMessages = errorMessages;
        	res.locals.showErrorMessage = showErrorMessage;
        	res.render(view, model);
    	} else {
    		next();
    	}

    };
};
