const session = require("express-session");
const redis = require("redis");
const RedisStore = require("connect-redis")(session);

module.exports = function redisSession() {
    console.log('redis-session middleware loading...');

    // 安全的会话配置
    const sessionSettings = {
        secret: process.env.SESSION_SECRET,
        key: process.env.SESSION_KEY || 'oprsid',
        resave: false, // 避免不必要的保存
        saveUninitialized: false, // 减少存储使用
        rolling: true, // 滚动会话过期时间
        cookie: {
            secure: process.env.NODE_ENV === 'production', // 生产环境强制HTTPS
            httpOnly: true, // 防止XSS访问cookie
            maxAge: 7200000, // 2小时 单位毫秒
            sameSite: 'strict' // CSRF保护
        }
    };

    const redisConfig = {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10) || 6379,
        db: parseInt(process.env.REDIS_DB, 10) || 2,
        ttl: 18000, // 5小时 单位秒
        pass: process.env.REDIS_PASSWORD
    };

    console.log('Session config:', {
        secret: sessionSettings.secret ? '[SET]' : '[NOT SET]',
        key: sessionSettings.key
    });
    console.log('Redis config:', {
        host: redisConfig.host,
        port: redisConfig.port,
        db: redisConfig.db
    });

    // 创建 Redis 客户端
    const redisClient = redis.createClient({
        host: redisConfig.host,
        port: redisConfig.port,
        password: redisConfig.pass,
        db: redisConfig.db || 0,
    });

    // 处理 Redis 连接错误
    redisClient.on("error", (err) => {
        console.error("Redis connection error:", err);
    });

    // 创建 RedisStore 实例，传入 Redis 客户端
    sessionSettings.store = new RedisStore({
        client: redisClient,
        ttl: redisConfig.ttl, // Set TTL for session in Redis
        disableTouch: false // Enable touch to extend session lifetime
    });

    return session(sessionSettings);
};
