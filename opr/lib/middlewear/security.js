'use strict';

const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const csrf = require('csurf');
const { body, validationResult } = require('express-validator');
const sanitizeHtml = require('sanitize-html');

/**
 * Security middleware configuration
 */

// Rate limiting middleware
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
});

// Enhanced CSRF protection
const csrfProtection = csrf({
    cookie: {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
    }
});

// Content Security Policy configuration
const cspConfig = {
    directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", 'fonts.googleapis.com'],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        fontSrc: ["'self'", 'fonts.gstatic.com'],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: [],
    },
};

// Sanitize HTML helper function
function sanitizeInput(dirty) {
    return sanitizeHtml(dirty, {
        allowedTags: [], // Remove all tags by default
        allowedAttributes: {}, // Remove all attributes
    });
}

// Input validation middleware
const validateInputs = [
    body('*').custom((value, { path }) => {
        // Skip validation for certain fields that may contain special characters
        if (['_csrf', 'password', 'confirmPassword'].includes(path)) {
            return true;
        }
        
        // Check for potentially dangerous characters
        if (typeof value === 'string' && /[<>"'`;\/\(\)]/.test(value)) {
            throw new Error(`Invalid characters in field ${path}`);
        }
        return true;
    })
];

module.exports = {
    helmet: helmet,
    limiter: limiter,
    csrfProtection: csrfProtection,
    cspConfig: cspConfig,
    sanitizeInput: sanitizeInput,
    validateInputs: validateInputs,
    validationResult: validationResult
};