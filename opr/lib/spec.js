'use strict';

const path = require('path');
let passport = require('passport'),
    auth = require('../lib/auth'),
    agenda = require('../lib/agenda'),
    db = require('@huibao/database');
const commonService = require('../services/commonService');

const showMenuMiddleware = require('../lib/middlewear/showMenu');
const requestLogger = require('./middlewear/requestLogger');
const specialization = require('../lib/middlewear/specialization');
const errorMessages = require('../lib/middlewear/errorMessages');
const cacheHelper = require('../lib/cacheHelper');
const restClient = require('../lib/restClient/restClient');
const webClient = require('../lib/webClient/webClient');
const umClient = require('../lib/umClient');
const permitAppHelper = require('../lib/permitAppHelper');
const paramHelper = require('../lib/paramHelper');
const ossHelper = require('@huibao/oss-helper');
const logger = require('@huibao/logger');
const { getEnvConfig } = require('./env-loader');

module.exports = function spec(app) {
    app.on('middleware:after:session', function configPassport(eventargs) {
        passport.use('oauth2', auth.oauth2());
        // used to serialize the user for the session
        passport.serializeUser(function(user, done) {
            done(null, user);
        });
        // used to deserialize the user
        passport.deserializeUser(function(user, done) {
            done(null, user);
        });
        app.use(passport.initialize());
        app.use(passport.session());
    });

    app.on('middleware:before:router', function configAfterRouter(eventargs) {
        // Security middlewares
        const security = require('./middlewear/security');
        const csrfValidator = require('./middlewear/csrfValidator');
        app.use(security.helmet(security.cspConfig));
        app.use(security.limiter);
        app.use(security.csrfProtection);
        app.use(csrfValidator.attachCSRFToken); // Attach CSRF token to response locals

        app.use(specialization());
        app.use(auth.injectUser()); // Inject the authenticated user into the response context
        app.use(showMenuMiddleware()); // 用于确定该显示什么菜单
        app.use(requestLogger());
        app.use(csrfValidator.validateCSRFToken); // Validate CSRF tokens in forms

        // 清空缓存
        cacheHelper.clearCache('Branch');
        cacheHelper.clearCache('Provincial');
        cacheHelper.clearCache('BranchName');
        cacheHelper.clearCache('BranchProvince');
        cacheHelper.clearCache('BranchInnerCode');
        cacheHelper.clearCache('BranchCompany');
        cacheHelper.clearCache('BLW_VERSION');
    });
    app.on('middleware:after:router', function configAfterRouter(eventargs) {
        app.use(errorMessages());
    });

    return {
        onconfig(config, next) {
            // 获取环境变量配置用于业务逻辑
            const envConfig = getEnvConfig();

            // 使用环境配置
            logger.config(envConfig.loggerLevel);
            global.branch = envConfig.branch;

            const dbConfig = envConfig.databaseConfig;
            db.config(dbConfig);
            // 初始化agenda
            agenda.config(dbConfig);

            const authConfig = envConfig.authConfig;
            auth.config(authConfig);
            auth.setNodeEnv(app.settings.env);
            ossHelper.config(envConfig.ossConfig);

            // 授信应用系统
            permitAppHelper.setConfig(authConfig.permitApps);

            // 初始化rest client
            restClient.config(envConfig.restUrl, envConfig.clientInfo);
            webClient.config(envConfig.webUrl);

            const umClientConfig = envConfig.umConfig;
            umClient.config(umClientConfig, app.settings.env);

            const cacheConfig = envConfig.cacheConfig;
            cacheHelper.config(cacheConfig);

            paramHelper.config(config.get('param'));

            // 初始化数据
            commonService.initial();

            next(null, config);
        },
    };
};
