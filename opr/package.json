{"name": "hizom", "version": "0.1.0", "description": "hizom cloud", "author": "hanting", "main": "index.js", "engines": {"node": ">=16.0.0"}, "scripts": {"start": "node index.js", "dev": "node index.js", "test": "npm run lint -- --fix && npm run test-local", "lint": "eslint .", "ci": "npm run lint && npm run cov"}, "overrides": {"graceful-fs": "^4.2", "underscore": "^1.13.6", "xmlhttprequest-ssl": "^2.1.1", "trim-newlines": "^3.0.1", "braces": "^3.0.3", "ws": "^8.18.0", "socket.io": "^4.8.1", "less": "^4.2.0", "clean-css": "^5.3.3", "shelljs": "^0.8.5", "glob": "^11.0.3"}, "dependencies": {"@huibao/common": "git+https://codeup.aliyun.com/60af9be42c5969c370c5a560/huibao-framework/common.git#v1.2.0", "@huibao/database": "git+https://codeup.aliyun.com/60af9be42c5969c370c5a560/huibao-framework/database.git#v1.0.4", "@huibao/logger": "git+https://codeup.aliyun.com/60af9be42c5969c370c5a560/huibao-framework/logger.git#v1.0.0", "@huibao/oss-helper": "git+https://codeup.aliyun.com/60af9be42c5969c370c5a560/huibao-framework/ossHelper.git#v1.1.0", "@socket.io/redis-adapter": "^8.3.0", "adaro": "0.1.10", "agenda": "3.1.0", "agendash": "^4.0.0", "ali-oss": "6.16.0", "archiver": "1.3.0", "async": "^3.2.4", "basic-auth": "1.0.0", "bluebird": "3.7.2", "bytes": "1.0.0", "cheerio": "0.20.0", "chinese-random-name": "0.1.3", "co": "4.6.0", "co-each": "0.1.0", "connect-flash": "0.1.1", "connect-redis": "5.2.0", "cors": "2.7.1", "cron": "1.0.4", "csurf": "^1.11.0", "date-fns": "2.30.0", "depd": "1.0.0", "dotenv": "^17.2.1", "dustjs-helpers": "1.6.3", "dustjs-linkedin": "2.6.2", "engine-munger": "0.2.9", "exceljs": "4.3.0", "express": "^4.21.2", "express-jwt": "3.4.0", "express-rate-limit": "^8.0.1", "express-session": "1.17.3", "express-validator": "^7.2.1", "finished": "1.2.2", "gm": "^1.23.0", "helmet": "^8.1.0", "iconv-lite": "0.4.13", "kraken-devtools": "1.4.0", "kraken-js": "2.4.0", "less": "^4.2.0", "lodash": "^4.17.4", "markdown": "0.5.0", "marked": "0.3.2", "moment": "2.13.0", "mongoose": "5.13.11", "mongoose-paginate": "5.0.0", "mongoose-updated_at": "0.0.2", "morgan": "1.0.0", "mqtt": "1.10.0", "node-uuid": "1.4.1", "node-xlsx": "0.23.0", "nodemailer": "2.7.2", "nodemailer-smtp-transport": "2.7.4", "number-precision": "^1.3.1", "passport": "0.2.0", "passport-local": "1.0.0", "passport-oauth": "1.0.0", "pinyin": "^2.10.2", "pygmentize-bundled": "2.1.1", "querystring": "0.2.1", "raven": "2.1.1", "redis": "3.1.2", "request": "2.88.2", "request-promise": "4.2.6", "restify-clients": "^2.6.4", "sanitize-html": "^2.17.0", "socket.io": "^4.8.1", "underscore": "^1.13.6", "validator": "^13.9.0", "winston": "2.3.1"}, "devDependencies": {"eslint": "5.0.0", "eslint-config-egg": "7", "eslint-plugin-import": "^2.13.0", "glob": "^11.0.3", "grunt": "^1.6.1", "grunt-concurrent": "^3.0.0", "grunt-config-dir": "^0.3.2", "grunt-contrib-clean": "^2.0.1", "grunt-contrib-compress": "^2.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-cssmin": "^5.0.0", "grunt-contrib-htmlmin": "^3.1.0", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-less": "^3.0.0", "grunt-contrib-uglify": "^5.2.2", "grunt-contrib-watch": "^1.1.0", "grunt-copy-to": "0.0.10", "grunt-dustjs": "^1.2.0", "grunt-filerev": "^2.3.1", "grunt-localizr": "^0.2.4", "grunt-mocha-cli": "^7.0.0", "grunt-usemin": "^3.1.1", "mocha": "^9.1.3", "supertest": "^6.1.6"}, "generator-kraken": {"version": "1.1.0", "template": "dustjs", "css": "less", "js": false, "task": "grunt"}, "volta": {"node": "12.22.12"}}