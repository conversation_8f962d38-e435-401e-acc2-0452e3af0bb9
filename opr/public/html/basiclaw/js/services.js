var basicLawServices = angular.module('basicLawServices', []);

basicLawServices.factory("BasicLawBasicLaw", ["$http", "HOST", function ($http, HOST) {
    return {
        basicMsg: function (obj) {
            return $http({method: "get", url: HOST.opr + '/basiclaw/messages', params: obj}).then(commResponseSuccess, commResponseError);
        },
        basicList: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/list', obj).then(commResponseSuccess, commResponseError);
        },
        checkList: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/checkList', obj).then(commResponseSuccess, commResponseError);
        },
        checkCountList: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/checkCountList', obj).then(commResponseSuccess, commResponseError);
        },
        runBasicLaw: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/runBasicLaw', obj).then(commResponseSuccess, commResponseError);
        },
        extractData: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/extractData', obj).then(commResponseSuccess, commResponseError);
        },
        updateCheck: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/updateCheck', obj).then(commResponseSuccess, commResponseError);
        },
        updateCheckCount: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/updateCheckCount', obj).then(commResponseSuccess, commResponseError);
        },
        blwVersion: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/blwVersion', obj).then(commResponseSuccess, commResponseError);
        },
        deleteBlwVersion: function (id) {
            return $http.delete(HOST.opr + '/basiclaw/deleteBlwVersion' + id);
        },
        contractList: function (obj) {
            return $http({
                method: "get",
                url: HOST.opr + '/basiclaw/contractList',
                params: obj
            }).then(commResponseSuccess, commResponseError);
        }
    }
}]);

basicLawServices.factory('BasicLawUpDown', ['$http', 'HOST', function ($http, HOST) {
    return {
        add: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/updown/add', obj).then(commResponseSuccess, commResponseError);
        },
        upCheck: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/updown/upCheck', obj).then(commResponseSuccess, commResponseError);
        },
        managerList: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/managerList', obj).then(commResponseSuccess, commResponseError);
        },
        managerListExport: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/managerList', obj, {responseType: 'blob'}).then(resSuccessBlobDownload,commResponseError);
        }
    }
}]);

basicLawServices.factory('BasicLawSubsidy', ['$http', 'HOST', function ($http, HOST) {
    return {
        add: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/subsidy/add', obj).then(commResponseSuccess, commResponseError);
        },
        check: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/subsidy/check', obj).then(commResponseSuccess, commResponseError);
        },
        done: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/subsidy/done', obj).then(commResponseSuccess, commResponseError);
        },
        importUpload: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/subsidy/importUpload', obj).then(commResponseSuccess, commResponseError);
        }
    }
}]);

basicLawServices.factory('BasicLawVentureGrant', ['$http', 'HOST', function ($http, HOST) {
    return {
        query: function (obj) {
            return $http({method: "get", url: HOST.opr + 'basicCheck/list', params: obj});
        },
        add: function (extractDate) {
            return $http.post(HOST.opr + 'basicCheck/add', {extractDate: extractDate});
        },
        detail: function (obj) {
            return $http({method: "get", url: HOST.opr + 'basicCheck/handleDetail', params: obj});
        },
        done: function (doneTemp) {
            return $http.post(HOST.opr + 'basicCheck/done', doneTemp);
        },
        checkBasicLaw: function (obj) {
            return $http.post(HOST.opr + 'basicCheck/checkBasicLaw', obj);
        },
        deepDetail: function (obj) {
            return $http({method: "get", url: HOST.opr + 'basicCheck/deepDetail', params: obj});
        }
    };
}]);

basicLawServices.factory("BasicLawSpecial", ["$http", "HOST", function ($http, HOST) {
    return {
        basicList: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/specialList', obj).then(commResponseSuccess, commResponseError);
        },
        specialDetail: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/specialDetail', obj).then(commResponseSuccess, commResponseError);
        },
        specialBasicLawVersion: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/blwSpecialVersion', obj).then(commResponseSuccess, commResponseError);
        },
        submit: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/submitSpecial', obj).then(commResponseSuccess, commResponseError);
        },
        confirm: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/confirmSpecial', obj).then(commResponseSuccess, commResponseError);
        },
        refuse: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/rejectSpecial', obj).then(commResponseSuccess, commResponseError);
        },
        delete: function (obj) {
            return $http.post(HOST.opr + '/basiclaw/deleteSpecial', obj).then(commResponseSuccess, commResponseError);
        }
    }
}]);
var resSuccessBlobDownload = function (response) {
    if (response.data.code && response.data.code != 0) {
        return { err: '导出数据失败', msg: response.data.msg }
    }

    var contentDisposition = response.headers("content-disposition");
    var fileName = contentDisposition.substr(contentDisposition.indexOf('filename="') + 10);
    fileName = decodeURIComponent(fileName.split('";')[0]);
    //var contentType = response.headers("content-type");
    var fileURL = URL.createObjectURL(response.data);

    console.log("contentDisposition: ", contentDisposition);
    console.log("fileName: ", fileName);
    console.log("fileURL: ", fileURL);

    var a         = document.createElement('a');
    a.href        = fileURL;
    a.target      = '_blank';
    a.download    = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    return fileURL;
};

var responseError = function (err) {
    return { err: err, msg: '调用后台出错' + err };
};
