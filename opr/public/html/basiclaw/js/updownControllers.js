var updownControllers = angular.module('updownControllers', []);

updownControllers.controller('BasicLawUpDownCtrl', ['$scope', '$ngBootbox', '$location', 'BasicLawBasicLaw', 'BasicLawUpDown', function ($scope, $ngBootbox, $location, BasicLawBasicLaw, BasicLawUpDown) {
    $scope.search = {};
    $scope.cases = [];
    $scope.personFyc = 1500;
    $scope.personFycZ = 1500;
    $scope.zpersonFyc = 3000;

    let yearMonth = moment(new Date()).add(-1, 'M').format("YYYY-MM");
    let endDate = moment(new Date()).add(-1, 'M').format("YYYY-MM");
    $scope.search.yearMonth = yearMonth;
    let dataOptions = {
        startView: 1,
        maxViewMode: 2,
        minViewMode: 1,
        format: "yyyy-mm",
        autoclose: true,
        language: 'zh-CN',
        endDate: endDate
    };
    $('#start_1').datepicker(dataOptions);

    if (window.location.href.indexOf("/agentUp") > 0) {
        $scope.search.type = 'AU';
        $scope.personSum = 6;
        $scope.remark = '按季度考核条件提取，满足条件升级为团队经理';
        $scope.agentUp = true;
        $scope.personSumZ = 40;
        $scope.remarkZ = '提取，满足条件升级为区域总监';
        $scope.basic = true;
    }
    if (window.location.href.indexOf("/managerUp") > 0) {
        $scope.search.type = 'MU';
        $scope.personSum = 40;
        $scope.remark = '按半年度考核条件提取，级为区域总监';
        $scope.basic = true;
    }
    if (window.location.href.indexOf("/managerDown") > 0) {
        $scope.search.type = 'MD';
        $scope.personSum = 6;
        $scope.remark = '按季度考核条件提取，满足条件降级为业务员';
        $scope.basic = true;
    }
    if (window.location.href.indexOf("/zdown") > 0) {
        $scope.search.type = 'ZD';
        $scope.personSum = 45;
        $scope.remark = '按年度考核条件提取，满足条件降级为业务员';
        $scope.basic = true;
    }
    if (window.location.href.indexOf("/seniorManagerUp") > 0) {
        $scope.search.type = 'SMU';
        $scope.NEW = true;
        $scope.yearMonth = true;
        $scope.personSum = 5;
        $scope.persistency = '85%';
        $scope.personFycZ = '15,000（B版：10,000）';
        $scope.effectiveNumber = '月度累积直接有效人力（≥）';
        $scope.fyc = '季度累计个人季度寿险FYC（≥）';
        $scope.remark = '按月度考核条件提取，满足条件升级为高级经理';
    }
    if (window.location.href.indexOf("/marketingManagerUp") > 0) {
        $scope.search.type = 'MMU';
        $scope.NEW = true;
        $scope.yearMonth = true;
        $scope.sellPosition = true;
        $scope.persistency = '85%';
        $scope.personFycZ = '10,000（B版：8,000）';
        $scope.fyc = '季度累计个人季度寿险FYC（≥）';
        $scope.remark = '按月度考核条件提取，满足条件升级为行销经理';
    }
    if (window.location.href.indexOf("/seniorDirectorUp") > 0) {
        $scope.search.type = 'SDU';
        $scope.NEW = true;
        $scope.basic = true;
        $scope.personSum = 5;
        $scope.persistency = '85%';
        $scope.personFycZ = '200,000（B版：150,000）';
        $scope.effectiveNumber = '直接推荐高级经理数（≥）';
        $scope.fyc = '团队自然季度累计FYP（≥）';
        $scope.remark = '按自然季度考核条件提取，满足条件升级为资深总监';
    }
    if (window.location.href.indexOf("/marketingDirectorUp") > 0) {
        $scope.search.type = 'MDU';
        $scope.NEW = true;
        $scope.basic = true;
        $scope.sellPosition = true;
        $scope.persistency = '85%';
        $scope.personFycZ = '300,000（B版：200,000）';
        $scope.fyc = '个人季度累计FYP（≥）';
        $scope.remark = '按自然季度考核条件提取，满足条件升级为行销总监';
    }
    if (window.location.href.indexOf("/seniorManagerDown") > 0) {
        $scope.search.type = 'SMD';
        $scope.NEW = true;
        $scope.yearMonth = true;
        $scope.DOWN = true;
        $scope.personSum = 6;
        $scope.persistency = '85%';
        $scope.personFycZ = '15,000（B版：10,000）';
        $scope.personFycD = '30,000（B版：30,000）';
        $scope.effectiveNumber = '直辖团队签约（含本人）（≥）';
        $scope.fyc = '本人连续3个月累计FYP（≥）';
        $scope.fycD = '直辖团队连续3个月累计FYP';
        $scope.remark = '按月度考核条件提取，满足条件降级为业务员';
    }
    if (window.location.href.indexOf("/seniorDirectorDown") > 0) {
        $scope.search.type = 'SDD';
        $scope.NEW = true;
        $scope.basic = true;
        $scope.DOWN = true;
        $scope.sellPosition = true;
        $scope.personFycD = 5;
        $scope.fycD = '所辖团队有高级经理数（≥）';
        $scope.persistency = '85%';
        $scope.personFycZ = '150,000（B版：100000）';
        $scope.fyc = '所辖团队＋直辖团队季度累计FYP（≥）';
        $scope.remark = '按季度考核条件提取，满足条件降级为高级经理';
    }
    if (window.location.href.indexOf("/marketingManagerDown") > 0) {
        $scope.search.type = 'MMD';
        $scope.NEW = true;
        $scope.yearMonth = true;
        $scope.sellPosition = true;
        $scope.persistency = '85%';
        $scope.personFycZ = '80,000（B版：50,000）';
        $scope.fyc = '个人连续3个月完成FYP（≥）';
        $scope.remark = '按月度考核条件提取，满足条件降级为业务员';
    }
    if (window.location.href.indexOf("/marketingDirectorDown") > 0) {
        $scope.search.type = 'MDD';
        $scope.NEW = true;
        $scope.basic = true;
        $scope.sellPosition = true;
        $scope.persistency = '85%';
        $scope.personFycZ = '240,000（B版：150,000）';
        $scope.fyc = '季度累计FYP（≥）';
        $scope.remark = '按月度考核条件提取，满足条件降级为资深总监';
    }
    $scope.query = function () {
        BasicLawBasicLaw.checkList({ type: $scope.search.type }).then(function (data) {
            console.log("upList:" + JSON.stringify(data));
            if (data.err) {
                $ngBootbox.alert(data.err);
                return;
            }
            $scope.cases = data.data;
            $scope.totalItems = data.pageCount * $scope.pageSize;
            $scope.currentPage = data.page;
        })
    };
    $scope.query();
    $scope.add = function () {
        if (($scope.basic && (!$scope.search.year || !$scope.search.quarter)) || ($scope.yearMonth && !$scope.search.yearMonth)) {
            $ngBootbox.alert("请选择年份和季度！");
            return;
        }
        BasicLawUpDown.add($scope.search).then(function (data) {
            console.log("UpDown.add:" + JSON.stringify(data));
            if (data.err) {
                $ngBootbox.alert(data.err);
                return;
            } else {
                $scope.cases.unshift(data);
            }
        })
    };
    $scope.checkDetail = function (o) {
        //更新状态为审核中
        if (o.status != "3") BasicLawBasicLaw.updateCheck({ _id: o._id });
        $location.url('/basiclaw/updownCheck/detail?case=' + JSON.stringify(o) + '&showType=' + $scope.search.type);
    };
    $scope.dateOptions = {
        maxDate: new Date(),
        minMode: "year",
        datepickerMode: "year"
    };
    $scope.popup = {
        opened: false
    };
    $scope.openPopup = function () {
        $scope.popup.opened = true;
    };
    $scope.closeAlert = function (index) {
        delete $scope.alert;
    };
}]);

updownControllers.controller('BasicLawUpDownCheckDetailCtrl', ['$scope', '$ngBootbox', '$location', 'BasicLawBasicLaw', 'BasicLawUpDown', 'Upload', 'HOST', 'BasicLawSubsidy', 'Common',
    function ($scope, $ngBootbox, $location, BasicLawBasicLaw, BasicLawUpDown, Upload, HOST, BasicLawSubsidy, Common) {
        $scope.cases = [];
        $scope.batchs;
        $scope.count;
        $scope.remark;

        $scope.totalItems = 0;
        $scope.currentPage = 1;
        $scope.pageSize = 10;
        $scope.caseTemp = JSON.parse($location.search().case);
        $scope.showType = $location.search().showType;
        console.log(JSON.stringify($scope.caseTemp));
        getSelectBoxValue($scope, Common, ['10011']);
        $scope.query = function () {
            var obj = {
                basicSeq: $scope.caseTemp.basicSeq,
                currentPage: $scope.currentPage,
                pageSize: $scope.pageSize
            };
            BasicLawUpDown.managerList(obj).then(function (data) {
                console.log("managerList:" + JSON.stringify(data));
                $scope.cases = data.data;
                $scope.totalItems = data.pageCount * $scope.pageSize;
                $scope.currentPage = data.page;
            });
        };
        $scope.query();
        $scope.export = () => {
            BasicLawUpDown.managerListExport({ basicSeq: $scope.caseTemp.basicSeq, exportFlag: true }).then(function (data) {
                console.log("managerList111:" + JSON.stringify(data));

            });
        };

        $scope.check = function (manager, index, type1, type2) {
            $ngBootbox.customDialog({
                title: '审核是否通过',
                templateUrl: "/html/basiclaw/partials/checkDialog.html",
                show: true,
                backdrop: true,
                animate: true,
                buttons: {
                    success: {
                        label: "OK",
                        className: "btn-primary",
                        callback: function () {
                            var status = $("input[name='checkResult']:checked").val();
                            var reason = $("#checkReason").val();
                            if (!reason && status == '3') {
                                $ngBootbox.alert('请填写审核失败原因！');
                                return;
                            }
                            BasicLawUpDown.upCheck({
                                status: status,
                                reason: reason,
                                manager: manager
                            }).then(function (data) {
                                console.log("data:" + JSON.stringify(data));
                                if (data.err) {
                                    $scope.alert = {
                                        type: 'danger',
                                        message: data.err
                                    };
                                    return;
                                }
                                $scope.cases[index].status = status;
                                //更新状态为已审核
                                var arr = $scope.cases.map(function (item, i) {
                                    if ($scope.cases[i].status != "1") {
                                        return true
                                    } else {
                                        return false
                                    }
                                });
                                // 更新批次的状态不用这儿处理 by lbb
                                //if (arr.indexOf(false) < 0)BasicLaw.updateCheck({_id: $scope.caseTemp._id, status: "3"});
                            })
                        }
                    },
                    warning: {
                        label: "Cancel",
                        className: "btn-default"
                    }
                }
            });
        };
        $scope.import = function () {
            if ($scope.form.agentUpfile.$valid && $scope.file) {
                $scope.upload($scope.file);
                //return $ngBootbox.alert('文件选择成功！');
            } else {
                return $ngBootbox.alert('请选择正确的文件格式!');
            }
        };
        $scope.upload = function (file) {
            let url = '/basiclaw/subsidy/agentUpLoad?yearMonth=' + $scope.caseTemp.yearMonth;
            Upload.upload({
                url: HOST.opr + url,
                data: {
                    file: file,
                    type: $scope.caseTemp[0] && $scope.caseTemp[0]['type'],
                    yearMonth: $scope.caseTemp.yearMonth
                }
            }).then(function (resp) {
                console.log(resp.data.code);
                if (resp.data.code == '999') {
                    $ngBootbox.alert(resp.data.result);
                } else if (resp.data.code == '0') {
                    console.log('resp.data.result', resp.data.result);
                    $scope.batchs = resp.data.result;
                    $scope.count = resp.data.count;
                    $('#batchModal').modal('show');
                }
            });
        };

        $scope.inputBatch = function () {
            BasicLawSubsidy.importUpload({ batchs: $scope.batchs, count: $scope.count, remark: $scope.remark, basicSeq: $scope.caseTemp.basicSeq }).then(function (result) {
                console.log('result.data', result);
                // 导入后本批次为审核中
                BasicLawBasicLaw.updateCheck({ _id: $scope.caseTemp._id, status: "2" });
                $ngBootbox.alert(result);
                setTimeout(2000);
                window.location.reload();
            });
        };

        $scope.managerDetail = function (o) {
            $location.url("/basiclaw/manager/detail?case=" + JSON.stringify(o));
        };
        $scope.goBack = function () {
            window.history.back();
        }
    }]);


updownControllers.controller('BasicLawUpDownManagerDetailCtrl', ['$scope', '$ngBootbox', '$location', 'BasicLawBasicLaw', 'BasicLawUpDown', function ($scope, $ngBootbox, $location, BasicLawBasicLaw, BasicLawUpDown) {
    $scope.case = JSON.parse($location.search().case);
    $scope.cases = [];
    $scope.totalItems = 0;
    $scope.currentPage = 1;
    $scope.pageSize = 20;
    $scope.query = function () {
        var obj = {
            search: {
                year: $scope.case.year,
                quarter: $scope.case.quarter,
                saleman: $scope.case.saleman,
                type: $scope.case.type
            },
            currentPage: $scope.currentPage,
            pageSize: $scope.pageSize
        };
        BasicLawBasicLaw.basicList(obj).then(function (data) {
            console.log('data', data);
            $scope.cases = data.data.docs;
            $scope.countEffective = data.count;
        });
    };
    $scope.query();
    $scope.contractDetail = function (o) {
        var data = {
            _id: o._id,
            cType: $scope.case.type,
            saleman: o.saleman,
            agentName: o.agentId.agentName,
            agentId: o.agentId
        };
        console.log('contractdata:  ', data);
        $location.url("/basiclaw/contract/detail?case=" + JSON.stringify(data));
    };

    $scope.goBack = function () {
        window.history.back();
    }
}]);
