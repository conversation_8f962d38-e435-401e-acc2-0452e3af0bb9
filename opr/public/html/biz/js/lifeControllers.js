var lifeControllers = angular.module('lifeControllers', []);

//0I-投保单录入、0I-投保单修改、0A-承保提交（资料递交）
lifeControllers.controller('LifeEntryCtrl', ['Common', 'HOST', '$uibModal', '$scope', '$window', '$location', 'Upload', '$ngBootbox', '$routeParams', 'Policy', 'insuretype',
    function (Common, HOST, $uibModal, $scope, $window, $location, Upload, $ngBootbox, $routeParams, Policy, insuretype) {
        $scope.altInputFormats = ['yyyy/M!/d!'];
        $scope.idType = idType;
        $scope.relations = relations;
        $scope.checkboxes = checkboxes;
        $scope.insuretypes = insuretype;
        $scope.approveType = approveType;
        $scope.premiumMode = premiumMode;
        $scope.imageArr = [];           //除寿险追踪状态流转影像的其他影像
        $scope.statusImageArr = [];     //寿险追踪状态流转影像
        $scope.search = {};
        $scope.contract = {};
        $scope.coverageArr = [{ premiumMode: '1' }];
        $scope.beneListOrder = ['1'];
        $scope.contractStatus = '';
        $scope.transfer = {};
        $scope.transferStates = transferStates;
        $scope.imageName = '上传问题件';
        $scope.necessary = [];      //必须上传的影像集合
        $scope.uploaded = {};       //已经上传的影像数(包含暂存时已经保存的图片)
        $scope.imgIds = '';
        //定义可选的影像
        $scope.option = {
            '0I': ['138'],
            '0A': ['138', '202']
        };
        $scope.contract.beneList = [{
            order: "1",
            brelations: "1", // 默认法定
        }];
        $scope.contract.insuredList = [{
            prelations: '1'
        }];
        $scope.operateType = "create";
        $scope.orders = beneOrder;

        //初始化
        $scope.init = function () {
            //产品类型
            $scope.contract.contractType = '1';
            $scope.contract.initialChargeMode = '2';
            $scope.contract.renewalChargeMode = '2';
            $scope.contract.premiumMode = '1';
            $scope.contract.applicant = { aidtype: '1' };
            $scope.activeTabNo = $routeParams.activeTabNo || 1;
            $scope.choiceIndex = 0;
            $scope.contract.faceaMoutTotle = 0;
            $scope.contract.totalModalPrem = 0;
            $scope.idType1 = false;
            $scope.contract.reckonType = '1'; //费用计算类型
        };
        $scope.init();

        $scope.checkboxChange = function (index, num) {
            for (var i = 0; i < $scope.contract.beneList.length; i++) {
                if ($scope.contract.beneList[i].checkbox == "1") {
                    $scope.contract.beneList[index].bidtype = $scope.contract.applicant.aidtype;
                    $scope.contract.beneList[index].bidno = $scope.contract.applicant.aidno;
                    $scope.contract.beneList[index].binsurename = $scope.contract.applicant.applicantname;
                    $scope.contract.beneList[index].brelations = undefined;
                } else if ($scope.contract.beneList[i].checkbox == "2") {
                    if ($scope.contract.insuredList && $scope.contract.insuredList.length > 0) {
                        $scope.contract.beneList[index].binsurename = $scope.contract.insuredList[0].insurename;
                        $scope.contract.beneList[index].bidtype = $scope.contract.insuredList[0].pidtype;
                        $scope.contract.beneList[index].bidno = $scope.contract.insuredList[0].pidno;
                        $scope.contract.beneList[index].brelations = '2';
                    }
                } else if (num == '3') {
                    $scope.contract.beneList[index].binsurename = undefined;
                    $scope.contract.beneList[index].bidtype = undefined;
                    $scope.contract.beneList[index].bidno = undefined;
                    $scope.contract.beneList[index].brelations = undefined;
                }
            }
        }

        $scope.init = function () {
            if ($routeParams.id) {
                $scope.operateType = 'update';
                Policy.findContract({ id: $routeParams.id }).then(function (result) {
                    console.log(result);
                    $scope.brightType = result.brightType;
                    var contract = result.contract;
                    $scope.format = 'yyyy-MM-dd HH:mm:ss';
                    $scope.search.operateDate = new Date();
                    if (!contract.insuredList || contract.insuredList.length == 0) {
                        contract.insuredList = [contract.policy];
                    }
                    if (contract.cardInfo && !contract.cardInfo.bankName) {
                        contract.cardInfo.bankName = contract.cardInfo.bankCode;
                    }
                    $scope.contract.history_contractNo = contract.contractNo; //保存原始保单号
                    $scope.postImages = result.postImages;
                    $scope.coverageArr = contract.children;
                    $scope.calc();
                    getImgList($scope);
                    $scope.contractStatus = contract.contractStatus;

                    //重复的状态不显示影像
                    let exist = {};
                    //寿险追踪状态流转影像
                    for (let i in contract.stateTransfers) {
                        let imgType = traceImageType[contract.stateTransfers[i].stateType] || traceImageType['201'];
                        if (imgType && !exist[imgType]) {
                            exist[imgType] = '1';
                            let html = '<div class="form-group" ><label class="col-sm-1 control-label">' + imgType + '</label><ul class="col-sm-11 list-inline">';
                            for (let j in $scope.postImages) {
                                if ($scope.postImages[j].resType == traceImageType[imgType]) {
                                    html += '<li><a href="' + $scope.postImages[j].url + '" target=_blank><img src="' + $scope.postImages[j].url + '" alt="' + decodeURIComponent($scope.postImages[j].name) + '" width="150" height="150"></a></li>';
                                }
                            }
                            html += '</ul></div>';
                            $('#statusForm').append(html);
                        }
                    }
                    loadFileinput([], [], 'useless');
                    loadFileinput([], [], '301');

                    //获取保险公司的影像
                    if (contract.providerId) {
                        Policy.getProviderInfoById(contract.providerId).then(function (result) {
                            $scope.mustImageList = [];
                            if (result.provider)
                                $scope.mustImageList = result.provider.mustImage;
                        })
                    }
                    //获取产品的保险年期选项（safePeriodList），
                    for (let i in contract.children) {
                        let obj = {
                            keyword: contract.children[i].productName,
                            providerId: contract.providerId,
                            productType: contract.contractType,
                            partiesId: contract.partiesId
                        }
                        Policy.findProducts(obj).then(function (result) {
                            if (result.data instanceof Array && result.data.length > 0 && result.data[0].safePeriod) {
                                contract.children[i].safePeriodList = result.data[0].safePeriod;
                            }
                        })
                    }
                    $scope.contract = contract;
                });
            }
        }
        $scope.init();
        $scope.validcontract = function () {
            $scope.tempOne = 0;
            $scope.tempTwo = 0;
            $scope.tempThree = 0;
            return true;
        };
        //保单录入、修改时，判断输入的保单号是否在数据库中已存在
        $scope.judgeContractNo = function (contractNo) {
            if (contractNo && $scope.contract.history_contractNo != contractNo) {
                Policy.judgeContractNo({
                    contractNo: contractNo
                }).then(function (result) {
                    if (result.data.contract) {
                        $ngBootbox.alert('保单号 "' + contractNo + '" 已存在！').then(function () {
                            $scope.contract.contractNo = $scope.contract.history_contractNo;
                        });
                    }
                });
            }
        };
        //暂存、资料递交       submitType:提交类型，
        $scope.addContract = function (status, submitType) {
            if (!$scope.contract.providerId || !$scope.contract.providerCode) {
                return $ngBootbox.alert("保险公司id或保险公司名称不能为空！");
            }
            if (!$scope.contract.partiesId || !$scope.contract.partiesName) {
                return $ngBootbox.alert("签约方id或签约方名称不能为空！");
            }
            if (!$scope.contract.approveDate && status != '0I')
                return $ngBootbox.alert("投保日期不能为空！");
            let approveDate = moment($scope.contract.approveDate).format('YYYY-MM-DD');
            let effectiveDate = moment($scope.contract.effectiveDate).format('YYYY-MM-DD');
            if (approveDate > effectiveDate)
                return $ngBootbox.alert("起保日期不能早于投保日期！");
            if (!$scope.contract.approveType) return $ngBootbox.alert("投保方式不能为空！");
            if (!$scope.contract.saleman) return $ngBootbox.alert("业务员手机不能为空！");

            //产品不能重复购买
            let products = [];
            let riderCount = 0; // 主险个数
            //商品验证
            for (let i = 0; i < $scope.coverageArr.length; i++) {
                if (!$scope.coverageArr[i].productId || !$scope.coverageArr[i].productName) {
                    return $ngBootbox.alert("商品id或商品名称不能为空！");
                }
                if (status == '0A') {
                    if ($scope.coverageArr[i].faceaMout != 0 && !$scope.coverageArr[i].faceaMout)
                        return $ngBootbox.alert("保险金额不能为空！");
                }
                if ($scope.coverageArr[i].totalModalPrem != 0 && !$scope.coverageArr[i].totalModalPrem)
                    return $ngBootbox.alert("保费不能为空！");
                if (!$scope.coverageArr[i].premiumPeriod && !$scope.coverageArr[i].safePeriod)
                    return $ngBootbox.alert("保险年期不能为空！");
                if (!$scope.coverageArr[i].payfeePeriod) return $ngBootbox.alert("缴费年期不能为空！");
                if (products.includes($scope.coverageArr[i].productId)) {
                    return $ngBootbox.alert(`产品【${$scope.coverageArr[i].productName}】不能重复购买！`);
                }
                if ($scope.coverageArr[i].planRiderType == '1') riderCount++;
                products.push($scope.coverageArr[i].productId);
            }
            //提交时验证保单号
            if ($scope.contract.contractNo) {
                let str = /^[\d\w\-_]{1,}$/;
                if (!str.test($scope.contract.contractNo)) {
                    return $ngBootbox.alert('保单号格式不正确,只能是数字、字母、下划线、横杆');
                }
            }
            if (!$scope.validcontract()) return;


            if (!$('#images').html()) {
                uploadImages($scope, loadFileinput);
            }

            //判断必选影像是否都上传完成
            let isUploadSuccess = true;
            for (let o in $scope.necessary) {
                let temp = $scope.necessary[o];
                if (!$scope.uploaded[temp]) {
                    isUploadSuccess = false;
                }
            }
            //资料递交时验证证件是否全部上传
            if (status == '0A' && submitType != '暂存') {
                if (!isUploadSuccess) {
                    return $ngBootbox.alert("证件资料不齐全，请先上传！");
                }
            }
            //提交到待承保
            if (status == '0') {
                if (!$scope.contract.effectiveDate) {
                    return $ngBootbox.alert("起保日期不能为空！");
                }
                if (!isUploadSuccess) {
                    return $ngBootbox.alert("请先上传保单影像！");
                }
            }

            //提交时验证保单信息
            if (status == '0' || status == '0A') {
                if (!$scope.contract.applicant.aidno) return $ngBootbox.alert("投保人证件号码不能为空！");
                if (!$scope.contract.applicant.applicantname) return $ngBootbox.alert("投保人姓名不能为空！");
                if (!$scope.contract.applicant.contractmobile) return $ngBootbox.alert("投保人手机不能为空！");
                if (!$scope.contract.applicant.address) return $ngBootbox.alert("投保人联系地址不能为空！");

                for (let i in $scope.contract.insuredList) {
                    if (!$scope.contract.insuredList[i].insurename) return $ngBootbox.alert("被保人姓名不能为空！");
                    if (!$scope.contract.insuredList[i].pidtype) return $ngBootbox.alert("被保人证件类型不能为空！");
                    if (!$scope.contract.insuredList[i].pidno) return $ngBootbox.alert("被保人证件号不能为空！");
                }

                for (let i in $scope.contract.beneList) {
                    if ($scope.contract.beneList[i].brelations != '1') {
                        if (!$scope.contract.beneList[i].brelations) return $ngBootbox.alert("受益人与被保人关系不能为空！");
                        if (!$scope.contract.beneList[i].binsurename) return $ngBootbox.alert("受益人姓名不能为空！");
                        if (!$scope.contract.beneList[i].bidtype) return $ngBootbox.alert("受益人证件类型不能为空！");
                        if (!$scope.contract.beneList[i].bidno) return $ngBootbox.alert("受益人证件号不能为空！");
                        if (!$scope.contract.beneList[i].insuretype) return $ngBootbox.alert("受益人受益类型不能为空！");
                        if (!$scope.contract.beneList[i].percent) return $ngBootbox.alert("受益人受益人占比不能为空！");
                    }
                }
                if (!$scope.contract.cardInfo) return $ngBootbox.alert("续期信息不能为空！");
                if (!$scope.contract.cardInfo.bankName) return $ngBootbox.alert("续期信息-开户行不能为空！");
                if (!$scope.contract.cardInfo.cardNo) return $ngBootbox.alert("续期信息-卡号不能为空！");

                if (status == '0') {
                    if (!$scope.contract.notificType) return $ngBootbox.alert("请选择客户告知书类型！");
                    if ($scope.contract.notificType == 'P') {
                        let img138 = _.find($scope.imageArr, {resType: "138"});
                        let pig138 = _.find($scope.postImages, {resType: "138"});
                        if (!img138 && !pig138) return $ngBootbox.alert("请先完成纸质告知书上传再提交！");
                    }
                }
            }
            $scope.contract.contractStatus = status;
            //暂存标识
            if (submitType) {
                $scope.contract.submitType = submitType;
            }
            var obj = {
                contract: $scope.contract,
                images: $scope.imageArr,
                coverages: $scope.coverageArr
            };
            if (riderCount > 1) {
                $ngBootbox.confirm('存在多个主险，请确认是否提交？').then(function(){
                    Policy.addContract('life', obj).then(function (result) {
                        handleForward(result);
                    })
                })
            } else {
                Policy.addContract('life', obj).then(function (result) {
                    handleForward(result);
                })
            }

            // 处理响应后跳转
            function handleForward(result){
                if (result.err) {
                    return $ngBootbox.alert("提交失败！" + JSON.stringify(result.msg));
                }

                $ngBootbox.alert("提交成功！").then(function () {
                    if (status == '0A' || status == '0')
                        $window.history.back();
                    else
                        $location.url('biz/query');
                });
            }
        };

        $scope.calc = function () {
            //$scope.contract.agencyfee = _.sumBy($scope.coverageArr, "agencyfee");
            $scope.contract.commissionPrem = _.sumBy($scope.coverageArr, "commissionPrem");
            $scope.contract.faceaMoutTotle = _.sumBy($scope.coverageArr, "faceaMout");
            $scope.contract.totalModalPrem = _.sumBy($scope.coverageArr, "totalModalPrem");
        };

        $scope.addCoverage = function () {
            $scope.coverageArr.push({ premiumMode: '1' });
        };
        $scope.deleteCoverage = function (index) {
            if ($scope.coverageArr.length == 1) return;
            $scope.coverageArr.splice(index, 1);
        };
        //添加被保人
        $scope.addInsuredList = function () {
            $scope.contract.insuredList.push({
                prelations: '1'
            });
            $scope.imageArr = [];
            $('#images').html('');
            $scope.uploaded = {};
        };
        //删除被保人
        $scope.deleteInsuredList = function (index) {
            if ($scope.contract.insuredList.length == 1) return;
            $scope.contract.insuredList.splice(index, 1);
            $scope.imageArr = [];
            $('#images').html('');
            $scope.uploaded = {};
        };
        //添加受益人
        $scope.addBeneList = function () {
            $scope.contract.beneList.push({
                order: $scope.beneListOrder[$scope.beneListOrder.length - 1]
            });
        };
        //删除受益人
        $scope.deleteBeneList = function (index) {
            if ($scope.contract.beneList.length == 1) return;
            $scope.contract.beneList.splice(index, 1);
        }

        //获取费率
        $scope.getRate = function (i) {
            let coverage = $scope.coverageArr[i];
            if (!$scope.contract.agentType) {
                return $ngBootbox.alert('请选择业务员!');
            }
            var productName = coverage.productName;
            if (!productName) {
                return $ngBootbox.alert('请选择产品!');
            }
            if ((coverage.safePeriodList && !coverage.safePeriod) || (!coverage.safePeriodList && !coverage.premiumPeriod)) {
                return $ngBootbox.alert('保险年期不能为空!');
            }
            var productCode = productName.split(':');

            var reqObj = {
                contractType: $scope.contract.contractType,
                payfeePeriod: coverage.payfeePeriod,
                branch: $scope.contract.branch,
                safePeriod: coverage.safePeriod,
                agentType: $scope.contract.agentType,
                productCode: [productCode[0]],
                partiesId: $scope.contract.partiesId,
                startDate: $scope.contract.approveDate
            };
            Policy.getFeeRate(reqObj).then(function (result) {
                console.log(result);
                if (result.err) {
                    return $ngBootbox.alert('获取费率出错!' + result.msg);
                }
                var rate = result.list[0];
                coverage.commissionRate = rate.commissionRate;

                //手续费费用tab页的数据
                if (rate.feeList && rate.feeList.length > 0) {
                    coverage.feeList = [];
                    $scope.contract.agencyfee = $scope.contract.agencyfee || 0;
                    for (let j in rate.feeList) {//totalModalPrem
                        let agencyfee = rate.feeList[j].agencyFeeRate * coverage.totalModalPrem / 100;
                        $scope.contract.agencyfee += agencyfee;     //总手续费
                        let fee = {
                            feeType: rate.feeList[j].feeType,
                            feeRate: rate.feeList[j].agencyFeeRate,
                            amount: agencyfee
                        }
                        coverage.feeList.push(fee);
                    }
                }
            });
        };

        //手续费费用tab页，计算手续费和手续费率  type: 0、手续费率    1、手续费
        $scope.calcFee = function (type, coverage, index) {
            let msg = '';
            if (type == '0') {
                if (coverage.feeList[index].feeRate > 100 || coverage.feeList[index].feeRate < 0) {
                    coverage.feeList[index].amount = 0;
                    coverage.feeList[index].feeRate = 0;
                    msg = '手续费率必须是0-100的数字！';
                }
                coverage.feeList[index].amount = coverage.feeList[index].feeRate * coverage.totalModalPrem / 100;
            } else if (type == '1') {
                if (coverage.feeList[index].amount > coverage.totalModalPrem || coverage.feeList[index].amount < 0) {
                    coverage.feeList[index].amount = 0;
                    coverage.feeList[index].feeRate = 0;
                    msg = '手续费不能大于保费或小于0！';
                }
                coverage.feeList[index].feeRate = coverage.feeList[index].amount * 100 / coverage.totalModalPrem;
            }
            $scope.contract.agencyfee = 0;
            for (let i in $scope.coverageArr) {
                $scope.contract.agencyfee += _.sumBy($scope.coverageArr[i].feeList, "amount");
            }
            if (msg) return $ngBootbox.alert(msg);
        }

        //获取业务员信息
        $scope.getAgentInfo = function (saleman) {
            if (!saleman) return;
            Common.getBrokerInfo(saleman).then(function (result) {
                var data = result.list;
                var some_html = '<div class="alert alert-danger fade in"><label>未签约或不存在对应的业务员</label></div>';
                //HB-2227: 不再验证渠道。
                if (data && data.aptitudeFlag == '1' && (data.agentStatus == '2')) {
                    $scope.contract.agentId = data._id;
                    $scope.contract.agentName = data.agentName;
                    $scope.contract.userCode = data.userCode;
                    $scope.contract.saleman = data.saleman;
                    $scope.contract.branch = data.branchCode;
                    $scope.contract.channelType = data.channelType;
                    $scope.contract.agentType = data.agentType;
                    $scope.contract.recommCode = data.recommCode;
                    $scope.contract.recommName = data.recommName;
                } else {
                    $scope.contract.agentId = "";
                    $scope.contract.agentName = "";
                    $scope.contract.userCode = "";
                    $scope.contract.branch = "";
                    $scope.contract.channelType = "";
                    $scope.contract.agentType = "";
                    $scope.contract.saleman = "";
                    $scope.contract.recommCode = "";
                    $scope.contract.recommName = "";
                    $ngBootbox.alert(some_html);
                }
            })
        };

        //产品变更--合计保险金额、合计保费清0
        $scope.coverageChange = function () {
            $scope.contract.faceaMoutTotle = 0;
            $scope.contract.totalModalPrem = 0;
        };

        $scope.idtypeChange = function () {
            $scope.checkboxChange();
            if ($scope.contract.applicant.aidtype == "1") {
                $scope.idType1 = true;
            } else {
                $scope.idType1 = false;
            }
        };

        //被保人与投保人关系
        $scope.relationsChange = function (index) {
            var relationsValue = $scope.contract.insuredList[index].prelations;
            var idno = '',
                idType = '',
                applicantname;
            if (relationsValue == '2') {
                idno = $scope.contract.applicant.aidno;
                idType = $scope.contract.applicant.aidtype;
                applicantname = $scope.contract.applicant.applicantname;
            }
            $scope.contract.insuredList[index].pidno = idno;
            $scope.contract.insuredList[index].pidtype = idType;
            $scope.contract.insuredList[index].insurename = applicantname;
            $scope.imageArr = [];
            $('#images').html('');
            $scope.uploaded = {};
        }

        //受益人与被保人关系
        $scope.brelationsChange = function (index) {
            var relationsValue = $scope.contract.beneList[index].brelations;
            var idno = '',
                idType = '',
                insurename = '';
            if (relationsValue == '2') {
                //被保人只有1个，选择本人的时候才同步
                if ($scope.contract.insuredList.length == 1) {
                    idno = $scope.contract.insuredList[0].pidno;
                    idType = $scope.contract.insuredList[0].pidtype;
                    insurename = $scope.contract.insuredList[0].insurename;
                }
                $scope.contract.beneList[index].bidno = idno;
                $scope.contract.beneList[index].bidtype = idType;
                $scope.contract.beneList[index].binsurename = insurename;
            }
        };

        //身份证验证
        $scope.idnoBlur = function () {
            var idno = $scope.contract.applicant.aidno;
            if (!idno) return;
            if ($scope.contract.applicant.aidtype == '1') {
                if (isCardNo(idno)) {
                    var cardinfo = cardToInfo(idno);
                    $scope.contract.applicant.asex = cardinfo[1];
                    $scope.contract.applicant.abirthday = new Date(cardinfo[0]);
                } else {
                    $scope.contract.applicant.aidno = "";
                    var some_html = '<br><div class="alert alert-danger fade in"><label>身份证格式不正确，或最后一位字母不是大写！</label></div>';
                    $ngBootbox.alert(some_html);
                }
            }
        };
        //被保人身份证号验证
        $scope.pidnoBlur = function (index) {
            var insureInfo = $scope.contract.insuredList[index];
            if (!insureInfo.pidno) return;
            if (insureInfo.pidtype == '1') {
                if (!isCardNo(insureInfo.pidno)) {
                    $scope.contract.insuredList[index].pidno = "";
                    var some_html = '<br><div class="alert alert-danger fade in">';
                    some_html += '<label>身份证格式不正确，或最后一位字母不是大写！</label></div>';
                    bootbox.alert(some_html);
                }
            }
        };
        //受益人身份证号验证
        $scope.bidnoBlur = function (index) {
            var insureInfo = $scope.contract.beneList[index];
            if (!insureInfo.bidno) return;
            if (insureInfo.bidtype == '1') {
                if (!isCardNo(insureInfo.bidno)) {
                    $scope.contract.beneList[index].bidno = '';
                    var some_html = '<br><div class="alert alert-danger fade in">';
                    some_html += '<label>身份证格式不正确，或最后一位字母不是大写！</label></div>';
                    bootbox.alert(some_html);
                }
            }
        };
        //预览tab页设置为只读
        $scope.readonlyFn = function () {
            angular.element('#preview p').prop('readonly', true);
            angular.element('#preview select,#preview input').prop('disabled', true);
        }

        /**
         * 影像上传
         * @param {*} initialPreview 初始化图片
         * @param {*} initialPreviewConfig 初始化图片配置
         * @param {*} id 图片类型：301-寿险追踪状态影像,查看详情请移驾
         */
        function loadFileinput(initialPreview, initialPreviewConfig, id) {
            $scope.imgIds = $scope.imgIds ? $scope.imgIds + ',#' + id : '#' + id;
            let maxFileCount, fileType, showUpload;
            if (id == '301') {
                maxFileCount = 10;
                fileType = ["jpg", "png", "gif", 'jpeg', 'docx', 'doc', 'pdf', 'tif'];
                showUpload = true;
            } else {
                maxFileCount = 1;
                fileType = ["jpg", "png", "gif", 'jpeg', 'tif', 'pdf'];
                showUpload = false;
            }
            $("#" + id).fileinput({
                language: 'zh',
                uploadUrl: "/biz/contract/uploadFile",
                allowedFileExtensions: fileType,
                initialPreview: initialPreview,
                maxFileCount: maxFileCount,
                validateInitialCount: true,
                msgFilesTooMany: "选择上传的文件数量 超过允许的最大数值！",
                initialPreviewConfig: initialPreviewConfig,
                browseOnZoneClick: true,
                maxFileSize: 100000,
                resizePreference: 'height',
                overwriteInitial: false,
                uploadLabel: "上传",
                browseLabel: "选择图片",
                dropZoneTitle: "点击",
                dropZoneClickTitle: "选择图片",
                browseClass: "btn btn-primary",
                showCaption: false,
                resizeImage: true,
                showRemove: false,
                showClose: false,
                showUpload: showUpload,
                isClear: true   //在源码(3066行)中新增的属性，调用“refresh”时是否删除初始化图片
            })//上传完成
                .on('fileuploaded', function (event, data, previewId, index) {
                    let image = data.response;
                    image.resType = id;
                    if (id == '301') {
                        let isExist = _.findIndex($scope.statusImageArr, function (o) {
                            return o.resType == id;
                        });
                        if (isExist != -1) return;
                        $scope.statusImageArr.push(image);
                    } else {    //其他影像,回执影像(202)是可选的，不统计
                        let isExist = _.findIndex($scope.imageArr, function (o) {
                            return o.resType == id;
                        });
                        if (isExist != -1) return;
                        if (id != '202') {
                            $scope.uploaded[id] = '1';
                        }
                        $scope.imageArr.push(image);
                    }
                })//图片初始化后，点击删除按钮
                .on('filedeleted', function (event, key, previewId, data) {
                    _.remove($scope.imageArr, function (n) {
                        return n.resType == id;
                    })
                    _.remove($scope.postImages, function (n) {
                        return n.resType == id;
                    })
                    delete $scope.uploaded[id];
                })//上传完成后，点击删除按钮
                .on('filesuccessremove', function (event, previewId) {
                    if (id == '301') {
                        _.remove($scope.statusImageArr, function (n) {
                            return n.resType == id;
                        })
                    } else {
                        _.remove($scope.imageArr, function (n) {
                            return n.resType == id;
                        })
                        delete $scope.uploaded[id];
                    }
                }).on('change', function () {
                    _.remove($scope.imageArr, function (n) {
                        return n.resType == id;
                    });
                    delete $scope.uploaded[id];
                    $('#' + id).fileinput('refresh');
                });
        }

        //投保方式change
        $scope.approveTypeChange = function () {
            $scope.imageArr = [];
            $('#images').html('');
            $scope.uploaded = {};
            if ($scope.contract.approveType == '6') {
                $ngBootbox.alert('运营录入，不能选择此种投保方式！');
                $scope.contract.approveType = "";
            }
        };

        //资料递交--状态变更
        $scope.stateChange = function () {
            var remark = stateFObj[$scope.search.state];
            $scope.search.remark = remark.replace('startDate', moment($scope.search.operateDate).format('YYYY/MM/DD'));
        };


        $scope.closeAlert = function (index) {
            delete $scope.alert;
        };
        //tab切换
        $scope.chooseTab = function (activeTabNo) {
            let approveDate = moment($scope.contract.approveDate).format('YYYY-MM-DD');
            let effectiveDate = moment($scope.contract.effectiveDate).format('YYYY-MM-DD');
            if (approveDate > effectiveDate)
                return $ngBootbox.alert("起保日期不能早于投保日期！");

            $scope.activeTabNo = activeTabNo;
            //承保提交5：影像，保单录入、修改6:影像    9:配送信息
            if (activeTabNo == '5' || activeTabNo == '6' || activeTabNo == '9') {
                if (!$('#images').html()) {
                    uploadImages($scope, loadFileinput);
                }
            }
            if (activeTabNo == '12') {
                $('#previewImg').html('');
                let images = $scope.imageArr.length > 0 ? $scope.imageArr : $scope.postImages || [];
                //显示影像
                Common.showImages(images, '#previewImg');
            }
        };

        //资料递交--新增寿险流转状态
        $scope.saveState = function () {
            if ($scope.transfer.state.length <= 0) return $ngBootbox.alert('请选择状态！');
            if (!$scope.transfer.statusStartDate) return $ngBootbox.alert('状态开始日期不能为空！');
            if (!$scope.transfer.remark) return $ngBootbox.alert('状态详情不能为空！');
            if ($scope.transfer.statusStartDate > new Date()) {
                return $ngBootbox.alert('状态开始日期不能大于当前时间！');
            }
            //设置流转状态影像类型
            for (let i in $scope.statusImageArr) {
                if ($scope.transfer.state.length == 1) {
                    $scope.statusImageArr[i].resType = traceImageType[$scope.transfer.state[0].value];
                } else if ($scope.transfer.state.length == 2 && ($scope.transfer.state[0].value == '待扣款' && $scope.transfer.state[1].value == '待核保')) {
                    $scope.statusImageArr[i].resType = '313';
                } else if ($scope.transfer.state.length == 2 && ($scope.transfer.state[0].value == '待核保' && $scope.transfer.state[1].value == '待扣款')) {
                    $scope.statusImageArr[i].resType = '312';
                } else {
                    $scope.statusImageArr[i].resType = '314';
                }
            }
            //不予承保比较特殊，是保单状态
            for (let i in $scope.transfer.state) {
                if ($scope.transfer.state[i].key == '14' && $scope.transfer.state.length > 1) {
                    return $ngBootbox.alert('不予承保只能单选！');
                } else if ($scope.transfer.state[i].key == '14' && $scope.contract.contractStatus == '1') {
                    return $ngBootbox.alert('保单已承保，不能操作不予承保！');
                }
            }
            let contract = {
                id: $scope.contract._id,
                approveDate: $scope.contract.approveDate,
                transfer: $scope.transfer,
                images: $scope.statusImageArr,
                saleman: $scope.contract.saleman
            }
            Policy.saveState(contract).then(function (data) {
                $window.location.reload();
            });
        };

        //angularjs-dropdown-multiselect---多选下拉框控件
        $scope.settings = { displayProp: 'value' };
        $scope.transfer.state = [];
        $scope.onSelectionChanged = { 'onSelectionChanged': stateChange };
        $scope.translation = { checkAll: '全选', uncheckAll: '取消' };

        //状态改变，修改文本
        function stateChange() {
            let state = $scope.transfer.state;
            $scope.transfer.remark = '';
            for (let i = 0; i < state.length; i++) {
                $scope.transfer.remark += stateInfo[state[i].key];
            }
            $scope.imageName = '上传问题件';
            if (state.length == 1) {
                if (state[0].value == '待核保' || state[0].value == '待扣款')
                    $scope.imageName = '上传问题件处理完成文件';
                else if (state[0].value == '不予承保') {
                    $scope.imageName = '上传不予承保影像';
                }
            }
        };

        //资料递交--退回
        $scope.untread = function () {
            Policy.untread({
                id: $scope.contract._id,
                opinion: $scope.contract.opinion
            }).then(function (result) {
                $ngBootbox.alert(result.msg).then(function () {
                    $window.history.back();
                });
            });
        };
        //上传全部影像
        $scope.allImgUpload = function () {
            $($scope.imgIds).fileinput('upload');
        }

        $scope.popup = {
            opened1: false, //投保日期
            opened2: false, //起保日期
            opened3: false, //状态开始日期
            opened7: false,
            opened8: false,
        };
        $scope.openPopup = function (num) {
            $scope.popup['opened' + num] = true;
        };

        getSelectBoxValue($scope, Common, ['10013', '10014'], $ngBootbox);
    }
]);

//寿险查看详情、寿险承保
lifeControllers.controller('LifeDetailCtrl', ['$scope', '$ngBootbox', '$timeout', '$uibModal', 'storage', 'idType', 'relations', 'Policy', '$routeParams', 'approvedReasons', 'insuretype', 'accountStatus', 'settleStatus', 'payStatus', 'untreadStatus', 'Common',
    function ($scope, $ngBootbox, $timeout, $uibModal, storage, idType, relations, Policy, $routeParams, approvedReasons, insuretype, accountStatus, settleStatus, payStatus, untreadStatus, Common) {
        $scope.altInputFormats = ['yyyy/M!/d!'];
        $scope.idType = idType;
        $scope.relations = relations;
        $scope.approvedReasons = approvedReasons;
        $scope.insuretypes = insuretype;
        $scope.imageArr = [];
        $scope.rereason = [];
        $scope.contract = {};
        $scope.coverageArr = [];
        $scope.activeTabNo = 1;
        $scope.choiceIndex = 0;
        $scope.operateType = $routeParams.type;
        $scope.accountStatus = accountStatus;
        $scope.settleStatus = settleStatus;
        $scope.payStatus = payStatus;
        $scope.untreadStatus = untreadStatus;
        $scope.incomePayType = incomePayType;
        $scope.status = '0A';
        $scope.isUploadSuccess = true;
        $scope.contractId = $routeParams.id;
        $scope.search = {};
        $scope.orders = [{
            key: "1",
            value: "壹"
        }, {
            key: "2",
            value: "贰"
        }, {
            key: "3",
            value: "叁"
        }];

        /*  angularjs-dropdown-multiselect---控件--start    */
        $scope.settings = { displayProp: 'value', scrollable: false };
        $scope.translation = { checkAll: '全选', uncheckAll: '取消' };
        $scope.onSelectionChanged = { 'onSelectionChanged': onChange };
        $scope.initStyle = function () {
            Common.initStyle();
        }
        /*  angularjs-dropdown-multiselect---控件--end    */

        function onChange() {
            if ($scope.rereason && $scope.rereason.length > 0) {
                $('#opinion').prop('readonly', true);
                let indexes = [];
                let reasons = [];
                for (let i = 0; i < $scope.rereason.length; i++) {
                    indexes.push($scope.rereason[i].key);
                    reasons.push($scope.rereason[i].value);
                };
                $scope.reindex = indexes.join(",");
                $scope.contract.opinion = reasons.join(";");
                $('#opinion').val(reasons.join("；\n"));
            } else {
                $('#opinion').prop('readonly', true);
                $('#opinion').val('');
                $scope.reindex = '';
            }
            if ($scope && $scope.rereason.length == 1 && $scope.rereason[0].value == "其它") {
                $('#opinion').prop('readonly', false);
                $('#opinion').val('');
            };
        };

        //获取原始费率
        $scope.getOriginalRate = function (id) {
            Policy.getOriginalRate({ id: id }).then(function (result) {
                if (result.data.code == '999') return;
                $scope.rateDesc = result.data.msg;
            });
        }

        Policy.findContract({ id: $scope.contractId, flag: $routeParams.flag }).then(function (result) {
            $scope.brightType = result.brightType;
            var contract = result.contract;
            if (!contract.insuredList || contract.insuredList.length == 0) {
                contract.insuredList = [contract.policy];
            }

            if ($scope.operateType == 'approved') {
                $scope.getOriginalRate($scope.contractId);
            }
            if (contract.coverages) {
                contract.coverages.forEach(function (item) {
                    item.name = item.name.replace(/<br\/>/g, "");
                });
            }
            $scope.postImages = result.postImages;
            $scope.coverageArr = contract.children;
            $scope.token = storage.local.getValue("jwtToken");
            $scope.claimArr = result.claimArr;
            contract.nextPayDate = moment(contract.effectiveDate).add(contract.paymentYear, 'year').format('YYYY-MM-DD');
            //显示影像
            Common.showImages($scope.postImages, '#images');
            Common.showImages($scope.postImages, '#previewImg');

            //获取保单保全记录
            var htm = "";
            for (let i = 0; i < $scope.postImages.length; i++) {
                htm += "<li class='linone' ng-repeat='image in postImages'><img src='" + $scope.postImages[i].url + "' alt='" + $scope.postImages[i].abbrName + "' height='150px' width='200px'></li>";
            }
            $(".images").append(htm);
            $('#image').viewer();
            Policy.getPolicyCopyList({ contractNo: contract.contractNo }).then(function (result) {
                $scope.policyList = result.data.policyList;
            });

            //电子保单下载
            Policy.elec_download(contract).then(function (result) {
                $scope.contract.elec_file = result.url;
            });

            //获取保险公司的影像
            if (contract.providerId) {
                Policy.getProviderInfoById(contract.providerId).then(function (result) {
                    if (result.provider) {
                        let mustImageList = result.provider.mustImage;
                        let mustImage = mustImageList[contract.approveType] || [];
                        //（判断“客户告知书”是否需要上传）
                        if (mustImage.indexOf('138') != -1) {
                            for (let i in $scope.postImages) {
                                //（判断“客户告知书”是否已经上传）
                                if ($scope.postImages[i].resType == '138') {
                                    $scope.isUploadSuccess = false;
                                    break;
                                }
                            }
                        } else {
                            $scope.isUploadSuccess = false;
                        }
                    }
                })
            }
            //查询寿险保单续期最新年度险种信息
            let paymentYear = contract.paymentYear || 1;
            Policy.findRenewal({ contractId: $scope.contractId, paymentYear: paymentYear }).then(result => {
                let renewal = result.data.renewal;
                if (renewal) {
                    console.log(JSON.stringify(renewal.children));
                    $scope.coverageArr = renewal.children;
                    contract.faceaMoutTotle = renewal.faceaMoutTotle;
                    contract.totalModalPrem = renewal.totalModalPrem;
                    contract.payfeePeriod = renewal.payfeePeriod;
                    contract.agencyfee = renewal.agencyfee;
                    contract.commissionPrem = renewal.commissionPrem;
                }
                $scope.histlist = result.data.histlist || []
            });
            $scope.search.contractNo = contract.contractNo;
            // 实名校验转换成中文
            if(contract.applicant.realFlag == false) {
                contract.applicant.realFlag = '不通过'
            } else if(contract.applicant.realFlag == true) {
                contract.applicant.realFlag = '通过'
            }

            $scope.queryIncomePay();
            $scope.contract = contract;
        });

        //承保    submitType：1-承保、2-提交承保与回销
        $scope.approved = function (submitType) {
            if (!$scope.contract.applicationNo) {
                return $ngBootbox.alert('投保单号不能为空！');
            }
            if (!$scope.contract.contractNo) {
                return $ngBootbox.alert('保单号不能为空！');
            }
            if (!$scope.contract.acceptDate) {
                return $ngBootbox.alert('承保日期不能为空！');
            }
            if (moment($scope.contract.acceptDate).format('YYYY-MM-DD') > moment().format('YYYY-MM-DD')) {
                return $ngBootbox.alert('承保日期不能大于当前日期！');
            }
            if (submitType == '2' && $scope.isUploadSuccess) {
                return $ngBootbox.alert('请先退回到“资料递交”或“初始状态”，上传客户通知书后再提交！');
            }
            $scope.contract.submitType = submitType;
            Policy.contractApproved($scope.contract).then(function (result) {
                $ngBootbox.alert(result.data.msg).then(function () {
                    $scope.goBack();
                });
            });
        };

        //回退(初始状态：0I, 资料递交状态：0A)
        $scope.untread = function () {
            Policy.untread({
                id: $scope.contract._id,
                opinion: $scope.contract.opinion,
                indexes: $scope.reindex,
                contractStatus: $scope.status
            }).then(function (result) {
                $ngBootbox.alert(result.msg).then(function () {
                    $scope.goBack();
                });
            });
        };

        $scope.queryIncomePay = function () {
            //查询保单收入/支出
            Policy.incomePay($scope.search).then(result => {
                $scope.incomePay = result.incomePay;
            });
        }
        //预览tab页设置为只读,隐藏“新增”、“删除”等按钮
        $scope.readonlyFn = function () {
            angular.element('#preview p').prop('readonly', true);
            angular.element('#preview select,#preview input').prop('disabled', true);
            angular.element("#preview a[class^='btn'], #preview button").hide();
        }

        $scope.goBack = function () {
            window.history.back();
        };

        $scope.chooseTab = function (activeTabNo) {
            $scope.activeTabNo = activeTabNo;
        };
        $scope.closeAlert = function () {
            delete $scope.alert;
        };

        $scope.copy_content = function(oElement, value) {
            copyToClipboard(oElement, value);
            growl.addSuccessMessage("已复制到剪切板", {ttl: bpo_prompt.success});
        };
        function copyToClipboard(oElement, value) {
            var aux = document.createElement("input");
            if (oElement) {
                var content = oElement.innerHTML || oElement.value;
            }
            var _content = value || content;
            aux.setAttribute("value", _content);
            document.body.appendChild(aux);
            aux.select();
            document.execCommand("Copy");
            document.body.removeChild(aux);
        }

        $scope.popup = {
            opened1: false, //投保日期
            opened2: false, //承保日期
        };
        $scope.openPopup = function (num) {
            $scope.popup['opened' + num] = true;
        };
    }
]);

//承保提交列表(寿险)
lifeControllers.controller('PolicySubmitCtrl', ['$scope', '$rootScope', '$location', '$window', '$ngBootbox', 'channelType', 'Auth', 'Policy', 'productType', 'Common', 'contractStatusSpecial',
    function ($scope, $rootScope, $location, $window, $ngBootbox, channelType, Auth, Policy, productType, Common, contractStatusSpecial) {

        //从$rootScope获取保存的search条件
        let historySearch = Common.searchSave($scope, $rootScope, $location);
        $scope.search = historySearch.search;

        $scope.altInputFormats = ['yyyy/M!/d!'];
        $scope.totalItems = 0;
        $scope.currentPage = 1;
        $scope.pageSize = 10;
        $scope.contracts = [];
        $scope.channelTypes = channelType;
        $scope.deleteData = { withdrawDate: new Date() }; //要作废的保单
        $scope.productTypeArray = productType;
        $scope.contractStatusSpecial = contractStatusSpecial;

        $scope.search.contractStatus = '0A';
        $scope.query = function () {
            $scope.search.currentPage = $scope.currentPage;
            $scope.search.pageSize = $scope.pageSize;
            //每次点击查询按钮，把当前所有查询条件保存到$rootScope中
            $rootScope.search[historySearch.urlKey] = $scope.search;
            Policy.contractEdit($scope.search).then(function (data) {
                var contracts = data.list.docs;
                //处理被保人列表显示
                contracts.forEach(function (contract) {
                    if (contract.insuredList && contract.insuredList.length > 0) {
                        contract.policy = contract.insuredList[0];
                    }
                });
                $scope.contracts = contracts;
                $scope.totalItems = data.list.total;
            });
        };
        $scope.query();

        //显示模态框
        $scope.remind = function (obj) {
            $scope.deleteData.id = obj._id;
            $scope.deleteData.channelType = obj.channelType;
            $('#remarkModal').modal('toggle');
        };
        $('#remarkModal').on('hidden.bs.modal', function () {
            $scope.deleteData.remark = '';
        });
        //撤件
        $scope.delete = function () {
            if (!$scope.deleteData.withdrawDate) return $ngBootbox.alert('撤件时间不能为空！');
            if (!$scope.deleteData.remark && $scope.deleteData.channelType == 'GRQD:个人渠道')
                return $ngBootbox.alert('个人渠道，撤件原因不能为空！');
            $ngBootbox.confirm('你确定撤件此保单吗？').then(function () {
                $scope.deleteData.type = '1';
                Policy.delete($scope.deleteData).then(function (data) {
                    $('#remarkModal').modal('toggle');
                    $ngBootbox.alert(data.msg).then(function () {
                        $scope.query();
                    });
                });
            });
        }

        //获取选择框的值
        getSelectBoxValue($scope, Common, ['10001', '10002', '10005']);
        //日期控件
        $scope.popup = {
            opened1: false, //撤件时间
        };
        $scope.openPopup = function (num) {
            $scope.popup['opened' + num] = true;
        };
        getSelectBoxValue($scope, Common, ['10011','10012']);
    }
]);

/**
 * @method      获取当前选中的保险公司所需要的证件资料（影像)
 *              providerCode：保险公司代码
 *              $scope.contract.approveType：投保方式
 */
function getImgList($scope) {
    if ($scope.contract.providerCode) {
        $scope.imgList = $scope.mustImageList[$scope.contract.approveType] || [];
    } else {
        $scope.imgList = [];
    }
}


/**
 * @method      保单影像上传、查看
 * @param {*}   $scope  --$scope
 * @param {*}   loadFileinput    --初始化影像上传框
 *
 * 寿险影像上传：每个保险公司（有的保险公司投保方式也是不同的）上传的影像和影像个数都不同，根据上传的图片总数，来判断影像是否符合上传要求。
 *             被保人的“与投保人关系”选择本人时，就不用上传“被保人正面、背面”影像。
 *             “保险公司、投保方式、与被保人关系”变更时，初始化已上传的影像，重新计算出当前保险公司所需的影像。
 *             “保单影像”只有在“承保提交”中才能上传，保单影像是必须上传的。
 *             “回执影像”在“承保提交”中不是必须的，所以提交时不验证回执影像。在“回执提交”中，回执影像是必须的
 *             数据库“ProviderImage”有完整的影像类型
 *             保单退回时：如有保单、回执影像就显示，没有就不显示（只读）
 */
function uploadImages($scope, loadFileinput) {
    let contractStatus = $scope.contract.contractStatus || '0I';
    //被保人与投保人的关系    2：本人
    $scope.relation_temp = '';
    for (let i in $scope.contract.insuredList) {
        if ($scope.contract.insuredList[i].prelations == '2' && $scope.contract.insuredList.length <= 1) {
            $scope.relation_temp = '2'; break;
        }
    }
    let html = '';
    getImgList($scope);
    $scope.necessary = [];
    let optionArr = $scope.option[contractStatus] || $scope.option['0I'];
    for (let i = 0; i < $scope.imgList.length; i++) {
        //证件资料，如投、被保人是同一人，不上传被保人影像
        if ($scope.relation_temp == '2' && ($scope.imgList[i] == '105' || $scope.imgList[i] == '106' || $scope.imgList[i] == '206')) {
            continue;
        }
        if (~['145', '148'].indexOf($scope.imgList[i])) {
            let existFlag = true;
            $scope.contract.resList && $scope.contract.resList.forEach(e => {
                if (e.resType == $scope.imgList[i]) {
                    existFlag = false;
                }
            });
            if (existFlag) {
                continue;
            }
        }
        //投保单录入、投保单修改
        if (contractStatus == '0I' || !contractStatus) {
            html = imageHtml.replace('imageName', imageType[$scope.imgList[i]]);
            html = html.replace('imageId', $scope.imgList[i]);
            //录入或修改时，保单影像和回执影像不用上传
            if ($scope.imgList[i] == '201' || $scope.imgList[i] == '202') {
                html = html.replace('isDisabled', 'disabled');
            } else if (optionArr.indexOf($scope.imgList[i]) == -1) {
                $scope.necessary.push($scope.imgList[i]);
            }
        }//承保提交(只有“保单”影像是必须的)
        else if (contractStatus == '0A') {
            html = imageHtml.replace('imageName', imageType[$scope.imgList[i]]);
            html = html.replace('imageId', $scope.imgList[i]);
            if ($scope.imgList[i] == '201') {
                $scope.necessary.push($scope.imgList[i]);
            } else if (optionArr.indexOf($scope.imgList[i]) == -1) {
                html = html.replace('isDisabled', 'disabled');
            }
        }
        $('#images').append(html);
        //初始化图片
        if ($scope.postImages && $scope.postImages.length > 0) {
            let initialPreview = [], initialPreviewConfig = [], img, item, flag = false;
            for (let k in $scope.postImages) {
                item = typeof ($scope.postImages[k]) == 'string' ? JSON.parse($scope.postImages[k]) : $scope.postImages[k];
                img = "<img src=" + item.url + " class='kv-preview-data file-preview-image' style='height:160px;width:100%' alt=" + decodeURIComponent(item.name) + ">";
                initialPreviewConfig = [{
                    url: "/biz/contract/image/" + item._id + "/remove"
                }];
                if (item.resType == $scope.imgList[i]) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                loadFileinput(img, initialPreviewConfig, item.resType);
                //保单录入：除“保单影像”、“回执影像”都要验证是否上传
                if (contractStatus == '0I' && $scope.imgList[i] != '201' && $scope.imgList[i] != '202') {
                    $scope.uploaded[item.resType] = '1';
                }//承保提交：只验证”保单影像“是否上传
                else if (contractStatus == '0A' && $scope.imgList[i] == '201') {
                    $scope.uploaded[item.resType] = '1';
                }
            } else {
                //保单录入：“保单影像”、“回执影像”为空时，不显示
                if (contractStatus == '0I' && ($scope.imgList[i] == '201' || $scope.imgList[i] == '202')) {
                    $('#' + $scope.imgList[i]).parent().remove();
                } else {
                    loadFileinput([], [], $scope.imgList[i]);
                }
            }
        } else {
            //保单录入：“保单影像”、“回执影像”为空时，不显示
            if (contractStatus == '0I' && ($scope.imgList[i] == '201' || $scope.imgList[i] == '202')) {
                $('#' + $scope.imgList[i]).parent().remove();
            } else {
                loadFileinput([], [], $scope.imgList[i]);
            }
        }
    }
}

//寿险影像类型
let imageType = {
    '101': '投保人身份证正面',
    '102': '投保人身份证背面',
    '103': '人身投保提示书正面',
    '104': '人身投保提示书背面',
    '105': '被保人身份证正面',
    '106': '被保人身份证背面',
    '107': '银行卡正面',
    '108': '代理人报告书',
    '109': '微投完成截图',
    '110': '银行自动转账授权申请书',
    '111': '销售人员报告书',
    '112': '税收声明书签名页',
    '113': '理财顾问报告书',
    '114': '个人税收居民身份声明签名页',
    '115': '电投确认书', //已废弃
    '116': '银行缴费卡',
    '117': '其他资料1',
    '118': '其他资料2',
    '119': '电投确认书',//等价于 ==> 电子投保确认书正面
    '120': '电子投保确认书背面',
    '121': '投保提示书正面',
    '122': '投保提示书背面',
    '123': '孕检资料',
    '124': '银行卡反面',
    '125': '其他',
    '126': '其他资料3',
    '127': '其他资料4',
    '128': '受益人1身份证正面',
    '129': '受益人1身份证背面',
    '130': '受益人2身份证正面',
    '131': '受益人2身份证背面',
    '132': '投保单第一页',
    '133': '投保单第二页',
    '134': '投保单第三页',
    '135': '投保单第四页',
    '136': '电投完成截图',
    '137': '投保单签名页',
    '138': '客户告知书',
    '201': '保单',
    '202': '回执',
    '140': '电子单第一页',
    '141': '电子单第二页',
    '142': '电子单第三页',
    '143': '电子单第四页',
    '144': '电子单第五页',
    '145': '风险提示书',
    '146': '投保提示书第一页',
    '147': '投保提示书第二页',
    '148': '权益告知书',
    '205': '投保人证件',
    '206': '被保人证件',
    '207': '经纪人证件',
};

//寿险追踪影像类型（“contract”表的“transfer.stateType”定义的key和“postImage”表的“resType”定义的key不同)
let traceImageType = {
    '101': '余额不足扣款失败',
    '102': '户名或账号有误',
    '103': '体检件',
    '104': '受益人法定',
    '105': '上传证件/银行卡影像非原件',
    '106': '因健康原因需要提供资料',
    '107': '修改保险计划',
    '108': '其他',
    '109': '录入',
    '110': '待核保',
    '111': '待扣款',
    '201': '多状态',
    '余额不足扣款失败': '301',
    '户名或账号有误': '302',
    '体检件': '303',
    '受益人法定': '304',
    '上传证件/银行卡影像非原件': '305',
    '因健康原因需要提供资料': '306',
    '修改保险计划': '307',
    '其他': '308',
    '录入': '309',
    '待核保': '310',
    '待扣款': '311',
    '待核保,待扣款': '312',
    '待扣款,待核保': '313',
    '多状态': '314'
};
//寿险追踪流转状态
let stateFObj = {
    '资料补充': "您的投保单已于startDate生成，目前缺少材料有：……，请及时补充，谢谢！",
    '资料邮寄': "您的投保资料已于startDate由汇中寄出等待保险公司签收，请知晓，谢谢！",
    '资料签收': "保险公司已于startDate收到您的投保资料，请知晓，谢谢！",
    '递交核保': "保险公司已于startDate将您的保单递交核保，请知晓，谢谢！",
    '已扣款核保中': "您的保单已于startDate扣款成功，保险公司正在核保，请耐心等待，谢谢！",
    '已核保未扣款': "您的保单已于startDate核保通过，请尽快缴费，谢谢！",
    '出单中': "您的保险合同已于startDate开始制作，请耐心等待邮寄，谢谢！",
    '不予承保': "您的保单已于startDate完成核保，保险公司不予承保，原因为……，请知晓，谢谢！",
    '问题件': "于startDate您的本次投保在核保过程中产生问题件，需要XXX，请尽快提供，谢谢!",
    '问题件处理中': "于startDate您的问题件正在努力处理，请知晓，谢谢！",
    '录入': "您的保单已于startDate电投录入完成，请知晓，谢谢！"
};
//状态详情
let stateInfo = {
    '101': "工银：联系客户完成充值，建议比应缴金额多充10元，确认余额充足后告知对接机构内勤转告工银安排扣款。",
    '102': "工银：1、用户名有误：补充文件中说明“账户名：XXX，银行卡号：XXXXXXXXX”，另提供银行卡正反面复印件。"
        + "2、卡号有误：签署“银行自动转账授权书”，填写授权账户信息前两行并签字。",
    '103': "工银：当地：拨打体检单上客服电话自行预约，体检时需带体检单原件（保险公司邮寄）、身份证原件和一寸照片。异地：与客户确认体检时间后至少提前两个工作日知会总部刘红老师安排预约，体检时需带体检单原件、身份证原件和一寸照片。",
    '104': "工银：签署“指定受益人权利告知书”。",
    '105': "工银：重新提交证件/银行卡正反面复印件。",
    '106': "工银：填写“补充文件”，并上交健康资料，如无资料提供请在补充文件中说明，如需填写补充问卷，请待收到工银。",
    '107': "工银：填写补充文件，正文写清楚新的投保计划。",
    '108': "(需要手动补充“供应商：问题件原因，问题件处理方式”)",
    '110': "问题件处理完成，等待保险公司人工核保。",
    '111': "问题件处理完成，等待保险公司扣款。",
    '14': ""
};

//imageName：影像名称    imageId：影像ID    isDisabled:是否禁用。。。使用时用replace替换这3个字符串
let imageHtml = '<div class="col-sm-3" style="height:430px;overflow-y:hidden;"><label>imageName</label>' +
    '<input type="file"  class="file addImage" name="file3" id="imageId" isDisabled><p class="help-block">支持jpg、jpeg、png、gif格式，大小不超过2.0M</p></div>';
