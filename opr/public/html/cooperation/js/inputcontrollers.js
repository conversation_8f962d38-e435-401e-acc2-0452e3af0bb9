var inputCooperationControllers = angular.module('inputCooperationControllers', []);

inputCooperationControllers.controller('CoopInputCtrl', ['$scope', '$rootScope', '$location', 'Common', "$ngBootbox", '$uibModal','CoopProductTypes', "CoopRegister", "CoopInput"
    , function ($scope, $rootScope, $location, Common, $ngBootbox, $uibModal,CoopProductTypes, CoopRegister, CoopInput) {

        $scope.totalItems = 0;
        $scope.currentPage = 1;
        $scope.pageSize = 10;
        $scope.cases = [];
        $scope.search = {
            contractType: []
        };
        $scope.contractType = CoopProductTypes;

        /*  angularjs-dropdown-multiselect---控件--start  */
        //1：（供应商 || 险种）   （2：签约方 、机构）  3：省份  4：城市
        $scope.translation = { checkAll: '全选', uncheckAll: '取消' };
        $scope.settings_search_1 = { displayProp: 'value', enableSearch: true, scrollable: true };
        $scope.settings_search_2 = { displayProp: 'name', enableSearch: true, scrollable: true };
        $scope.settings_search_3 = { displayProp: 'provinceName', enableSearch: true, scrollable: true };
        $scope.settings_search_4 = { displayProp: 'cityName', enableSearch: true, scrollable: true };
        //change事件
        // $scope.onSelectionChanged_1 = { 'onSelectionChanged': providerChange };
        // $scope.onSelectionChanged_2 = { 'onSelectionChanged': provinceChange };
        //初始化多选控件的样式
        $scope.initStyle = function(){
            Common.initStyle();
        }
        /*  angularjs-dropdown-multiselect---控件--end    */

        $scope.init = function () {
            Common.getProviders().then(function (result) {
                $scope.providers = result.list;
            });
            Common.branch().then(function (allBranch) {
                $scope.branchs = Common.getBranchs(allBranch.data.branches);
            });
            Common.branch().then(function (allBranch) {
                $scope.branchs = Common.getBranchs(allBranch.data.branches);
            });

        };
        $scope.init();

        $scope.query = function () {
            let obj = $scope.search;
            obj.status = "1";
            obj.currentPage = $scope.currentPage || 1;
            obj.pageSize = $scope.pageSize || 10;
            CoopInput.query(obj).then(function (result) {
                $scope.cases = result.docs;
                $scope.totalItems = result.total;
                $scope.cases.forEach(function (e) {
                    e.branch = Common.getBaseCode(e.branch, $scope.branchs)
                })
            });
        };
        $scope.query();

        //录入
        $scope.input = function (id) {
            $location.url("/cooperation/input/inputInput?id=" + id);
        }

        //查看
        $scope.detail = function (id) {
            $location.url("/cooperation/input/detail?id=" + id);
        }

        $scope.providerChange = function (providerName) {
            Common.parties({ providerName: providerName }).then(function (result) {
                $scope.partiesArray = result.data.partiesArray;
            });
        };

        $scope.closeAlert = function (index) {
            delete $scope.alert;
        };
    }
]);

//录入-录入
inputCooperationControllers.controller('CoopInputInputCtrl', ['$scope', '$ngBootbox', '$rootScope', '$timeout', '$location', "$ngBootbox", '$uibModal', 'Common', "CoopProductTypes", "CoopBillTypes",
    "CoopFeeTypes", "channelType", "CoopTaxTypes", "CoopVatTypes", "CoopHasPrintDetails", "CoopRegister", "CoopInput",
    function ($scope, $ngBootbox, $rootScope, $timeout, $location, $ngBootbox, $uibModal, Common, CoopProductTypes, CoopBillTypes, CoopFeeTypes,
        channelType, CoopTaxTypes, CoopVatTypes, CoopHasPrintDetails, CoopRegister, CoopInput) {

        var obj = {};
        obj.id = $location.search().id || undefined;
        console.log("id in add" + obj.id);

        $timeout(function () {
            document.getElementById("newremark").style.height = document.getElementById("newremark").scrollHeight + 10 + "px";
            document.getElementById("inputremark").style.height = document.getElementById("inputremark").scrollHeight + 10 + "px";
            document.getElementById("inputreason").style.height = document.getElementById("inputreason").scrollHeight + 10 + "px";
            document.getElementById("underwriteremark").style.height = document.getElementById("underwriteremark").scrollHeight + 10 + "px";
            document.getElementById("underwritereason").style.height = document.getElementById("underwritereason").scrollHeight + 10 + "px";
        }, 500)

        $scope.search = {};
        $scope.input = {
            'inputOperBy': window.logedInUser.fullName,
            'inputOperAt': new Date()
        };
        $scope.CoopProductTypes = CoopProductTypes;
        $scope.CoopBillTypes = CoopBillTypes;
        $scope.CoopFeeTypes = CoopFeeTypes;
        $scope.channelTypes = channelType;
        $scope.CoopTaxTypes = CoopTaxTypes;
        $scope.CoopVatTypes = CoopVatTypes;
        $scope.CoopHasPrintDetails = CoopHasPrintDetails;
        $scope.search.feeType = [];
        $scope.imageArr = [];
        // $scope.totalItems = 0;
        $scope.currentPage = 1;
        $scope.pageSize = 10;

        /*  angularjs-dropdown-multiselect---控件--start    */
        $scope.settings = { displayProp: 'value', scrollable: true };
        $scope.translation = { checkAll: '全选', uncheckAll: '取消' };
        $timeout(function () {
            angular.element('.selectBox div,.selectBox ul,.selectBox button').addClass('col-sm-12');
            angular.element('.selectBox div,.selectBox ul').css({ 'padding': '0px' });
            angular.element('.selectBox input').parent().css({ 'padding': '15px' });
        }, 0);
        /*  angularjs-dropdown-multiselect---控件--end    */

        $scope.init = function () {
            Common.getProviders().then(function (result) {
                $scope.providers = result.list;
            });
            Common.branch().then(function (allBranch) {
                $scope.branchs = Common.getBranchs(allBranch.data.branches);
            });
        };
        $scope.init();

        CoopRegister.inputquery(obj).then(function (result) {
            $scope.partyName = "" + result.partiesName + ":" + result.hasTax + ":" + result.partiesId;
            $scope.search = result;

            $scope.input = {
                'inputAt': result.inputAt,
                'inputFile': result.inputFile,
                'inputOperBy': window.logedInUser.fullName,
                'inputOperAt': moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
                'inputReason': result.inputReason,
                'inputRemark': result.inputRemark,
            }

            $scope.imageArr = result.images;
            if ($scope.search.contractType && $scope.search.contractType.length > 0) {
                let arr = $scope.search.contractType.split(",");
                $scope.search.contractType = [];
                arr.map(e => {
                    $scope.CoopProductTypes.map(p => {
                        if (e == p.key) {
                            $scope.search.contractType.push(p);
                        }
                    })
                })
            }

            var files = $scope.imageArr, initialPreview = [], initialPreviewConfig = [];
            $scope.files = [];
            for (var i in files) {
                var fileType = typeof files[i];
                var item = fileType == 'string' ? JSON.parse(files[i]) : files[i];
                $scope.files.push(item);
                var img = "<img src=" + item.url + " class='kv-preview-data file-preview-image' style='height:160px;' alt=" + item.abbrName + ">";
                console.log(img)
                initialPreview.push(img);
                initialPreviewConfig.push({
                    url: "/biz/contract/image/" + item._id + "/remove",
                    extra: { name: item.name }
                });
            }
            loadFileinput(initialPreview, initialPreviewConfig);

            if ($scope.search.feeType) {
                //_feeType这个为了显示用
                $scope.search._feeType = $scope.search.feeType;
                var types = $scope.search.feeType.split(',');
                var otypes = [];
                types.forEach(function (t) {
                    CoopFeeTypes.forEach(function (ft) {
                        if (t == ft.key) otypes.push(ft);
                    })
                });
                $scope.search.feeType = otypes;
            }

            if ($scope.search.startDate) { $scope.search.startDate = moment(new Date($scope.search.startDate)).format("YYYY-MM-DD HH:mm:ss"); }
            if ($scope.search.endDate) { $scope.search.endDate = moment(new Date($scope.search.endDate)).format("YYYY-MM-DD HH:mm:ss"); }
            if ($scope.search.registerOperAt) { $scope.search.registerOperAt = moment(new Date($scope.search.registerOperAt)).format("YYYY-MM-DD HH:mm:ss"); }

            $scope.provider($scope.search.providerName);
        });

        $scope.submit = function () {
            let obj = $scope.input;
            obj._id = $scope.search._id;
            // for (var element of $scope.imageArr){
            //     element.postId = 'cooperation-input';
            // }
            obj.images = $scope.imageArr;

            console.log("submit-req"); console.log(obj);
            CoopInput.submit(obj).then(function (data) {
                if (data.err) {
                    $ngBootbox.alert("保存失败");
                } else {
                    $ngBootbox.alert("保存成功");
                    $location.url("/cooperation/input");
                }
            })
        }

        $scope.save = function () {
            let obj = $scope.input;
            obj._id = $scope.search._id;
            obj.images = $scope.imageArr;
            console.log("save-req"); console.log(obj);
            CoopInput.save(obj).then(function (result) {
                if (result.err) {
                    $ngBootbox.alert("保存失败");
                } else {
                    $ngBootbox.alert("保存成功");
                    $location.url("/cooperation/input");
                }
            });
        }

        $scope.retreat = function () {
            $ngBootbox.confirm("确认要退回吗？").then(function () {
                if ($scope.input.inputReason) {
                    let obj = {};
                    obj.inputReason = $scope.input.inputReason;
                    obj._id = $scope.search._id;
                    console.log("retreat-req"); console.log(obj);
                    CoopInput.retreat(obj).then(function (result) {
                        console.log("retreat-result");
                        console.log(result);
                        if (result.err) {
                            $ngBootbox.alert("退回失败");
                        } else {
                            $ngBootbox.alert("退回成功");
                            $location.url("/cooperation/input");
                        }
                    })
                } else {
                    return $ngBootbox.alert('退回原因不能为空！');
                };
            }, function () {
            });
        };

        $scope.goback = function () {
            $location.url("/cooperation/input");
        }

        $scope.provider = function (providerName) {

            Common.parties({ providerName: providerName }).then(function (result) {
                $scope.partiesArray = result.data.partiesArray;
            });
        };

        $scope.providerChange = function (providerName) {
            $scope.search.hasTax = undefined;
            Common.parties({ providerName: providerName }).then(function (result) {
                $scope.partiesArray = result.data.partiesArray;
            });
        };

        $scope.showChannel = function () {
            $scope.search.agentType = '';
            var channel = $scope.search.channelType;
            if (channel) {
                var obj = channel.split(':');
                Common.getChannels({ channelType: obj[0] }).then(data => {
                    $scope.agentTypes = data.channels;
                });
            } else {
                $scope.agentType = [];
            }
        }
        //获取业务员信息
        $scope.getAgentInfo = function (saleman) {
            if (!saleman) return;
            Common.getBrokerInfo(saleman).then(function (result) {
                var data = result.list;
                if (data) {
                    $scope.search.agentName = data.agentName;
                    $scope.search.branch = data.branchCode;
                    $scope.search.channelType = data.channelType;
                    $scope.search.agentType = data.agentType;
                } else {
                    $scope.search.agentName = "";
                    $scope.search.branch = "";
                    $scope.search.channelType = "";
                    $scope.search.agentType = "";
                    $scope.search.saleman = "";
                    var some_html = '<div class="alert alert-danger fade in"><label>不存在与该电话号码对应的营销人员</label></div>';
                    $ngBootbox.alert(some_html);
                }
            })
        };

        //根据签约方获取结算方式
        $scope.changeParties = function () {
            let arr = $scope.partyName.split(':');
            $scope.search.partiesName = arr[0];
            $scope.search.hasTax = arr[1];
            $scope.search.partiesId = arr[2];
            // JSON.parse($scope.search.partiesName).hasTax;
        };

        //图片上传
        function loadFileinput(initialPreview, initialPreviewConfig) {
            $("#addImage").fileinput({
                language: 'zh',
                uploadUrl: "/biz/contract/uploadFile",
                allowedFileExtensions: ["jpg", "png",'jpeg', 'pdf', 'txt', 'doc', 'docx', 'xls', 'xlsx', 'rar', 'zip'],
                initialPreview: initialPreview,
                initialPreviewConfig: initialPreviewConfig,
                browseOnZoneClick: true,
                maxFileCount: 10,
                maxFileSize: 20000,
                resizePreference: 'height',
                overwriteInitial: false,
                uploadLabel: "上传",
                browseLabel: "选择图片",
                dropZoneTitle: "点击",
                dropZoneClickTitle: "选择图片",
                browseClass: "btn btn-primary",
                showCaption: false,
                resizeImage: true
            }).on('fileuploaded', function (event, data) {
                console.log(JSON.stringify(data));
                $scope.imageArr.push(data.response);
                console.log("imageArr");
                console.log($scope.imageArr);
            }).on('filedeleted', function (event, key, jqXHR, data) {
                console.log('data'); console.log(data);
                let file = $scope.files;
                for (let i = 0; i < file.length; i++) {
                    if (data.name == file[i].name)
                        file.splice(i, 1);
                }
                $scope.imageArr = $scope.files = file;
                console.log('$scope.files'); console.log($scope.files);
            });
        }

        // 日期控件
        $scope.popup = {
            opened1: false,
            opened2: false,
            opened3: false
        };
        $scope.openPopup = function (number) {
            $scope.popup['opened' + number] = true;
        }
    }
]);

//录入-详情
inputCooperationControllers.controller('CoopInputDetailCtrl', ['$scope', '$ngBootbox', '$rootScope', '$timeout', '$location', "$ngBootbox", '$uibModal', 'Common', "CoopProductTypes", "CoopBillTypes",
    "CoopFeeTypes", "channelType", "CoopTaxTypes", "CoopVatTypes", "CoopHasPrintDetails", "CoopRegister", "CoopInput",
    function ($scope, $ngBootbox, $rootScope, $timeout, $location, $ngBootbox, $uibModal, Common, CoopProductTypes, CoopBillTypes, CoopFeeTypes,
        channelType, CoopTaxTypes, CoopVatTypes, CoopHasPrintDetails,CoopRegister, CoopInput) {

        var obj = {};
        obj.id = $location.search().id || undefined;
        console.log("id in add" + obj.id);

        $scope.search = {};
        $scope.CoopProductTypes = CoopProductTypes;
        $scope.CoopBillTypes = CoopBillTypes;
        $scope.CoopFeeTypes = CoopFeeTypes;
        $scope.channelTypes = channelType;
        $scope.CoopTaxTypes = CoopTaxTypes;
        $scope.CoopVatTypes = CoopVatTypes;
        $scope.CoopHasPrintDetails = CoopHasPrintDetails;
        $scope.search.feeType = [];
        $scope.imageArr = [];
        // $scope.totalItems = 0;
        $scope.currentPage = 1;
        $scope.pageSize = 10;

        /*  angularjs-dropdown-multiselect---控件--start    */
        $scope.settings = { displayProp: 'value', scrollable: true };
        $scope.translation = { checkAll: '全选', uncheckAll: '取消' };
        $timeout(function () {
            angular.element('.selectBox div,.selectBox ul,.selectBox button').addClass('col-sm-12');
            angular.element('.selectBox div,.selectBox ul').css({ 'padding': '0px' });
            angular.element('.selectBox input').parent().css({ 'padding': '15px' });
        }, 0);
        /*  angularjs-dropdown-multiselect---控件--end    */
        $timeout(function () {
            document.getElementById("newremark").style.height = document.getElementById("newremark").scrollHeight + 10 + "px";
            document.getElementById("inputremark").style.height = document.getElementById("inputremark").scrollHeight + 10 + "px";
            document.getElementById("inputreason").style.height = document.getElementById("inputreason").scrollHeight + 10 + "px";
            document.getElementById("underwriteremark").style.height = document.getElementById("underwriteremark").scrollHeight + 10 + "px";
            document.getElementById("underwritereason").style.height = document.getElementById("underwritereason").scrollHeight + 10 + "px";
        }, 500)
        $scope.init = function () {
            Common.getProviders().then(function (result) {
                $scope.providers = result.list;
            });
            Common.branch().then(function (allBranch) {
                $scope.branchs = Common.getBranchs(allBranch.data.branches);
            });
        };
        $scope.init();

        CoopRegister.inputquery(obj).then(function (result) {
            $scope.partyName = "" + result.partiesName + ":" + result.hasTax + ":" + result.partiesId;
            $scope.search = result;
            $scope.imageArr = result.images;
            if ($scope.search.contractType && $scope.search.contractType.length > 0) {
                let arr = $scope.search.contractType.split(",");
                $scope.search.contractType = [];
                arr.map(e => {
                    $scope.CoopProductTypes.map(p => {
                        if (e == p.key) {
                            $scope.search.contractType.push(p);
                        }
                    })
                })
            }

            console.log("feetype"); console.log($scope.search.feeType);
            if ($scope.search.feeType) {
                //_feeType这个为了显示用
                $scope.search._feeType = $scope.search.feeType;
                var types = $scope.search.feeType.split(',');
                var otypes = [];
                types.forEach(function (t) {
                    CoopFeeTypes.forEach(function (ft) {
                        if (t == ft.key) otypes.push(ft);
                    })
                });
                $scope.search.feeType = otypes;
            }

            var files = $scope.imageArr, initialPreview = [], initialPreviewConfig = [];
            $scope.files = [];
            for (var i in files) {
                var fileType = typeof files[i];
                var item = fileType == 'string' ? JSON.parse(files[i]) : files[i];
                $scope.files.push(item);
                var img = "<img src=" + item.url + " class='kv-preview-data file-preview-image' style='height:160px;' alt=" + item.abbrName + ">";
                console.log(img)
                initialPreview.push(img);
                initialPreviewConfig.push({
                    // url: "/biz/contract/image/" + item._id + "/remove",
                    extra: { name: item.name }
                });
            }
            loadFileinput(initialPreview, initialPreviewConfig);

            if ($scope.search.startDate) { $scope.search.startDate = moment(new Date($scope.search.startDate)).format("YYYY-MM-DD HH:mm:ss"); }
            if ($scope.search.endDate) { $scope.search.endDate = moment(new Date($scope.search.endDate)).format("YYYY-MM-DD HH:mm:ss"); }
            if ($scope.search.registerOperAt) { $scope.search.registerOperAt = moment(new Date($scope.search.registerOperAt)).format("YYYY-MM-DD HH:mm:ss"); }
            if ($scope.search.inputOperAt) { $scope.search.inputOperAt = moment(new Date($scope.search.inputOperAt)).format("YYYY-MM-DD HH:mm:ss"); }
            if ($scope.search.inputAt) { $scope.search.inputAt = moment(new Date($scope.search.inputAt)).format("YYYY-MM-DD HH:mm:ss"); }

            $scope.provider($scope.search.providerName);
        });

        $scope.back = function () {
            $location.url("/cooperation/input");
        }

        $scope.provider = function (providerName) {

            Common.parties({ providerName: providerName }).then(function (result) {
                $scope.partiesArray = result.data.partiesArray;
            });
        };

        $scope.providerChange = function (providerName) {
            $scope.search.hasTax = undefined;
            Common.parties({ providerName: providerName }).then(function (result) {
                $scope.partiesArray = result.data.partiesArray;
            });
        };

        $scope.showChannel = function () {
            $scope.search.agentType = '';
            var channel = $scope.search.channelType;
            if (channel) {
                var obj = channel.split(':');
                Common.getChannels({ channelType: obj[0] }).then(data => {
                    $scope.agentTypes = data.channels;
                });
            } else {
                $scope.agentType = [];
            }
        }
        //获取业务员信息
        $scope.getAgentInfo = function (saleman) {
            if (!saleman) return;
            Common.getBrokerInfo(saleman).then(function (result) {
                var data = result.list;
                if (data) {
                    $scope.search.agentName = data.agentName;
                    $scope.search.branch = data.branchCode;
                    $scope.search.channelType = data.channelType;
                    $scope.search.agentType = data.agentType;
                } else {
                    $scope.search.agentName = "";
                    $scope.search.branch = "";
                    $scope.search.channelType = "";
                    $scope.search.agentType = "";
                    $scope.search.saleman = "";
                    var some_html = '<div class="alert alert-danger fade in"><label>不存在与该电话号码对应的营销人员</label></div>';
                    $ngBootbox.alert(some_html);
                }
            })
        };

        //根据签约方获取结算方式
        $scope.changeParties = function () {
            let arr = $scope.partyName.split(':');
            $scope.search.partiesName = arr[0];
            $scope.search.hasTax = arr[1];
            $scope.search.partiesId = arr[2];
            // JSON.parse($scope.search.partiesName).hasTax;
        };

        //图片上传
        function loadFileinput(initialPreview, initialPreviewConfig) {
            $("#addImage").fileinput({
                language: 'zh',
                uploadUrl: "/biz/contract/uploadFile",
                allowedFileExtensions: ["jpg", "png", 'jpeg', 'pdf', 'txt', 'doc', 'docx', 'xls', 'xlsx', 'rar', 'zip'],
                initialPreview: initialPreview,
                initialPreviewConfig: initialPreviewConfig,
                browseOnZoneClick: true,
                maxFileCount: 10,
                maxFileSize: 20000,
                resizePreference: 'height',
                overwriteInitial: false,
                uploadLabel: "上传",
                browseLabel: "选择图片",
                dropZoneTitle: "点击",
                dropZoneClickTitle: "选择图片",
                browseClass: "btn btn-primary",
                showCaption: false,
                resizeImage: true
            }).on('fileuploaded', function (event, data) {
                console.log(JSON.stringify(data));
                $scope.imageArr.push(data.response);
                console.log("imageArr");
                console.log($scope.imageArr);
            });
        }

        // 日期控件
        $scope.popup = {
            opened1: false,
            opened2: false,
            opened3: false
        };
        $scope.openPopup = function (number) {
            $scope.popup['opened' + number] = true;
        }
    }
]);
