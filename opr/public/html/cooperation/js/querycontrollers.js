const queryCooperationControllers = angular.module('queryCooperationControllers', []);

queryCooperationControllers.controller('CoopQueryCtrl', [ '$scope', '$rootScope', '$location', 'Common', 'CoopStatus', '$ngBootbox', '$uibModal', 'CoopRegister', 'CoopInput', 'CoopQuery',
    function($scope, $rootScope, $location, Common, CoopStatus, $ngBootbox, $uibModal, CoopRegister, CoopInput, CoopQuery) {

        // 从$rootScope获取保存的search条件
        const historySearch = Common.searchSave($scope, $rootScope, $location);
        $scope.search = historySearch.search;

        $scope.totalItems = 0;
        $scope.currentPage = 1;
        $scope.pageSize = 10;
        $scope.CoopStatus = CoopStatus;
        $scope.CoopHasBusiness = CoopHasBusiness;
        $scope.cases = [];
        console.log('存储的搜索条件:' + JSON.stringify($scope.search));


        $scope.query = function() {

            // 每次点击查询,把查询条件保存到rootScope
            $rootScope.search[historySearch.urlKey] = $scope.search;
            const obj = $scope.search;
            obj.currentPage = $scope.currentPage;
            obj.pageSize = $scope.pageSize;
            CoopQuery.query(obj).then(function(result) {
                $scope.cases = result.docs;
                $scope.totalItems = result.total;
                $scope.cases.forEach(function(e) {
                    e.branch = Common.getBaseCode(e.branch, $scope.branchs);
                    e.invoiceBranch = Common.getBaseCode(e.invoiceBranch, $scope.branchs);
                });
            });
        };
        $scope.query();

        $scope.export = function (type) {
            let url;
            if (type == '1') {
                url = '/cooperation/query/export?1=1';
            }
            if (type == '2') {
                url = '/cooperation/query/exportDetail?1=1';
            }
            if ($scope.search.startDate) {
                url += '&startDate=' + moment($scope.search.startDate).format('YYYY-MM-DD');
            }
            if ($scope.search.endDate) {
                url += '&endDate=' + moment($scope.search.endDate).format('YYYY-MM-DD');
            }
            if ($scope.search.providerCode) {
                url += '&providerCode=' + $scope.search.providerCode;
            }
            if ($scope.search.partiesId) {
                url += '&partiesId=' + $scope.search.partiesId;
            }
            if ($scope.search.branch) {
                url += '&branch=' + $scope.search.branch;
            }
            if ($scope.search.invoiceBranch) {
                url += '&invoiceBranch=' + $scope.search.invoiceBranch;
            }
            if ($scope.search.status) {
                url += '&status=' + $scope.search.status;
            }
            if ($scope.search.patchNo) {
                url += '&patchNo=' + $scope.search.patchNo;
            }
            if ($scope.search.handleSeq) {
                url += '&handleSeq=' + $scope.search.handleSeq;
            }
            if ($scope.search.start) {
                url += '&start=' + moment($scope.search.start).format('YYYY-MM-DD');
            }
            if ($scope.search.end) {
                url += '&end=' + moment($scope.search.end).format('YYYY-MM-DD');
            }
            window.location.href = url;
            return true;
        }

        // 获取选择框的值
        getSelectBoxValue($scope, Common, [ '10001', '10004', '10006' ]);


        // 如果搜索条件保留了保险公司,联动保留签约方搜索条件
        if ($scope.search.providerCode) {
            $scope.findParties();
        }

        // 查看
        $scope.detail = function(id) {
            $location.url('/cooperation/query/detail?id=' + id);
        };
        // 补传（业务清单）
        $scope.repair = function(id) {
            $location.url('/cooperation/query/repair?id=' + id);
        };
        // 复制
        $scope.copy = function(id) {
            $location.url('/cooperation/register/add?id=' + id);
        };
        // 退回
        $scope.retreat = function(id) {
            $ngBootbox.confirm('确定退回该批次吗？').then(() => {
                CoopQuery.retreat(id).then(result => {
                    const message = result.msg || '退回失败！';
                    $ngBootbox.alert(message).then(() => {
                        $scope.query();
                    });
                });
            });
        };

        // 日期控件
        $scope.popup = {
            opened1: false,
            opened2: false,
            opened3: false,
            opened4: false,
        };
        $scope.openPopup = function (number) {
            $scope.popup['opened' + number] = true;
        }

        $scope.closeAlert = function(index) {
            delete $scope.alert;
        };
    },
]);

// 录入-详情
queryCooperationControllers.controller('CoopQueryDetailCtrl', [ '$scope', '$ngBootbox', '$rootScope', '$timeout', '$location', '$ngBootbox', '$uibModal', 'Common', 'CoopProductTypes', 'CoopBillTypes',
    'CoopFeeTypes', 'channelType', 'CoopTaxTypes', 'CoopVatTypes', 'CoopHasPrintDetails', 'CoopRegister', 'CoopInput', 'CoopDataSource',
    function($scope, $ngBootbox, $rootScope, $timeout, $location, $ngBootbox, $uibModal, Common, CoopProductTypes, CoopBillTypes, CoopFeeTypes,
             channelType, CoopTaxTypes, CoopVatTypes, CoopHasPrintDetails, CoopRegister, CoopInput, CoopDataSource) {

        const obj = {};
        obj.id = $location.search().id || undefined;
        console.log('id in add' + obj.id);
        $scope.search = {
            invoiceType: [],
        };
        $scope.CoopProductTypes = CoopProductTypes;
        $scope.CoopBillTypes = CoopBillTypes;
        $scope.CoopFeeTypes = CoopFeeTypes;
        $scope.channelTypes = channelType;
        $scope.CoopTaxTypes = CoopTaxTypes;
        $scope.CoopVatTypes = CoopVatTypes;
        $scope.CoopHasPrintDetails = CoopHasPrintDetails;
        $scope.CoopInvoiceType = CoopInvoiceType;
        $scope.CoopDataSource = CoopDataSource;
        $scope.search.feeType = [];
        $scope.imageArr = [];
        $scope.total = {};
        // $scope.totalItems = 0;
        $scope.currentPage = 1;
        $scope.pageSize = 10;

        /*  angularjs-dropdown-multiselect---控件--start    */
        $scope.settings = { displayProp: 'value', scrollable: true };
        $scope.translation = { checkAll: '全选', uncheckAll: '取消' };
        $timeout(function() {
            angular.element('.selectBox div,.selectBox ul,.selectBox button').addClass('col-sm-12');
            angular.element('.selectBox div,.selectBox ul').css({ padding: '0px' });
            angular.element('.selectBox input').parent().css({ padding: '15px' });
        }, 0);
        /*  angularjs-dropdown-multiselect---控件--end    */
        $timeout(function() {
            document.getElementById('newremark').style.height = document.getElementById('newremark').scrollHeight + 10 + 'px';
            // document.getElementById("inputremark").style.height = document.getElementById("inputremark").scrollHeight + 10 + "px";
            // document.getElementById("inputreason").style.height = document.getElementById("inputreason").scrollHeight + 10 + "px";
            // document.getElementById("underwriteremark").style.height = document.getElementById("underwriteremark").scrollHeight + 10 + "px";
            // document.getElementById("underwritereason").style.height = document.getElementById("underwritereason").scrollHeight + 10 + "px";
        }, 300);

        CoopRegister.inputquery(obj).then(function(result) {
            $scope.partyName = '' + result.partiesName + ':' + result.hasTax + ':' + result.partiesId;
            $scope.search = result;
            if ($scope.search.contractType && $scope.search.contractType.length > 0) {
                const arr = $scope.search.contractType.split(',');
                $scope.search.contractType = [];
                arr.map(e => {
                    $scope.CoopProductTypes.map(p => {
                        if (e == p.key) {
                            $scope.search.contractType.push(p);
                        }
                    });
                });
            }
            $scope.imageArr = result.images;
            // 图片处理
            const files = $scope.imageArr,
                initialPreview = [],
                initialPreviewConfig = [];
            $scope.files = [];
            for (const i in files) {
                const fileType = typeof files[i];
                const item = fileType == 'string' ? JSON.parse(files[i]) : files[i];
                $scope.files.push(item);
                const img = '<img src=' + item.url + ' class=\'kv-preview-data file-preview-image\' style=\'height:160px;\' alt=' + item.abbrName + '>';
                console.log(img);
                initialPreview.push(img);
                initialPreviewConfig.push({
                    // url: "/biz/contract/image/" + item._id + "/remove",
                    extra: { name: item.name },
                });
            }
            loadFileinput(initialPreview, initialPreviewConfig);

            if ($scope.search.feeType) {
                // _feeType这个为了显示用
                $scope.search._feeType = $scope.search.feeType;
                const types = $scope.search.feeType.split(',');
                const otypes = [];
                types.forEach(function(t) {
                    CoopFeeTypes.forEach(function(ft) {
                        if (t == ft.key) otypes.push(ft);
                    });
                });
                $scope.search.feeType = otypes;
            }

            if ($scope.search.startDate) { $scope.search.startDate = moment(new Date($scope.search.startDate)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.endDate) { $scope.search.endDate = moment(new Date($scope.search.endDate)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.registerOperAt) { $scope.search.registerOperAt = moment(new Date($scope.search.registerOperAt)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.inputOperAt) { $scope.search.inputOperAt = moment(new Date($scope.search.inputOperAt)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.inputAt) { $scope.search.inputAt = moment(new Date($scope.search.inputAt)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.underwriteOperAt) { $scope.search.underwriteOperAt = moment(new Date($scope.search.underwriteOperAt)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.underwriteAt) { $scope.search.underwriteAt = moment(new Date($scope.search.underwriteAt)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.reconOperAt) { $scope.search.reconOperAt = moment(new Date($scope.search.reconOperAt)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.reconAt) { $scope.search.reconAt = moment(new Date($scope.search.reconAt)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.settleOperAt) { $scope.search.settleOperAt = moment(new Date($scope.search.settleOperAt)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.settleAt) { $scope.search.settleAt = moment(new Date($scope.search.settleAt)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.payOperAt) { $scope.search.payOperAt = moment(new Date($scope.search.payOperAt)).format('YYYY-MM-DD HH:mm:ss'); }
            if ($scope.search.payAt) { $scope.search.payAt = moment(new Date($scope.search.payAt)).format('YYYY-MM-DD HH:mm:ss'); }

            if ($scope.search.providerName) $scope.findParties();
            if ($scope.search.partiesId) $scope.partiesChange($scope.search.partiesId);
            if ($scope.search.children && $scope.search.children.length <= 0) {
                $scope.search.children = [{}];
            }
            $scope.calc();
        });

        $scope.back = function() {
            $location.url('/cooperation/query');
        };

        // 获取业务员信息
        $scope.getAgentInfo = function(saleman) {
            if (!saleman) return;
            Common.getBrokerInfo(saleman).then(function(result) {
                const data = result.list;
                if (data) {
                    $scope.search.agentName = data.agentName;
                    $scope.search.branch = data.branchCode;
                    $scope.search.channelType = data.channelType;
                    $scope.search.agentType = data.agentType;
                } else {
                    $scope.search.agentName = '';
                    $scope.search.branch = '';
                    $scope.search.channelType = '';
                    $scope.search.agentType = '';
                    $scope.search.saleman = '';
                    const some_html = '<div class="alert alert-danger fade in"><label>不存在与该电话号码对应的营销人员</label></div>';
                    $ngBootbox.alert(some_html);
                }
            });
        };

        // 根据签约方获取结算方式
        $scope.changeParties = function() {
            const arr = $scope.partyName.split(':');
            $scope.search.partiesName = arr[0];
            $scope.search.hasTax = arr[1];
            $scope.search.partiesId = arr[2];
            // JSON.parse($scope.search.partiesName).hasTax;
        };

        // 图片上传
        function loadFileinput(initialPreview, initialPreviewConfig) {
            $('#addImage').fileinput({
                language: 'zh',
                uploadUrl: '/biz/contract/uploadFile',
                allowedFileExtensions: [ 'jpg', 'png', 'jpeg', 'pdf', 'txt', 'doc', 'docx', 'xls', 'xlsx', 'rar', 'zip' ],
                initialPreview: initialPreview,
                initialPreviewConfig: initialPreviewConfig,
                browseOnZoneClick: true,
                maxFileCount: 100,
                maxFileSize: 20000,
                resizePreference: 'height',
                overwriteInitial: false,
                uploadLabel: '上传',
                browseLabel: '选择图片',
                dropZoneTitle: '点击',
                dropZoneClickTitle: '选择图片',
                browseClass: 'btn btn-primary',
                showCaption: false,
                resizeImage: true,
            }).on('fileuploaded', function(event, data) {
                console.log(JSON.stringify(data));
                $scope.imageArr.push(data.response);
                console.log('imageArr');
                console.log($scope.imageArr);
            })
                .on('filedeleted', function(event, key, jqXHR, data) {
                    console.log('data'); console.log(data);
                    const file = $scope.files;
                    for (let i = 0; i < file.length; i++) {
                        if (data.name == file[i].name) { file.splice(i, 1); }
                    }
                    $scope.imageArr = $scope.files = file;
                    console.log('$scope.files'); console.log($scope.files);
                });
        }

        getSelectBoxValue($scope, Common, [ '10002', '10004', '10006', '10007' ]);
        // 计算合计
        $scope.calc = function() {
            const children = $scope.search.children || [];
            $scope.total.totalPremium = 0; $scope.total.settleAmount = 0; $scope.total.invoiceAmount = 0;
            $scope.total.extaxInvoiceAmount = 0; $scope.total.tax = 0; $scope.total.payoutAmount = 0;
            $scope.total.policyNum = 0;
            children.forEach(elem => {
                $scope.total.totalPremium += elem.totalPremium || 0;
                $scope.total.settleAmount += elem.settleAmount || 0;
                $scope.total.invoiceAmount += elem.invoiceAmount || 0;
                $scope.total.extaxInvoiceAmount += elem.extaxInvoiceAmount || 0;
                $scope.total.tax += elem.tax || 0;
                $scope.total.payoutAmount += elem.payoutAmount || 0;
                $scope.total.policyNum += elem.policyNum || 0;

                $scope.total.totalPremium = Math.round($scope.total.totalPremium * 100) / 100;
                $scope.total.settleAmount = Math.round($scope.total.settleAmount * 100) / 100;
                $scope.total.invoiceAmount = Math.round($scope.total.invoiceAmount * 100) / 100;
                $scope.total.extaxInvoiceAmount = Math.round($scope.total.extaxInvoiceAmount * 100) / 100;
                $scope.total.tax = Math.round($scope.total.tax * 100) / 100;
                $scope.total.payoutAmount = Math.round($scope.total.payoutAmount * 100) / 100;
                $scope.total.policyNum = Math.round($scope.total.policyNum * 100) / 100;
            });
        };
        // 清单类型(是否选中)
        $scope.isSelected = function(key) {
            return $scope.search.invoiceType.indexOf(key) != -1;
        };
        // 日期控件
        $scope.popup = {
            opened1: false,
            opened2: false,
            opened3: false,
        };
        $scope.openPopup = function(number) {
            $scope.popup['opened' + number] = true;
        };
    },
]);


// 补传（业务清单）
queryCooperationControllers.controller('CoopQueryRepairCtrl', [ '$scope', '$rootScope', '$location', 'Common', '$timeout', '$ngBootbox', '$uibModal', 'Upload', 'CoopRegister', 'CoopQuery', 'CoopDataSource',
    function($scope, $rootScope, $location, Common, $timeout, $ngBootbox, $uibModal, Upload, CoopRegister, CoopQuery, CoopDataSource) {
        const id = $location.search().id;
        $scope.search = {
            invoiceType: [],
        };
        $scope.validateFlag = '2';
        $scope.CoopProductTypes = CoopProductTypes;
        $scope.CoopBillTypes = CoopBillTypes;
        $scope.CoopFeeTypes = CoopFeeTypes;
        $scope.channelTypes = channelType;
        $scope.CoopTaxTypes = CoopTaxTypes;
        $scope.CoopVatTypes = CoopVatTypes;
        $scope.CoopHasPrintDetails = CoopHasPrintDetails;
        $scope.CoopBusinessType = CoopBusinessType;
        $scope.CoopInvoiceType = CoopInvoiceType;
        $scope.CoopDataSource = CoopDataSource;
        $scope.files = [];
        // 查询
        CoopRegister.inputquery({ id: id }).then(function(result) {
            console.log(result);
            $scope.search = result;
            $scope.search.invoiceType = $scope.search.invoiceType || [];
            $scope.search.startDate = moment($scope.search.endDate).format('YYYY-MM-DD HH:mm:ss');
            $scope.search.endDate = moment($scope.search.endDate).format('YYYY-MM-DD HH:mm:ss');
            $scope.search.registerOperAt = moment($scope.search.registerOperAt).format('YYYY-MM-DD HH:mm:ss');
            if ($scope.search.providerName) $scope.findParties();
            if ($scope.search.partiesId) $scope.partiesChange($scope.search.partiesId);
            if ($scope.search.children && $scope.search.children.length <= 0) {
                $scope.search.children = [{}];
            }
        });

        // 提交
        $scope.repairFile = function() {
            const coop = {
                id: id,
                files: $scope.files,
                handleSeq: $scope.search.handleSeq,
                invoiceType: $scope.search.invoiceType,
                relateInvoiceNo: $scope.search.relateInvoiceNo,
            };
            CoopQuery.repairFile(coop).then(function(result) {
                $ngBootbox.alert(result.msg).then(function() {
                    if (!result.err) { window.history.back(); }
                });
            });
        };

        // 上传投保单清单
        $scope.import = function() {
            if (!$scope.form.file.$valid || !$scope.file) {
                return $ngBootbox.alert('请选择正确的文件格式！');
            }
            $scope.upload($scope.file);
        };
        $scope.upload = function(file) {
            const url = 'cooperation/register/validatePolicy';
            const data = {
                file: file,
                businessType: $scope.search.businessType,
                validateFlag: $scope.validateFlag,
            };
            Upload.upload({
                url: url,
                data: data,
                method: 'post',
            }).then(function(resp) {
                if (resp.data.code == '999') return $ngBootbox.alert(resp.data.message);
                $ngBootbox.alert('上传成功！').then(function() {
                    // $scope.imageArr.push(resp.data);
                    $scope.files.push(resp.data);
                });
            });
        };

        // 是否校验清单
        $scope.isValidate = function(type) {
            $scope.validateFlag = type;
        };
        // 清单类型(修改选中状态)
        $scope.updateSelected = function(key) {
            const index = $scope.search.invoiceType.indexOf(key);
            if (index == -1) $scope.search.invoiceType.push(key);
            else $scope.search.invoiceType.splice(index, 1);
        };
        // 清单类型(是否选中)
        $scope.isSelected = function(key) {
            return $scope.search.invoiceType.indexOf(key) != -1;
        };
        // 删除文件清单
        $scope.deleteFile = function(index) {
            $scope.files.splice(index, 1);
        };
        // 日期控件
        $scope.popup = {
            opened1: false,
            opened2: false,
            opened3: false,
        };
        $scope.openPopup = function(number) {
            $scope.popup['opened' + number] = true;
        };

        $scope.back = function() {
            $location.url('/cooperation/query');
        };

        getSelectBoxValue($scope, Common, [ '10002', '10004', '10006', '10007' ]);
        $timeout(function() {
            angular.element('.selectBox div,.selectBox ul,.selectBox button').addClass('col-sm-12');
            angular.element('.selectBox div,.selectBox ul').css({ padding: '0px' });
            angular.element('.selectBox input').parent().css({ padding: '15px' });
        }, 0);
    },
]);
