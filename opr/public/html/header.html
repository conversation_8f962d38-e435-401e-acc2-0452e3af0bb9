<div class="navbar navbar-inverse navbar-static-top" id="menu1" role='navigation'>
    <div class="container-fluid">
        <div class="navbar-header">
            <button type="button" data-toggle="collapse" data-target=".navbar-collapse" class="navbar-toggle">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>

        </div>
        <div class="collapse navbar-collapse">

            <ul class="nav navbar-nav menu">
                    <li uib-dropdown on-toggle="toggled(open)">
                            <a href id="simple-dropdown" uib-dropdown-toggle>
                                    {{title}}
                                    <!-- <b class="caret"></b> -->
                            </a>
                            <!-- <ul class="dropdown-menu" uib-dropdown-menu aria-labelledby="simple-dropdown" >
                                <li><a href="{{hostUrl}}" target="_blank">  {{title}}</a></li>
                                <li ng-repeat="(key,value) in permitAppMap">
                                    <a href="{{value.url}}" target="_blank"> {{value.name}}</a>
                                </li>
                            </ul> -->
                    </li>
                <li ng-repeat="menu in menuTree" ng-class="{ active:isActive(menu.fullUrl) }" ng-click="getSubMenu(menu)">
                    <a href="{{menu.fullUrl}}">{{menu.text}}</a>
                </li>

            </ul>
            <ui class="nav navbar-nav navbar-right">
                <li uib-dropdown on-toggle="toggled(open)" ng-if="logedInUser">
                    <a href id="simple-dropdown" uib-dropdown-toggle>
                        {{logedInUser.fullName}}<b class="caret"></b>
                    </a>
                    <ul class="dropdown-menu" uib-dropdown-menu aria-labelledby="simple-dropdown">
                        <li><a href="{{umURL}}/user/baseInfo" tabindex="-1">用户信息</a></li>
                        <li><a href="{{umURL}}/user/resetPassword?source={{hostUrl}}" tabindex="-1">修改密码</a></li>
                        <li><a href="/logout">退出</a></li>
                    </ul>
                </li>

                <li ng-if="!logedInUser"><a href="/login">登录</a></li>
            </ui>
        </div>
    </div>
</div>

<ul class="nav nav-pills menu">
    <li class="dropdown" ng-if="subIndex > -1" ng-show="true" ng-repeat="subMenu in menuTree[subIndex].children" ng-class="{ active:isActive(subMenu.url) }">
        <a href="javascript:void()" ng-if="subMenu.children.length > 0" class="dropdown-toggle" data-toggle="dropdown">{{subMenu.text}}<b class="caret"></b></a>
        <ul ng-if="subMenu.children.length > 0" class="dropdown-menu" role="menu">
            <li ng-repeat="subsubMenu in subMenu.children"><a href="{{subsubMenu.url}}" tabindex="-1">{{subsubMenu.text}}</a></li>
        </ul>
        <a ng-if="subMenu.children.length == 0" href="{{subMenu.fullUrl}}">{{subMenu.text}}</a>
    </li>
    <li class="dropdown" ng-if="!subIndex" ng-repeat="subMenu in subMenus" ng-class="{ active:isActive(subMenu.url) }">
        <a href="javascript:void()" ng-if="subMenu.children.length > 0" class="dropdown-toggle" data-toggle="dropdown">{{subMenu.text}}<b class="caret"></b></a>
        <ul ng-if="subMenu.children.length > 0" class="dropdown-menu" role="menu">
            <li ng-repeat="subsubMenu in subMenu.children"><a href="{{subsubMenu.fullUrl}}" tabindex="-1">{{subsubMenu.text}}</a></li>
        </ul>
        <a ng-if="subMenu.children.length == 0" href="{{subMenu.fullUrl}}">{{subMenu.text}}</a>
    </li>
</ul>
