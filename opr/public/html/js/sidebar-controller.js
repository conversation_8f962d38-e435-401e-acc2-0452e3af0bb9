/**
 * 侧边栏控制器 - 处理侧边栏菜单状态管理和交互
 */
var sidebarModule = angular.module('sidebarModule', []);

sidebarModule.controller('SidebarController', ['$scope', '$location', '$window', '$timeout', function($scope, $location, $window, $timeout) {
    
    // 侧边栏状态管理
    $scope.sidebarCollapsed = false;
    $scope.sidebarMobileOpen = false;
    $scope.systemMenuOpen = false;
    
    // 获取全局变量
    $scope.menuTree = typeof roleMenuTree !== 'undefined' ? roleMenuTree : [];
    $scope.logedInUser = typeof logedInUser !== 'undefined' ? logedInUser : null;
    $scope.hostUrl = typeof hostUrl !== 'undefined' ? hostUrl : '';
    $scope.umURL = typeof umURL !== 'undefined' ? umURL : '';
    $scope.title = typeof branchName !== 'undefined' ? branchName : '运营系统';
    $scope.permitAppMap = typeof permitAppMap !== 'undefined' ? permitAppMap : {};
    
    // 面包屑导航
    $scope.breadcrumbs = [];
    $scope.currentPageTitle = '';
    
    // 初始化菜单状态
    $scope.initializeMenus = function() {
        if ($scope.menuTree) {
            $scope.menuTree.forEach(function(menu) {
                menu.isOpen = false;
                if (menu.children) {
                    menu.children.forEach(function(subMenu) {
                        subMenu.isOpen = false;
                    });
                }
            });
        }
        $scope.updateActiveMenu();
        $scope.updateBreadcrumbs();
    };
    
    // 切换侧边栏收起/展开状态
    $scope.toggleSidebar = function() {
        $scope.sidebarCollapsed = !$scope.sidebarCollapsed;
        $scope.saveSidebarState();
        
        // 触发窗口resize事件，通知其他组件调整布局
        $timeout(function() {
            angular.element($window).triggerHandler('resize');
        }, 300);
    };
    
    // 切换移动端侧边栏
    $scope.toggleMobileSidebar = function() {
        $scope.sidebarMobileOpen = !$scope.sidebarMobileOpen;
        
        // 阻止页面滚动
        if ($scope.sidebarMobileOpen) {
            angular.element('body').addClass('sidebar-open');
        } else {
            angular.element('body').removeClass('sidebar-open');
        }
    };
    
    // 关闭移动端侧边栏
    $scope.closeMobileSidebar = function() {
        $scope.sidebarMobileOpen = false;
        angular.element('body').removeClass('sidebar-open');
    };
    
    // 切换系统选择菜单
    $scope.toggleSystemMenu = function() {
        $scope.systemMenuOpen = !$scope.systemMenuOpen;
    };
    
    // 切换主菜单
    $scope.toggleMenu = function(menu) {
        if (menu.children && menu.children.length > 0) {
            menu.isOpen = !menu.isOpen;
            
            // 在收起状态下，点击菜单时展开侧边栏
            if ($scope.sidebarCollapsed && menu.isOpen) {
                $scope.sidebarCollapsed = false;
                $scope.saveSidebarState();
            }
        } else if (menu.fullUrl) {
            $window.location.href = menu.fullUrl;
        }
    };
    
    // 切换子菜单
    $scope.toggleSubMenu = function(subMenu, $event) {
        if ($event) {
            $event.preventDefault();
            $event.stopPropagation();
        }
        
        if (subMenu.children && subMenu.children.length > 0) {
            subMenu.isOpen = !subMenu.isOpen;
        }
    };
    
    // 检查菜单是否激活
    $scope.isActive = function(url) {
        if (!url) return false;
        
        var currentPath = $location.path();
        var currentUrl = $location.absUrl();
        
        // 检查完整URL匹配
        if (currentUrl.indexOf(url) > -1) {
            return true;
        }
        
        // 检查路径匹配
        if (url.startsWith('/') && currentPath.indexOf(url) === 0) {
            return true;
        }
        
        return false;
    };
    
    // 更新激活菜单状态
    $scope.updateActiveMenu = function() {
        if (!$scope.menuTree) return;
        
        $scope.menuTree.forEach(function(menu) {
            var isMenuActive = false;
            
            if (menu.children && menu.children.length > 0) {
                menu.children.forEach(function(subMenu) {
                    var isSubMenuActive = false;
                    
                    if (subMenu.children && subMenu.children.length > 0) {
                        subMenu.children.forEach(function(subSubMenu) {
                            if ($scope.isActive(subSubMenu.url || subSubMenu.fullUrl)) {
                                isSubMenuActive = true;
                                isMenuActive = true;
                            }
                        });
                    } else if ($scope.isActive(subMenu.url || subMenu.fullUrl)) {
                        isSubMenuActive = true;
                        isMenuActive = true;
                    }
                    
                    if (isSubMenuActive) {
                        subMenu.isOpen = true;
                    }
                });
            } else if ($scope.isActive(menu.url || menu.fullUrl)) {
                isMenuActive = true;
            }
            
            if (isMenuActive) {
                menu.isOpen = true;
            }
        });
    };
    
    // 更新面包屑导航
    $scope.updateBreadcrumbs = function() {
        $scope.breadcrumbs = [];
        var currentUrl = $location.absUrl();
        var currentPath = $location.path();
        
        if (!$scope.menuTree) return;
        
        // 查找当前页面对应的菜单项
        $scope.menuTree.forEach(function(menu) {
            if (menu.children && menu.children.length > 0) {
                menu.children.forEach(function(subMenu) {
                    if (subMenu.children && subMenu.children.length > 0) {
                        subMenu.children.forEach(function(subSubMenu) {
                            if ($scope.isActive(subSubMenu.url || subSubMenu.fullUrl)) {
                                $scope.breadcrumbs = [
                                    { text: '首页', url: '#/' },
                                    { text: menu.text, url: 'javascript:void(0)' },
                                    { text: subMenu.text, url: 'javascript:void(0)' },
                                    { text: subSubMenu.text, url: subSubMenu.fullUrl }
                                ];
                                $scope.currentPageTitle = subSubMenu.text;
                            }
                        });
                    } else if ($scope.isActive(subMenu.url || subMenu.fullUrl)) {
                        $scope.breadcrumbs = [
                            { text: '首页', url: '#/' },
                            { text: menu.text, url: 'javascript:void(0)' },
                            { text: subMenu.text, url: subMenu.fullUrl }
                        ];
                        $scope.currentPageTitle = subMenu.text;
                    }
                });
            } else if ($scope.isActive(menu.url || menu.fullUrl)) {
                $scope.breadcrumbs = [
                    { text: '首页', url: '#/' },
                    { text: menu.text, url: menu.fullUrl }
                ];
                $scope.currentPageTitle = menu.text;
            }
        });
        
        // 如果没有找到匹配的菜单，设置默认面包屑
        if ($scope.breadcrumbs.length === 0) {
            $scope.breadcrumbs = [{ text: '首页', url: '#/' }];
            $scope.currentPageTitle = '首页';
        }
    };
    
    // 保存侧边栏状态到本地存储
    $scope.saveSidebarState = function() {
        try {
            localStorage.setItem('sidebarCollapsed', $scope.sidebarCollapsed);
        } catch (e) {
            console.warn('无法保存侧边栏状态到本地存储:', e);
        }
    };
    
    // 从本地存储加载侧边栏状态
    $scope.loadSidebarState = function() {
        try {
            var saved = localStorage.getItem('sidebarCollapsed');
            if (saved !== null) {
                $scope.sidebarCollapsed = saved === 'true';
            }
        } catch (e) {
            console.warn('无法从本地存储加载侧边栏状态:', e);
        }
    };
    
    // 监听路由变化
    $scope.$on('$locationChangeSuccess', function() {
        $scope.updateActiveMenu();
        $scope.updateBreadcrumbs();
        
        // 在移动端导航后关闭侧边栏
        if ($scope.isMobile()) {
            $scope.closeMobileSidebar();
        }
    });
    
    // 检查是否为移动设备
    $scope.isMobile = function() {
        return $window.innerWidth <= 768;
    };
    
    // 监听窗口大小变化
    angular.element($window).on('resize', function() {
        $scope.$apply(function() {
            // 在桌面端自动关闭移动端菜单
            if (!$scope.isMobile() && $scope.sidebarMobileOpen) {
                $scope.closeMobileSidebar();
            }
        });
    });
    
    // 处理点击外部区域关闭菜单
    angular.element(document).on('click', function(event) {
        var target = angular.element(event.target);
        
        // 如果点击的不是侧边栏内部元素，关闭系统菜单
        if (!target.closest('.sidebar').length && !target.closest('.sidebar-toggle').length) {
            $scope.$apply(function() {
                $scope.systemMenuOpen = false;
            });
        }
    });
    
    // 键盘快捷键支持
    angular.element(document).on('keydown', function(event) {
        // Ctrl/Cmd + B 切换侧边栏
        if ((event.ctrlKey || event.metaKey) && event.keyCode === 66) {
            event.preventDefault();
            $scope.$apply(function() {
                $scope.toggleSidebar();
            });
        }
        
        // ESC 键关闭移动端菜单
        if (event.keyCode === 27 && $scope.sidebarMobileOpen) {
            $scope.$apply(function() {
                $scope.closeMobileSidebar();
            });
        }
    });
    
    // 组件初始化
    $scope.init = function() {
        $scope.loadSidebarState();
        $scope.initializeMenus();
        
        // 延迟更新，确保DOM已渲染
        $timeout(function() {
            $scope.updateActiveMenu();
            $scope.updateBreadcrumbs();
        }, 100);
    };
    
    // 清理函数
    $scope.$on('$destroy', function() {
        angular.element($window).off('resize');
        angular.element(document).off('click keydown');
        angular.element('body').removeClass('sidebar-open');
    });
    
    // 初始化
    $scope.init();
}]);

// 侧边栏指令
sidebarModule.directive('sidebar', function() {
    return {
        restrict: 'E',
        templateUrl: '/html/sidebar-layout.html',
        controller: 'SidebarController',
        scope: {},
        link: function(scope, element, attrs) {
            // 确保侧边栏正确初始化
            scope.init();
        }
    };
});