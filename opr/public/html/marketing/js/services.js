var marketingServices = angular.module('marketingServices', []);

marketingServices.factory('Lottery', ['$http', 'HOST', function ($http, HOST) {
    return {
        queryLottery: function (obj) {//查询中奖信息
            return $http.post(HOST.opr + "/marketing/lottery/query", obj).then(commResponseSuccess, commResponseError);
        },
        queryById: function (obj) {//发放
            return $http.post(HOST.opr + "/marketing/lottery/queryById", obj).then(commResponseSuccess, commResponseError);
        },
    };
}]);

marketingServices.factory('MarketUpDown', ['$http', 'HOST', function ($http, HOST) {
    return {
        queryList: function (obj) {
            return $http({ method: 'get', url: HOST.opr + '/marketing/updown/queryList', params: obj }).then(commResponseSuccess, commResponseError);
        },
        queryById: function (obj) {//查询单个记录
            return $http.get(HOST.opr + "/marketing/updown/queryById?id=" + obj).then(commResponseSuccess, commResponseError);
        },
        save: function (obj) {
            return $http.post(HOST.opr + "/marketing/updown/save", obj).then(commResponseSuccess, commResponseError);
        },
        generate: function (obj) {
            return $http.post(HOST.opr + "/marketing/updown/generate", obj).then(commResponseSuccess, commResponseError);
        },
        saveReplaceProduct: function (obj) {
            return $http.post(HOST.opr + "/marketing/updown/saveReplaceProduct", obj).then(commResponseSuccess, commResponseError);
        }
    };
}]);

// 数据文件管理服务
marketingServices.factory('DataFileService', ['$http', 'HOST', function ($http, HOST) {
    return {
        deleteImage: function (obj) {
            return $http.get(HOST.opr + '/data/file/deleteImage?id=' + obj).then(commResponseSuccess, commResponseError);
        },
        addContract: function (obj) {
            return $http.post(HOST.opr + '/data/file/image', obj).then(commResponseSuccess, commResponseError);
        },
        queryFile: function(obj) {
            return $http.post(HOST.opr + '/data/file/query', obj).then(commResponseSuccess, commResponseError);
        }
    };
}]);

// 意外险营销活动
marketingServices.factory('MarketPE', ['$http', 'HOST', function ($http, HOST) {
    return {
        product: function (productType) {
            return $http.get(HOST.opr + '/marketing/promotionExpress/product?productType=' + productType).then(commResponseSuccess, commResponseError);
        },
        addPromotionExp: function (obj) {
            return $http.post(HOST.opr + '/marketing/promotionExpress/addPE', obj).then(commResponseSuccess, commResponseError);
        },
        delete: function (id) {
            return $http.delete(HOST.opr + '/marketing/promotionExpress/' + id).then(commResponseSuccess, commResponseError);
        },
        updatePE: function (obj) {
            return $http.post(HOST.opr + '/marketing/promotionExpress/updatePE', obj).then(commResponseSuccess, commResponseError);
        },
        queryPE: function (obj) {
            return $http.post(HOST.api + 'marketing/queyPE', obj).then(commResponseSuccess, commResponseError);
        },
        queryPEDetail: function (obj) {
            return $http.post(HOST.opr + '/marketing/promotionExpress/queyPEDetail', obj).then(commResponseSuccess, commResponseError);
        },
        exportPEDetail: function (obj) {
            return $http.post(HOST.opr + '/marketing/promotionExpress/queyPEDetail', obj);
        },
        getFeeRate: function (obj) {
            return $http.post(HOST.opr + '/biz/contract/getFeeRate', obj).then(commResponseSuccess, commResponseError);
        },
    };
}
]);

//产品配置
marketingServices.factory('ProductSet', ['$http', 'HOST', function ($http, HOST) {
    return {
        //查询喜报录入列表
        queryList: function (obj) {
            // return $http.post(HOST.opr+ '/marketing/' + "goodnews/queryList", obj);
            return $http.post(HOST.api + 'product/productLists', obj);
        },
        save: function (obj) {//保存登记信息
            return $http.post(HOST.api + 'product/saveProduct', obj).then(commResponseSuccess, commResponseError);
        },
        cacheClear: function () {
            return $http.get(HOST.panda + 'product/cache/clear').then(function (data) {
                return data && data.data;
            });
        },
        addFactor: function (obj) { // 保费试算配置新增字段
            return $http.post(HOST.opr + '/marketing/premiumFactor/addFactor', obj).then(commResponseSuccess, commResponseError);
        },
        queryFactor: function (field) { // 查询保费试算新增因素
            return $http.get(HOST.opr + '/marketing/premiumFactor/queryFactor?field=' + field).then(commResponseSuccess, commResponseError);
        },
        fetchParents: function (obj) {
            return $http.get(HOST.opr + '/marketing/premiumFactor/fetchParents?field=' + obj.field).then(commResponseSuccess, commResponseError)
        },
    };
}]);

// 积分兑换商品
marketingServices.factory('ExchangeService', ['$http', 'HOST', function ($http, HOST) {
    return {
        productSave: function (obj) { // 保存商品信息
            return $http.post(HOST.opr + '/marketing/exchange/productSave', obj).then(commResponseSuccess, commResponseError);
        },
        query: function (obj) { // 查询
            return $http.post(HOST.opr + '/marketing/exchange/query', obj).then(commResponseSuccess, commResponseError);
        },
        addStock: function(obj) { // 追加库存
            return $http.post(HOST.opr + '/marketing/exchange/addStock', obj).then(commResponseSuccess, commResponseError);
        },
        editSave : function(obj){ // 保存修改
            return $http.post(HOST.opr + '/marketing/exchange/editSave', obj).then(commResponseSuccess, commResponseError);
        },
        delGoods : function(id){ // 删除商品
            return $http.delete(HOST.opr + '/marketing/exchange/delete' + id).then(commResponseSuccess, commResponseError);
        },
        queryRecord : function(obj){ // 查询兑换记录
            return $http.post(HOST.opr + '/marketing/exchange/queryRecord',obj).then(commResponseSuccess, commResponseError);
        },
        sendExpress: function(obj){ // 发货信息
            return $http.post(HOST.opr + '/marketing/exchange/sendExpress',obj).then(commResponseSuccess, commResponseError);
        },
        pagingQueryExport: function() { // 导出excel
            return HOST.opr;
     },
    }
}])

// 熊猫课堂
marketingServices.factory('courseService', ['$http', 'HOST', function ($http, HOST) {
    return {
        queryLesson: function (obj) { // 查询课程
            return $http.get(HOST.opr + '/marketing/course/lesson?' + withQuerystring(obj)).then(commResponseSuccess, commResponseError);
        },
        queryLessonById: function (id) { // 查询课程
            return $http.get(HOST.opr + '/marketing/course/lesson/' + id).then(commResponseSuccess, commResponseError);
        },
        deleteLessonById: function (id) { // 删除课程
            return $http.delete(HOST.opr + '/marketing/course/lesson/' + id).then(commResponseSuccess, commResponseError);
        },
        deletePartById: function (id) { // 删除课时
            return $http.delete(HOST.opr + '/marketing/course/lesson/part/' + id).then(commResponseSuccess, commResponseError);
        },
        // 新增、修改课程
        saveLesson: function (obj) {
            return $http.post(HOST.opr + '/marketing/course/lesson', obj).then(commResponseSuccess, commResponseError);
        },
        queryLessonParts: function ({ lessonId, ...obj }) {
            return $http.get(HOST.opr + '/marketing/course/lesson/' + lessonId + '/part?' + withQuerystring(obj)).then(commResponseSuccess, commResponseError);
        },
        // 查询课时
        queryLessonPartById: function (id) {
            return $http.get(HOST.opr + '/marketing/course/lesson/part/' + id).then(commResponseSuccess, commResponseError);
        },
        // 新增、修改课时
        savePart: function (obj) {
            return $http.post(HOST.opr + '/marketing/course/lesson/part', obj).then(commResponseSuccess, commResponseError);
        },

        // 查询试题列表
        queryQuestions: function ({ partId, ...obj }) {
            return $http.get(HOST.opr + '/marketing/course/part/' + partId + '/question?' + withQuerystring(obj)).then(commResponseSuccess, commResponseError);
        },
        // 按id查询试题
        queryQuestionById: function (id) {
            return $http.get(HOST.opr + '/marketing/course/part/question/' + id).then(commResponseSuccess, commResponseError);
        },
        // 按id删除试题
        deleteQuestionById: function (id) {
            return $http.delete(HOST.opr + '/marketing/course/part/question/' + id).then(commResponseSuccess, commResponseError);
        },
        // 新增、修改试题
        saveQuestion: function ({ partId, ...obj }) {
            return $http.post(HOST.opr + '/marketing/course/part/' + partId + '/question', obj).then(commResponseSuccess, commResponseError);
        },
        // 查询答题列表
        queryAnswer: function ({ partId, ...obj }) {
            return $http.get(HOST.opr + '/marketing/course/part/' + partId + '/answer?' + withQuerystring(obj),).then(commResponseSuccess, commResponseError);
        },
    }
}])

const withQuerystring = (params = {}) => {
    return new URLSearchParams(params).toString()
}

