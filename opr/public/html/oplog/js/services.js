var oplogServices = angular.module("oplogServices", []);

oplogServices.factory("OplogManager", [
    "$http",
    "HOST",
    function ($http, HOST) {
        return {
            query: function (params) {
                return $http.get(HOST.pupp + 'tracks?' + transformParams(params)).then(commResponseSuccess, commResponseError);
            },
            generateVideo: function (params) {
                return $http.post(HOST.pupp + 'tracks/video/' + params.session, params)
            },
        };
    },
]);

/**
 * 拼接 get请求 参数
 * @param {*} params
 * @returns
 */
function transformParams(params) {
    var list = [];
    for (var item in params) {
        list.push(item + "=" + params[item]);
    }
    return list.join("&")
}
