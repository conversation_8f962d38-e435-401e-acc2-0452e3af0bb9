'use strict';

let renewableServices = angular.module('renewableServices',[]);

renewableServices.factory('Renewable',['$http','HOST',function($http,HOST){
        let biz = HOST.opr + '/biz/';
        let renewable = HOST.opr + '/renewable/';
        return {
            //获取供应商
            findProviders: function(){
                return $http({method: 'get' ,url: biz + 'contract/findProviders'});
            },
            //根据供应商获取对应签约方
            findParties: function(obj){
                return $http({method: 'get' ,url: biz + 'contract/findParties',params: obj});
            },
            //续保提醒清单--查询
            queryInventory: function(obj){
                return $http({method: 'get', url: renewable + 'queryInventory', params: obj}).then(commResponseSuccess,commResponseError);
            },
            //车险续保提醒清单--提取
            extractInventory: function(obj){
                return $http({method: 'get', url: renewable + 'extractInventory', params: obj}).then(commResponseSuccess,commResponseError);
            },
            //非车续保提醒清单--提取
            extractNocarInventory: function(obj){
                return $http({method: 'get', url: renewable + 'extractNocarInventory', params: obj}).then(commResponseSuccess,commResponseError);
            },
            //查询清单详情列表(查询单个提取日期)
            queryInventoryDetail: function(obj){
                return $http({method: 'get', url: renewable + 'queryInventoryDetail', params: obj}).then(commResponseSuccess,commResponseError);
            },
            //查询清单列表(查询所有日期)
            queryInventoryList: function(obj){
                return $http({method: 'get', url: renewable + 'queryInventoryList', params: obj}).then(commResponseSuccess,commResponseError);
            },
            //生成续保链接
            renewableLink: function(obj){
                return $http({method: 'get', url: renewable + 'renewableLink', params: obj}).then(commResponseSuccess,commResponseError);
            },
            //查询续保录入清单详情列表(查询所有日期)
            queryEnterList: function(obj){
                return $http({method: 'get', url: renewable + 'queryEnterList', params: obj}).then(commResponseSuccess,commResponseError);
            },
            //查询续保录入清单详情列表(查询所有日期)
            queryEnterInventoryList: function(obj){
                return $http({method: 'get', url: renewable + 'queryEnterInventoryList', params: obj}).then(commResponseSuccess,commResponseError);
            },
            //保单查询
            findContract: function (obj) {
                return $http({method: 'get', url: renewable + 'findContract', params: obj}).then(commResponseSuccess,commResponseError);
            },
            //更新保单
            updateContra: function (obj) {
                return $http.post(renewable + 'updateContra',obj).then(commResponseSuccess,commResponseError);
            },
            //提交审核
            pushRenewal: function (obj) {
                return $http({method: 'get', url: renewable + 'pushRenewal', params: obj}).then(commResponseSuccess,commResponseError);
            },
            // 删除清单
            delete: function (obj) {
                return $http({method: 'get', url: renewable + 'delete', params: obj}).then(commResponseSuccess,commResponseError);
            },
            // 暂存审核
            batchSave: function (obj) {
                return $http({method: 'get', url: renewable + 'batchSave', params: obj}).then(commResponseSuccess,commResponseError);
            },
            // 审核退回
            sendBack: function (obj) {
                return $http.post(renewable + 'sendBack',obj).then(commResponseSuccess,commResponseError);
            },
            // 审核成功
            toContract: function (obj) {
                return $http.post(renewable + 'toContract',obj).then(commResponseSuccess,commResponseError);
            },
            // 获取费率
            getFeeRate: function (obj) {
                return $http.post(biz + 'contract/getFeeRate', obj).then(commResponseSuccess, commResponseError);
            },
            //保单处理
            handleRenewableRemind: function(obj){
                return $http.post(renewable + 'handleRenewableRemind',obj).then(commResponseSuccess,commResponseError);
            },
            //查询同一车牌号的历史保单
            queryLienceContract: function (obj) {
                return $http.post(renewable + 'queryLienceContract',obj).then(commResponseSuccess,commResponseError);
            },
            //完成
            successOperation: function(obj){
                return $http.post(renewable + 'successOperation',obj).then(commResponseSuccess,commResponseError);
            },
            //转报价
            createProposal: function (obj) {
                console.log(HOST.createRenewable + 'carRenewable');
                return $http.post(HOST.createRenewable + 'carRenewable',obj).then(commResponseSuccess,commResponseError);
            },
            //根据contractNo查询续保数据
            queryByContractNo: function(obj){
                return $http({method: 'get', url: renewable + 'queryByContractNo', params: obj}).then(commResponseSuccess,commResponseError);
            },
            //续保查询
            queryRenewableList: function(obj){
                return $http({method: 'get', url: renewable + 'queryRenewableList', params: obj}).then(commResponseSuccess,commResponseError);
            }
        };
    }
]);





