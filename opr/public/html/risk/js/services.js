let riskServices = angular.module("riskServices", []);

riskServices.factory("RiskManager", [
    "$http",
    "HOST",
    function ($http, HOST) {
        return {
            queryRiskRuleList: function (params) {
                return $http.get(HOST.opr + '/risk/rule/list?' + transformParams(params)).then(commResponseSuccess, commResponseError);
            },
            queryRiskPromptList: function (params) {
                return $http.post(HOST.opr + '/risk/prompt/list', params).then(commResponseSuccess, commResponseError);
            },
            promptReply: function (params) {
                return $http.post(HOST.opr + '/risk/prompt/reply', params).then(commResponseSuccess, commResponseError);
            },
            promptCheck: function (params) {
                return $http.post(HOST.opr + '/risk/prompt/check', params).then(commResponseSuccess, commResponseError);
            },
            queryPolicyDetail: function(params) {
                return $http.post(HOST.opr + '/risk/prompt/policy/list?' ,params).then(commResponseSuccess, commResponseError);
            }
        };
    },
]);

/**
 * 拼接 get请求 参数
 * @param {*} params
 * @returns
 */
function transformParams(params) {
    var list = [];
    for (var item in params) {
        list.push(item + "=" + params[item]);
    }
    return list.join("&")
}
