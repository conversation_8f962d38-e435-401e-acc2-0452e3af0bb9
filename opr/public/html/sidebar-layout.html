<!-- 侧边栏导航 -->
<nav class="sidebar" ng-class="{'collapsed': sidebarCollapsed, 'mobile-open': sidebarMobileOpen}">
  <!-- 顶部控制区域 -->
  <div class="sidebar-header">
    <!-- 品牌区域 -->
    <a href="#/" class="sidebar-brand">
      <div class="brand-icon">
        <i class="fa fa-shield"></i>
      </div>
      <span class="brand-text">业管运营管理平台</span>
    </a>
    
    <!-- 收起/展开按钮 -->
    <button class="sidebar-toggle-btn" ng-click="toggleSidebar()" title="{{sidebarCollapsed ? '展开菜单' : '收起菜单'}}">
      <i class="fa" ng-class="sidebarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'"></i>
    </button>
  </div>

  <!-- 菜单列表 -->
  <ul class="sidebar-menu">
    <!-- 系统选择下拉 -->
    <li class="has-submenu" ng-class="{'open': systemMenuOpen}" ng-if="permitAppMap">
      <ul class="submenu">
        <li><a href="{{hostUrl}}" target="_blank">{{title}}</a></li>
        <li ng-repeat="(key,value) in permitAppMap">
          <a href="{{value.url}}" target="_blank">{{value.name}}</a>
        </li>
      </ul>
    </li>

    <!-- 主菜单项 -->
    <li ng-repeat="menu in menuTree"
        ng-class="{'has-submenu': menu.children && menu.children.length > 0, 'open': menu.isOpen}"
        ng-click="toggleMenu(menu)">
      <a href="{{menu.children && menu.children.length > 0 ? 'javascript:void(0)' : menu.fullUrl}}"
         ng-class="{'active': isActive(menu.fullUrl)}">
        <i class="menu-icon {{menu.icon || 'fa fa-folder'}}"></i>
        <span class="menu-text">{{menu.text}}</span>
        <i class="menu-arrow fa fa-chevron-right" ng-if="menu.children && menu.children.length > 0"></i>
      </a>

      <!-- 子菜单 -->
      <ul class="submenu" ng-if="menu.children && menu.children.length > 0">
        <li ng-repeat="subMenu in menu.children"
            ng-class="{'has-submenu': subMenu.children && subMenu.children.length > 0, 'open': subMenu.isOpen}">
          <a href="{{subMenu.children && subMenu.children.length > 0 ? 'javascript:void(0)' : subMenu.fullUrl}}"
             ng-class="{'active': isActive(subMenu.url)}"
             ng-click="subMenu.children && subMenu.children.length > 0 && toggleSubMenu(subMenu, $event)">
            {{subMenu.text}}
          </a>

          <!-- 三级菜单 -->
          <ul class="submenu" ng-if="subMenu.children && subMenu.children.length > 0">
            <li ng-repeat="subsubMenu in subMenu.children">
              <a href="{{subsubMenu.fullUrl}}" ng-class="{'active': isActive(subsubMenu.url)}">
                {{subsubMenu.text}}
              </a>
            </li>
          </ul>
        </li>
      </ul>
    </li>
  </ul>
</nav>

<!-- 移动端遮罩层 -->
<div class="sidebar-overlay" ng-class="{'active': sidebarMobileOpen}" ng-click="closeMobileSidebar()"></div>

<!-- 主内容区域 -->
<main class="main-content" ng-class="{'sidebar-collapsed': sidebarCollapsed}">
  <!-- 内容头部 -->
  <div class="content-header">
    <button class="sidebar-toggle" ng-click="toggleMobileSidebar()">
      <i class="fa fa-bars"></i>
    </button>
    <h1 class="header-title">{{currentPageTitle || '首页'}}</h1>
    <div class="header-actions">
      <!-- 用户菜单 -->
      <div class="dropdown" uib-dropdown ng-if="logedInUser">
        <button class="btn btn-default btn-sm" uib-dropdown-toggle>
          {{logedInUser.fullName}} <i class="fa fa-chevron-down"></i>
        </button>
        <ul class="dropdown-menu dropdown-menu-right" uib-dropdown-menu>
          <li><a href="{{umURL}}/user/baseInfo" target="_blank">
            <i class="fa fa-user"></i> 用户信息
          </a></li>
          <li><a href="{{umURL}}/user/resetPassword?source={{hostUrl}}" target="_blank">
            <i class="fa fa-key"></i> 修改密码
          </a></li>
          <li class="divider"></li>
          <li><a href="/logout">
            <i class="fa fa-sign-out"></i> 退出登录
          </a></li>
        </ul>
      </div>
      <a href="/login" class="btn btn-primary btn-sm" ng-if="!logedInUser">
        <i class="fa fa-sign-in"></i> 登录
      </a>
    </div>
  </div>

  <!-- 内容主体 -->
  <div class="content-body">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb" ng-if="breadcrumbs.length > 0">
      <li ng-repeat="crumb in breadcrumbs" ng-class="{'active': $last}">
        <a href="{{crumb.url}}" ng-if="!$last">{{crumb.text}}</a>
        <span ng-if="$last">{{crumb.text}}</span>
      </li>
    </ol>

    <!-- 页面内容 -->
    <div ng-view class="view-frame fade-in"></div>
  </div>
</main>
