'use strict';

const co = require('co');
const moment = require('moment');
const Promise = require('bluebird');
const logger = require('@huibao/logger').logger();
const UUID = require('node-uuid');

const Renewal = require('../models/renewal/Renewal');
const RenewalRemind = require('../models/renewal/RenewalRemind');
const Contract = require('../models/biz/Contract');
const Docking = require('../models/DockingHandle');
const AgentBroker = require('../models/agent/AgentBroker');
const EndorMain = require('../models/biz/EndorMain');
const LifeInfo = require('../models/biz/LifeInsuranceInfo');

const auth = require('../lib/auth');
const restful = require('../controllers/util/restful');
const service = require('../services/bizService');
const commonService = require('../services/commonService');
const endorService = require('../services/endorService');
const endorService_1 = require('../controllers/endor/service');
const messageService = require('../services/messageService');
const utils = require('../lib/utils');
const BackhaulService = require('../services/backhaulService');
const RenewalCommission = require('../models/renewal/RenewalCommission');

// 钉钉审批编号：202209151453000401510 运营给的脱落机构和渠道清单
const FALLOFF_LIST = {
    SLBX71: "101:慧保,102:汇中", // 湖北龙腾
    SLBX12: "101:慧保,102:汇中", // 湖北腾飞
    SLBX48: "101:慧保,102:汇中", // 湖北汇中
    SLBX58: "101:慧保,102:汇中", // 武汉汇保
    SLBX11: "101:慧保,102:汇中", // 武汉长江
    SLBX64: "101:慧保,102:汇中",
    SLBX55: "232:南京颖宋",
    SLBX60: "234:龙腾盛世,270:龙腾二世,288:龙腾三世,246:钜派永誉",
    SLBX17: "258:利琳团队,237:广州俊俊,258:利琳团队",
    SLBX39: "328:上海共建",
    SLBX85: "502:汇中电商",
    SLBX76: "101:慧保,102:汇中",
    SLBX96: "101:慧保,102:汇中,513:汇中北分",
    SLBX32: "101:慧保,102:汇中",
    SLBX74: "281:上海宝鑫",
    SLBX22: "101:慧保,102:汇中,222:奔腾渠道",
    SLBX27: "101:慧保,102:汇中",
    SLBX73: "101:慧保,102:汇中",
    SLBX49: "101:慧保,102:汇中",
    SLBX69: "280:汇中甬诚",
    SLBX61: "101:慧保,102:汇中,230:广西雄飞",
    SLBX26: "101:慧保,102:汇中",
    SLBX0001: "342:汇中读保,202:速保荣博,214:上海海达,218:上海佳雯,220:汇中烨容,223:致美科技,226:速保荣学,227:汇中启航,235:汇中兴盛,249:汇中战将,250:汇中战狼,257:汇中战神,271:汇中星耀,274:汇中赤焰,275:汇中领冠,282:汇中新筹,327:上海战士,247:汇彩金梁,101:慧保,102:汇中,248:铖蒙民鑫,264:汇中拓舟,260:汇中扬帆",
    SLBX33: "101:慧保,102:汇中",
    SLBX54: "101:慧保,102:汇中",
    SLBX08: "101:慧保,102:汇中",
    SLBX25: "101:慧保,102:汇中",
    SLBX20: "101:慧保,102:汇中",
    SLBX51: "101:慧保,102:汇中",
    SLBX05: "101:慧保,102:汇中,207:深圳海岳佳,223:致美科技,225:深圳龙飞,243:深圳红旗,251:深圳富豪,253:深圳丰田,344:深圳富利",
    SLBX31: "101:慧保,102:汇中",
    SLBX36: "101:慧保,102:汇中",
    SLBX41: "101:慧保,102:汇中",
    SLBX04: "101:慧保,102:汇中,259:安徽强盛",
    SLBX47: "101:慧保,102:汇中",
    SLBX101: "101:慧保,102:汇中", // 四川汇中 0313

    // add by lbb 2023.06.30
    SLBX06: "101:慧保,102:汇中", // 佛山鸿翔
    SLBX66: "101:慧保,102:汇中", // 深圳荣威
    SLBX72: "101:慧保,102:汇中", // 陕西汇中
    SLBX92: "101:慧保,102:汇中", // 深圳汇中
    SLBX97: "101:慧保,102:汇中", // 汇中九门

    // add by lbb 2023.09.05
    SLBX89: '515:汇中瓴狮',
};

// 寿险新单回访数据提取
exports.extractCallBack = extractCallBack;

// 续期扣款失败回访数据提取
exports.renewalDeductFailInventry = renewalDeductFailInventry;

// 续期提醒数据提取
exports.extractRenewalInventory = extractRenewalInventory;

// 创建续期保单
exports.createRenewalRemind = createRenewalRemind;

// 续期录入
exports.enterRenewal = enterRenewal;

// 续期审核通过
exports.checkAdopt = checkAdopt;

// 续期扣款导入
exports.deductImport = deductImport;

// 更新续期服务人员
exports.updateRenewalUser = updateRenewalUser;

// 更新Docking中的总记录数
exports.updateDockingCount = updateDockingCount;

/**
 * 寿险新单回访数据提取
 * @param {*} extractDate   提取日期
 * @param {*} user          登录用户
 */
function extractCallBack(extractDate, user) {
    return co(function* () {
        logger.info('寿险回访数据提取---开始');
        // 续期提取总数
        let extractCount = 0;
        /** *** 针对回访数据，发送消息提醒，扣款失败、已失效的数据不发  ******/
        const visitCondition = {
            // channelType: 'GRQD:个人渠道',
            receiptOperateDate: {
                $gte: moment(extractDate).startOf('days').toDate(),
                $lte: moment(extractDate).endOf('days').toDate(),
            },
        };
        logger.info('visitCondition:', JSON.stringify(visitCondition));
        let visitList = yield LifeInfo.find(visitCondition);
        extractCount = visitList.length;

        // 生成清单
        let docking = createCallBackDocking(user, extractCount, extractDate, 6);
        docking = new Docking(docking);
        yield docking.save();
        // 保存操作记录（清单id，操作内容，用户名称）
        commonService.addAperateLog({ _id: docking._id, action: '提取寿险新单' }, user.name || '系统');
        return Promise.resolve();
    }).catch(err => {
        logger.error('extractCallBack--err:', err);
        return Promise.reject(err);
    });
}

// 扣款失败提醒的次数
const remindMap = {
    1: 30, // 30天
    2: 50, // 50天
    3: 61  // 到期后次日
}

/**
 * 续期扣款失败回访数据提取
 * @param {*} extractDate   提取日期
 * @param {*} user          登录用户
 */
 function renewalDeductFailInventry(extractDate, user) {
    return co(function* () {
        logger.info('续期扣款失败回访数据提取---开始');
        // 续期提取总数
        let extractCount = 0;
        const condition = {
            fallOff: '1', // 脱落机构
            contractStatus: '1', // 有效保单
            deductStatus:  { $in: ['2','4'] }, // 待扣款和扣款失败状态
            failRemindDate: {
                $gte: moment(extractDate).startOf('days').toDate(),
                $lte: moment(extractDate).endOf('days').toDate(),
            },
        };
        logger.info('renewal_deduct_fail_condition:', JSON.stringify(condition));
        let list = yield RenewalRemind.find(condition);
        extractCount = list.length;
        let failNo = `R${moment().format('YYYYMMDD')}${+moment()}`;// 批次号
        for(let i = 0; i < list.length; i++) {
            let item  =list[i];
            item.failNo = failNo;
            if(!item.failCallNode) {
                item.failCallNode = 1;
            }else {
                item.failCallNode += 1;
            }
            let day = remindMap[item.failCallNode];
            if(day) {
                item.failRemindDate = moment(item.renewalDate).add(day, 'days').toDate();
            }
            yield item.save();
        }

        // 生成清单
        let docking = createCallBackDocking(user, extractCount, extractDate, 7);
        docking.billNo = failNo;
        docking = new Docking(docking);
        yield docking.save();
        // 保存操作记录（清单id，操作内容，用户名称）
        commonService.addAperateLog({ _id: docking._id, action: '提取续期扣款失败清单' }, user.name || '系统');
        return Promise.resolve();
    }).catch(err => {
        logger.error('extractCallBack--err:', err);
        return Promise.reject(err);
    });
}


/**
 * 续期提醒数据提取
 * @param {*} extractDate   提取日期
 * @param {*} isSendMessage 是否发送app、微信消息    true-发送、false-不发送
 * @param {*} user          登录用户
 */
function extractRenewalInventory(extractDate, isSendMessage, user) {
    return co(function* () {
        logger.info('续期提醒数据提取---开始');
        const condition = {
            effectiveDate: {
                $gte: moment(extractDate).add(-334, 'day').startOf('days')
                    .toDate(),
                $lte: moment(extractDate).add(-334, 'day').endOf('days')
                    .toDate(),
            },
            contractType: '1',
            contractStatus: {$in: ['1', '18', '23'] }, // 为了计算继续率,要把协议解约和现价退保的也提取出来
            'children.planRiderType': '1',
            'children.payfeePeriod': { $gt: 1.1 },
        };
        if (user.branch) {
            auth.availableBranch(condition, 'branch', user, '');
        }
        logger.info('extractInventory------condition:' + JSON.stringify(condition));
        // 续期提取总数
        let extractCount = 0;
        /** *** 针对回访数据，发送消息提醒，扣款失败、已失效的数据不发  ******/
        // const visitCondition = {
        //     extractDate: {
        //         $gte: moment(extractDate).startOf('days').toDate(),
        //         $lte: moment(extractDate).endOf('days').toDate(),
        //     },
        // };
        // let visitList = yield RenewalRemind.find(visitCondition);
        // visitList = JSON.parse(JSON.stringify(visitList));
        // for (let i = 0; i < visitList.length; i++) {
        //     extractCount++;
        //     // 扣款状态:2-扣款失败  5-已失效， 不发消息
        //     if (visitList.deductStatus === '2' || visitList.deductStatus === '5') continue;
        // }

        /** *** 缴费年度为2年，查询Contract,并把数据保存到RenewalRemind *****/
        let billNo = `R${moment().format('YYYYMMDD')}${+moment()}`;// 批次号
        let contract = yield Contract.find(condition);
        contract = JSON.parse(JSON.stringify(contract));
        for (let i = 0; i < contract.length; i++) {
            contract[i].billNo = billNo;
            // 续期提醒数据，重复校验，同缴费年度的保单只提取一次
            const isExists = yield RenewalRemind.findOne({ contractNo: contract[i].contractNo, paymentYear: 2 });
            if (isExists) continue;

            extractCount++;
            // 判断是否脱落机构(渠道)保单
            let _channel = FALLOFF_LIST[contract[i].branch];
            if(_channel) {
                let codes = _channel.split(',');
                if(codes.includes(contract[i].agentType)) {
                    contract[i].fallOff = '1';
                }
            }

            // 创建续期对象
            const renewalRemind = yield createRenewalRemind(contract[i], {}, extractDate, 2, 2);

            // 发送app、微信消息
            if (isSendMessage && contract[i].contractStatus == '1') {
                yield messageService.sendLifeMessage({
                    sendType: ['2', '3'],
                    messageType: '10014',
                    contractId: renewalRemind.contractId,
                    premium: renewalRemind.totalModalPrem,
                    msgType: '保单消息',
                    sType: '2',
                    title: '续期提醒',
                });
            }
            if(contract[i].contractStatus == '1') {
                //添加任务处理托管保单相关
                BackhaulService.asyncTask({ channelCode: 'common', bizNo: renewalRemind.renewalNo, extend: renewalRemind, msgType: 'transRenewalRemind' }, function (info) { console.log(info); });
            }
        }

        /** *** 缴费年度大于2年，查询Renewal,并把数据保存到RenewalRemind *****/
        condition.enterStatus = '3';
        condition.renewalDate = condition.effectiveDate;
        delete condition.effectiveDate;
        logger.info('condition----', JSON.stringify(condition));
        let renewals = yield Renewal.find(condition);
        renewals = JSON.parse(JSON.stringify(renewals));
        for (const renewal of renewals) {
            renewal.billNo = billNo;
            const paymentYear = renewal.paymentYear + 1;
            const durationYear = renewal.durationYear + 1;
            // 续期提醒数据，重复校验，同缴费年度的保单只提取一次
            const isExists = yield RenewalRemind.findOne({ contractNo: renewal.contractNo, paymentYear });
            if (isExists) continue;
            // 续期年度 大于 缴费年期说明客户已缴满，不需要再进行续期了
            if (paymentYear > renewal.period) continue;

            extractCount++;
            // 判断是否脱落机构(渠道)保单
            let _channel = FALLOFF_LIST[renewal.branch];
            if(_channel) {
                let codes = _channel.split(',');
                if(codes.includes(renewal.agentType)) {
                    renewal.fallOff = '1';
                }
            }
            // 创建续期对象
            const renewalRemind = yield createRenewalRemind(renewal, user, extractDate, paymentYear, durationYear);

            // 发送app、微信消息
            if (isSendMessage) {
                yield messageService.sendLifeMessage({
                    sendType: ['2', '3'],
                    messageType: '10014',
                    contractId: renewalRemind.contractId,
                    premium: renewalRemind.totalModalPrem,
                    msgType: '保单消息',
                    sType: '2',
                    title: '续期提醒',
                });
            }
            //添加任务处理托管保单相关
            BackhaulService.asyncTask({ channelCode: 'common', bizNo: renewalRemind.renewalNo, extend: renewalRemind, msgType: 'transRenewalRemind' }, function (info) { console.log(info); });
        }
        // 生成清单
        let docking = createDocking(user, extractCount, extractDate);
        docking.billNo = billNo;
        docking = new Docking(docking);
        yield docking.save();
        // 保存操作记录（清单id，操作内容，用户名称）
        commonService.addAperateLog({ _id: docking._id, action: '提取清单' }, user.name || '系统');
        return Promise.resolve();
    }).catch(err => {
        logger.error('extractRenewalInventory--err:', err);
        return Promise.reject(err);
    });
}


/**
 * 创建续期保单
 * @param {*} contract      保单对象
 * @param {*} user          当前登录用户
 * @param {*} extractDate   提取日期
 * @param {*} paymentYear   缴费年度
 * @param {*} durationYear  保单年度
 */
function createRenewalRemind(contract, user, extractDate, paymentYear, durationYear) {
    return co(function* () {
        logger.info('createRenewalRemind===>: ', contract._id);
        if (contract.children) {
            for (let j = 0; j < contract.children.length; j++) {
                const child = contract.children[j];
                child.paymentYear = paymentYear;
                child.feeList = [];
                child.agencyfee = 0;
                child.agencyfeeRate = 0;
                child.commissionPrem = 0;
                child.commissionRate = 0;
                child.firstRecomm = 0;
                child.secondRecomm = 0;
                child.premiumPeriod = child.premiumPeriod || '1';// 保险年期没有默认1
                child.payfeePeriod = child.payfeePeriod || 1; // 缴费年期没有默认1
                delete child.productFlag;

                // 取主险的'保险年期'和'缴费年期',否则取附加险的
                if (child.planRiderType === '1') {
                    contract.payfeePeriod = child.payfeePeriod;
                    contract.premiumPeriod = child.premiumPeriod;
                }
            }
        }

        // 更新续期服务人员
        if (contract.userCode !== contract.serUserCode) {
            yield updateRenewalUser(contract, contract.serUserCode);
        }

        contract.cySubsidy = 0;
        contract.agencyfee = 0;
        contract.agencyfeeRate = 0;
        contract.commissionPrem = 0;
        contract.commissionRate = 0;
        contract.firstRecomm = 0;
        contract.secondRecomm = 0;
        // 个人渠道的保单，才会在续期提醒清单中显示
        // if (contract.channelType === 'GRQD:个人渠道') {
        contract.extractDate = extractDate;
        // }

        delete contract.enterStatus;
        delete contract.infoChangeType;
        delete contract.rateFlag;
        delete contract.paymentDate;
        delete contract.deductDate;
        delete contract.deductStatus;
        delete contract.uploadId;
        delete contract.renewalFinishStatus;
        delete contract.checkDate;
        delete contract.enterDate;

        contract.paymentYear = paymentYear; // 缴费年度
        contract.durationYear = durationYear; // 保单年度
        // 续年度起保日期：从renewal表提取“续年度起保日期”是11个月前的数据
        const date = contract.renewalDate || contract.effectiveDate;
        contract.renewalDate = moment(date).add(1, 'year').toDate();
        contract.failRemindDate = contract.renewalDate; // 扣款失败提醒日期
        contract.renewalNo = `RE${contract.paymentYear}` + moment().format('YYYYMMDDHHmmssms') + contract._id.substr(contract._id.length - 4);
        contract.createBy = user.name || 'system';
        contract.createdAt = new Date();
        contract.updateBy = user.name || 'system';
        contract.updatedAt = new Date();
        contract.contractId = contract.contractId || contract._id;
        delete contract._id;
        const renewalRemind = new RenewalRemind(contract);
        yield renewalRemind.save();
        return Promise.resolve(renewalRemind);
    }).catch(err => {
        logger.error('createRenewalRemind-err:', err);
        return Promise.reject(err);
    });
}

/**
 * 续期录入
 * @param {*} renewalRemind   续期保单对象
 * @param {*} user   当前登录用户
 */
function enterRenewal(renewalRemind, user) {
    return co(function* () {
        const contract = yield Contract.findOne({ _id: renewalRemind.contractId, contractStatus: '1' });
        if (!contract) return Promise.resolve({ code: '999', msg: '该保单不是已承保状态！' });
        const condition = {
            renewalNo: renewalRemind.renewalNo,
        };
        // 计算时不传费用类型，接口自动获取该保单当前年度费率
        for (const child of renewalRemind.children) {
            child.feeList = [];
            child.agencyfeeRecover = 0;
            child.agencyFeeRate = 0;
            child.agencyfee = 0;
        }
        // 删除的附加险不进行利益计算,剔除掉
        let cancelRisk = renewalRemind.children.filter(x=> x.productFlag == '2');
        renewalRemind.children = renewalRemind.children.filter(x=> x.productFlag != '2');
        // 利益计算
        const calcResult = yield service.calcLifeProfitAsync(renewalRemind, false);
        if (calcResult.isSuccess === 'F') {
            return Promise.resolve({ code: '999', msg: calcResult.errorReason });
        }
        const data = JSON.parse(JSON.stringify(calcResult.data));
        renewalRemind.children = data.children;
        // 删除的附加险利益计算后重新加回children,供后续生成保全记录
        if(cancelRisk.length > 0) {
            renewalRemind.children = renewalRemind.children.concat(cancelRisk);
        }
        renewalRemind.totalModalPrem = 0; renewalRemind.agencyfee = 0; renewalRemind.commissionPrem = 0;
        renewalRemind.faceaMoutTotle; renewalRemind.agencyfeeRecover = 0;
        for (const child of data.children) {
            if (child.productFlag === '2') continue;
            renewalRemind.totalModalPrem += Number(child.totalModalPrem);
            renewalRemind.agencyfee += Number(child.agencyfee);
            renewalRemind.commissionPrem += Number(child.commissionPrem);
            renewalRemind.faceaMout += Number(child.faceaMout);
            renewalRemind.agencyfeeRecover += Number(child.agencyfeeRecover) || 0;
        }
        renewalRemind.cySubsidy = data.cySubsidy || 0;
        renewalRemind.issueBranch = data.issueBranch;
        renewalRemind.firstRecomm = data.firstRecomm || 0;
        renewalRemind.secondRecomm = data.secondRecomm || 0;
        renewalRemind.hasTax = data.hasTax;
        renewalRemind.extaxModalPrem = data.extaxModalPrem || 0;
        renewalRemind.vatAmount = data.vatAmount || 0;
        renewalRemind.partiesId = data.partiesId;
        renewalRemind.partiesName = data.partiesName;
        renewalRemind.vatRate = Math.round((1 - data.extaxModalPrem / renewalRemind.totalModalPrem) * 100);
        renewalRemind.enterStatus = renewalRemind.status;
        renewalRemind.updateBy = user.name;
        renewalRemind.updatedAt = new Date();
        renewalRemind.enterDate = new Date();

        let renewal = yield Renewal.findOne(condition);
        // 续期修改
        if (renewal) {
            renewalRemind.updateBy = user.name;
            renewalRemind.updatedAt = new Date();
            delete renewalRemind._id;
            yield Renewal.findOneAndUpdate(condition, renewalRemind);
        } else { // 续期录入
            renewalRemind.createBy = user.name;
            renewalRemind.createdAt = new Date();
            delete renewalRemind._id;
            renewal = new Renewal(renewalRemind);
            yield renewal.save();
        }
        // 把续期提醒的数据录入状态标记为处理中
        yield RenewalRemind.findOneAndUpdate(condition, { enterStatus: '1' });
        const action = renewalRemind.status === '1' ? '提交' : '暂存';
        // 保存操作记录
        commonService.addAperateLog({ _id: renewal._id, action }, user);
        return Promise.resolve(renewal);
    }).catch(err => {
        logger.error('enterRenewal--err:', err);
        return Promise.reject(err);
    });
}

/**
 * 审核通过
 * @param {*} renewal       续期保单对象
 * @param {*} user          当前登录用户
 * @param {*} checkDate     审核时间
 */
function checkAdopt(renewal, user, checkDate) {
    return co(function* () {
        if (renewal.contractStatus !== '1') return Promise.reject('保单状态不是已承保，审核失败！');
        renewal = JSON.parse(JSON.stringify(renewal));
        renewal.token = user.token;
        renewal.unfreezeDate = moment(new Date()).add(1, 'day').startOf('day')
            .add(12, 'hour')
            .toDate();

        // 利益发放给服务人员，如果有的话
        renewal.userCode = renewal.serUserCode || renewal.userCode;
        renewal.agentName = renewal.serAgentName || renewal.agentName;
        const renewal_before = JSON.parse(JSON.stringify(renewal));
        // 剔除掉已取消的险种 update by lbb 2022-11-08
        renewal.children = renewal.children.filter(x=> x.productFlag != '2');
        renewal.checkDate = checkDate;

        // 手续费对账清单
        service.generateBrokerageBillC(renewal);

        // 利益结算
        let commission = yield queryRenewalCommission(renewal);
        console.log('commission:',JSON.stringify(commission));
        if(commission) {
            const settleResult = yield service.calcLifeProfitAsync(commission, true);
            if (settleResult.isSuccess === 'F') {
                yield updateRenewalCommission(commission, 'N', settleResult.errorReason);
                return Promise.reject(settleResult.errorReason);
            }
            yield updateRenewalCommission(commission, '1', '');
        }

        const update_renewal = {
            enterStatus: '3',
            deductStatus: '3',
            renewalFinishStatus: '1',
            checkDate,
            // acceptDate: checkDate,
            // approveDate: checkDate,
            // reportDate: checkDate,
            updateBy: user.name,
            updatedAt: new Date(),
            children: renewal.children,
        };
        const update_remind = {
            enterStatus: '2',
            deductStatus: '3',
            renewalFinishStatus: '1',
            checkDate,
            children: renewal.children,
            agencyfee: renewal.agencyfee,
            issueBranch: renewal.issueBranch,
            commissionPrem: renewal.commissionPrem,
            extaxModalPrem: renewal.extaxModalPrem,
            totalModalPrem: renewal.totalModalPrem,
            vatAmount: renewal.vatAmount || 0,
        };
        renewal = yield Renewal.findOneAndUpdate({ renewalNo: renewal.renewalNo, enterStatus: '2' }, update_renewal, { new: true });

        // 续期提醒表：录入状态标记为2（已完成）,扣款状态标记为3（已扣款）,续期完成状态标记为1（已续期）
        yield RenewalRemind.findOneAndUpdate({ renewalNo: renewal.renewalNo }, update_remind);
        yield Contract.update({ contractNo: renewal.contractNo }, { $set: { paymentYear: renewal.paymentYear } });

        // 信息变化类型
        if (renewal_before.infoChangeType.length > 0) {
            yield createEndor(renewal_before, user);
        }

        // 保存操作记录
        commonService.addAperateLog({ _id: renewal._id, action: '审核通过' }, user);
        BackhaulService.asyncTask({ channelCode: 'common', bizNo: renewal.renewalNo, extend: renewal, msgType: 'transRenewalSucc' }, function (info) { console.log(info); });

    }).catch(err => {
        logger.error('checkAdopt--err===:', err);
        return Promise.reject(err || '审核失败！');
    });
}


/**
 * 续期扣款导入:
 * 状态：
 * 2-成功、3-失败：发送消息提醒；
 * 4-待扣款；
 * 5-失效：生成保全批单；
 * @param {*} data  导入的数据
 * @param {*} user  登录用户
 */
function deductImport(data, user) {
    return co(function* () {
        const importRecords = [];
        // 定义所有扣款状态
        const deduct = {
            '失败': '2',
            '成功': '3',
            '待扣款': '4',
            '失效': '5',
        };
        // 续期录入表变更对象
        const update_renewal = { updateBy: user.name, updatedAt: moment().toDate(), };
        // 扣款记录
        const deductOperate = { operateBy: user.name, operatedAt: moment().toDate() };

        for (let i = 0; i < data.length; i++) {
            const rowObj = data[i];
            logger.info('deductImport==rowDate==> ', JSON.stringify(rowObj));
            const contractNo = rowObj.contractNo;                       // 保单号
            const paymentYear = rowObj.paymentYear;                 // 缴费年度
            const deductStatus = rowObj.deductStatus;                // 扣款状态
            let deductDate = utils.handleDate(rowObj.deductDate); // 扣款时间
            const remark = rowObj.remark;                                  // 备注

            if (!contractNo && !paymentYear) continue;
            if (!contractNo) {
                importRecords.push({ contractNo: '', message: '第' + i + '条记录，保单号不能为空。' });
                continue;
            }
            if (!paymentYear) {
                importRecords.push({ contractNo, message: '第' + i + '条记录，缴费年度不能为空。' });
                continue;
            }
            if (!deductStatus) {
                importRecords.push({ contractNo, message: '第' + i + '条记录，扣费状态不能为空。' });
                continue;
            }
            const status = deduct[deductStatus];
            if (!status) {
                importRecords.push({ contractNo, message: '第' + i + '条记录，扣费状态只能是“成功、失败、待扣款、失效”中的一个。' });
                continue;
            }
            const condition = {
                contractNo,
                paymentYear,
            };
            // 查询续期提醒表
            const renewalRemind = yield RenewalRemind.findOne(condition);
            if (!renewalRemind) {
                importRecords.push({ contractNo, message: '第' + i + '条记录，查询不了该保单号。' });
                continue;
            }// 原扣款状态为“成功”
            else if (renewalRemind.deductStatus === '3') {
                const message = status === '3' ? '第' + i + '条记录，该保单当前年度扣款状态为“成功”，无须操作！' : '第' + i + '条记录，此次导入数据不成功，请核实是否录入有误。';
                importRecords.push({ contractNo, message });
                continue;
            } else if (renewalRemind.enterStatus === '2') {
                const message = '第' + i + '条记录，该保单当前年度已续期已存在，状态为"已审核"！';
                importRecords.push({ contractNo, message });
                continue;
            } else if (renewalRemind.deductStatus === '5') {
                const message = '第' + i + '条记录，该保单当前年度扣款状态为“已失效”，无须操作！';
                importRecords.push({ contractNo, message });
                continue;
            } else if (status === '5') {
                const period = moment(renewalRemind.renewalDate).add(2, 'month').toDate();
                if (moment().diff(period, 'seconds') < 0) {
                    const message = '第' + i + '条记录，此次导入数据不成功，请核实是否过宽限期。';
                    importRecords.push({ contractNo, message });
                    continue;
                }
            }
            // 扣款时间非必填
            if (deductDate) {
                if (new Date(deductDate) === 'Invalid Date') {
                    importRecords.push({ contractNo, message: '第' + i + '条记录，扣款时间格式错误！' });
                    continue;
                } else {
                    deductDate = moment(deductDate).format('YYYY-MM-DD HH:mm:ss');
                    update_renewal.deductDate = deductDate;
                    deductOperate.deductDate = deductDate;
                    renewalRemind.deductDate = deductDate;
                }
            }
            // 修改续期表
            update_renewal.deductStatus = status;
            yield Renewal.findOneAndUpdate(condition, update_renewal);
            // 扣款操作记录
            deductOperate.deductStatus = status;
            deductOperate.remark = remark;
            renewalRemind.deductOperateList = renewalRemind.deductOperateList || [];
            renewalRemind.deductOperateList.push(deductOperate);
            renewalRemind.deductStatus = status;
            renewalRemind.updateBy = user.name;
            renewalRemind.updatedAt = new Date();

            // 导入扣款状态: 2-失败、发消息提醒
            if (status === '2') {
                if (renewalRemind.failedCount <= 0) {
                    // 推送app、短信消息(扣款失败只发一次)
                    yield messageService.sendLifeMessage({
                        sendType: ['2', '3'],
                        messageType: '10011',
                        contractId: renewalRemind.contractId,
                        premium: renewalRemind.totalModalPrem,
                        msgType: '保单消息',
                        sType: '2',
                        title: '续期扣款失败',
                    });
                }
                renewalRemind.failedCount = isNaN(renewalRemind.failedCount) ? 1 : ++renewalRemind.failedCount;
                // 扣款失败次数为第一次时,此保单转移至当前时间的第二天，由续期提醒再次提取，但不发消息提醒
                if (renewalRemind.failedCount <= 1) {
                    yield updateDockingCount(renewalRemind.extractDate);
                    renewalRemind.extractDate = moment().add(1, 'day').toDate();
                    renewalRemind.callNode = '2';
                    renewalRemind.renewalStatus = '0';
                }
            } // 导入扣款状态: 3-成功、发消息提醒,修改原保单缴费年度
            else if (status === '3') {
                // 推送app、短信消息
                yield messageService.sendLifeMessage({
                    sendType: ['2', '3'],
                    messageType: '10012',
                    contractId: renewalRemind.contractId,
                    premium: renewalRemind.totalModalPrem,
                    msgType: '保单消息',
                    sType: '3',
                    title: '续期扣款成功',
                });
                // 扣款成功更新保单续期年度  update by lbb 2023-03-09
                yield Contract.update({ contractNo }, { $set: { paymentYear: renewalRemind.paymentYear } });
            }// 导入扣款状态：5-失效，生成保全批单
            else if (status === '5') {
                let endorNo = 'ET14' + UUID.v1();
                endorNo = endorNo.replace(/-/g, '');
                let _contract = yield Contract.findOne({ contractNo, contractStatus: '1', contractType: '1' });
                if (!_contract) {
                    importRecords.push({ contractNo, message: '第' + i + '条记录，导入成功！生成“保单失效”批单失败，原因：保单不是“已承保”状态!' });
                    continue;
                }
                const endor_temp = yield EndorMain.findOne({ endorNo });
                if (endor_temp) {
                    importRecords.push({ contractNo, message: '第' + i + '条记录，导入成功！生成“保单失效”批单失败，原因：批单号已存在！' });
                    continue;
                }
                const nowDate = new Date();
                const endorModel = {
                    endorType: '14',
                    applyDate: nowDate,
                    applyMan: user.fullName,
                    endorNo,
                    endorTime: nowDate,
                    applyReason: '续期扣款导入：失效',
                    contractId: renewalRemind.contractId,
                };
                yield endorService.applyType14_15(renewalRemind, endorModel, user);
                // 备份保全数据
                const serialNo = yield endorService_1.genePolicyCopyMain(endorModel, user);
                logger.info('serialNo==', serialNo);
                // 批单：审核通过
                yield EndorMain.update({ endorNo }, { $set: { endorStatus: '2', serialNo, checkDate: nowDate } });
                // 保单更新为：失效
                yield Contract.update({ contractNo }, { $set: { bigStatus: '3', contractStatus: '16' } });
                yield messageService.sendLifeMessage({
                    sendType: ['2', '3'],
                    messageType: '10010',
                    contractId: renewalRemind.contractId,
                    msgType: '保单消息',
                    sType: '16',
                    title: '扣款失效',
                });
                renewalRemind.callNode = '4';
                renewalRemind.renewalStatus = '0';
                renewalRemind.bigStatus = '3';
                renewalRemind.contractStatus = '16';
                // 操作记录保存续期扣款失效,此保单转移至当前时间的第二天，由续期提醒再次提取，但不发消息提醒
                yield updateDockingCount(renewalRemind.extractDate);
                // 失效同步至保单托管保单
                restful.syncInvalid({ contractNo }, () => { });
                renewalRemind.extractDate = moment().add(1, 'day').toDate();
            }
            yield renewalRemind.save();
        }
        if (importRecords.length <= 0) {
            importRecords.push({ message: '导入成功！' });
        }
        return Promise.resolve(importRecords);
    }).catch(err => {
        logger.error('deductImport==err==>', err);
        return Promise.reject(err);
    });
}

/**
 * 创建清单对象
 * @param {*} user              --当前登录用户的信息
 * @param {*} extractCount      --提取的笔数
 * @param {*} extractDate       --提取日期
 */
function createDocking(user, extractCount, extractDate) {
    const docking = {
        branch: user.branch || global.branch,
        count: extractCount,
        extractDate: extractDate || new Date(),
        type: 1,
        status: '1',
        createBy: user.name || '系统',
        createdAt: new Date(),
        updatedAt: new Date(),
        updateBy: user.fullName || '系统',
    };
    return docking;
}

/**
 * 创建清单对象
 * @param {*} user              --当前登录用户的信息
 * @param {*} extractCount      --提取的笔数
 * @param {*} extractDate       --提取日期
 */
function createCallBackDocking(user, extractCount, extractDate, type) {
    const docking = {
        branch: user.branch || global.branch,
        count: extractCount,
        extractDate: extractDate || new Date(),
        type: type,
        status: '1',
        createBy: user.name || '系统',
        createdAt: new Date(),
        updatedAt: new Date(),
        updateBy: user.fullName || '系统',
    };
    return docking;
}

/**
 * 更新续期服务人员
 * @param {*} contract 更新结果
 * @param {*} serUserCode 服务人员代码
 */
function updateRenewalUser(contract, serUserCode) {
    return co(function* () {
        const agentBroker = yield AgentBroker.findOne({ userCode: serUserCode });
        if (!agentBroker) return;
        contract.agentId = agentBroker._id;
        contract.branch = agentBroker.branchCode;
        contract.channelType = agentBroker.channelType;
        contract.agentType = agentBroker.agentType;
        contract.agentName = agentBroker.agentName;
        contract.userCode = agentBroker.userCode;
        contract.serUserCode = agentBroker.userCode;
        contract.serAgentName = agentBroker.agentName;
    }).catch(err => {
        return Promise.reject(err);
    });
}

/**
 * 根据信息变化类型生成保全记录
 * @param {*} renewal_before 原续期保单
 * @param {*} renewal_after 变更后的续期保单
 */
function createEndor(renewal, user) {
    return co(function* () {
        const remind = yield RenewalRemind.findOne({ renewalNo: renewal.renewalNo });
        if (!remind) return Promise.reject('未找到续期保单，续期单号【' + renewal.renewalNo + '】');
        const type = {
            1: '12', // 增加附加险，对应保全类型【增减保】
            2: '12', // 减少附加险，对应保全类型【增减保】
            3: '12', // 增减保，对应保全类型【增减保】
            4: '18', // 减额缴清，对应保全类型【减额缴清】
        };
        const infoChangeType = {
            1: '增加附加险',
            2: '取消附加险',
            3: '加/减保',
            4: '减额缴清',
        };
        const endor = {
            contractId: renewal.contractId,
            contractType: '1',
            contractNo: renewal.contractNo,
            applyDate: new Date(),
            endorTime: new Date(),
            endorStatus: '0',
            applyMan: user.fullName,
        };
        let num = 1;
        for (const item of renewal.infoChangeType) {
            const renewal_before = JSON.parse(JSON.stringify(remind));
            const renewal_after = JSON.parse(JSON.stringify(renewal));
            const changeObj = yield renewalChangeToEndor(renewal_before, renewal_after, item);
            let endorType = type[item];
            endor.endorType = endorType;
            endor.endorNo = 'ET' + endorType + moment().format('YYYYMMDDHHmmssms') + renewal.contractNo.substr(-4) + num;
            endorType === '12' ? endorType += '_nocar' : undefined;
            endor.changeObj = changeObj;
            endor.applyReason = '续期审核=》信息变化类型【' + infoChangeType[item] + '】';
            yield endorService['applyType' + endorType](renewal_after, endor, user);
            yield EndorMain.update({ endorNo: endor.endorNo }, { checkDate: new Date(), endorStatus: '2' });
            num++;
        }
    }).catch(err => {
        logger.info('createEndor--err: ', err.message, err.stack);
        if (err.message === '没有变更的产品！') err.message = '信息变化类型与实际产品操作不同，无法生成保全记录！';
        return Promise.reject(err);
    });
}
/**
 * 根据信息变化类型生成保全记录
 * @param {*} renewal_before 原续期保单
 * @param {*} renewal_after 变更后的续期保单
 * @param {*} type 信息变化类型: 1-增加附加险 2-取消附加险 3-加/减保 4-减额缴清
 */
function renewalChangeToEndor(renewal_before, renewal_after, type) {
    return co(function* () {
        const changeObj = {
            renewalFlag: true,
            faceaMoutC: 0,
            cySubsidyC: 0,
            totalModalPremC: 0,
            commissionPremC: 0,
            secondRecommC: 0,
            firstRecommC: 0,
            agencyfeeC: 0,
        };
        for (let i = 0; i < renewal_after.children.length; i++) {
            const child_after = renewal_after.children[i];
            if (type === '1') {
                if (child_after.productFlag !== '1') {
                    delete child_after.productFlag;
                    continue;
                }
                child_after.changeSecondRecomm = 0;
                child_after.changeFirstRecomm = 0;
                child_after.changeTotalModalPrem = child_after.totalModalPrem || 0;
                child_after.changeCommissionRate = child_after.commissionRate || 0;
                child_after.changeCommissionPrem = Number(child_after.commissionPrem) || 0;
                child_after.changeAgencyFeeRate = child_after.agencyFeeRate || 0;
                child_after.changeFaceaMout = child_after.faceaMout || 0;
                child_after.changeAgencyfee = Number(child_after.agencyfee) || 0;
                changeObj.faceaMoutC += child_after.changeFaceaMout;
                changeObj.totalModalPremC += child_after.changeTotalModalPrem;
                changeObj.agencyfeeC += child_after.changeAgencyfee;
                changeObj.commissionPremC += child_after.changeCommissionPrem;
                changeObj.detailType = '3';
                continue;
            } else if (type === '2') {
                if (child_after.productFlag !== '2') {
                    delete child_after.productFlag;
                    continue;
                }
                child_after.changeSecondRecomm = 0;
                child_after.changeFirstRecomm = 0;
                child_after.changeTotalModalPrem = -child_after.totalModalPrem || 0;
                child_after.changeCommissionRate = child_after.commissionRate || 0;
                child_after.changeCommissionPrem = Number(-child_after.commissionPrem) || 0;
                child_after.changeAgencyFeeRate = child_after.agencyFeeRate || 0;
                child_after.changeFaceaMout = -child_after.faceaMout || 0;
                child_after.changeAgencyfee = Number(-child_after.agencyfee) || 0;
                changeObj.faceaMoutC -= child_after.faceaMout;
                changeObj.totalModalPremC -= child_after.totalModalPrem;
                changeObj.agencyfeeC -= child_after.agencyfee;
                changeObj.commissionPremC -= child_after.commissionPrem;
                changeObj.detailType = '4';
                continue;
            } else if (type === '3') {
                delete child_after.productFlag;
            }
            for (const child_before of renewal_before.children) {
                if (child_before.productId === child_after.productId) {
                    if (type === '4') { // 减额缴清
                        if (child_after.faceaMout - child_before.faceaMout === 0) continue;

                        child_after.changeFaceaMout = child_after.faceaMout - child_before.faceaMout;
                        child_after.changeTotalModalPrem = child_after.totalModalPrem - child_before.totalModalPrem;
                        child_after.changePayfeePeriod = child_after.payfeePeriod - child_before.payfeePeriod;

                        child_after.faceaMout = child_before.faceaMout;
                        child_after.totalModalPrem = child_before.totalModalPrem;
                        child_after.payfeePeriod = child_before.payfeePeriod;

                    } else if (type === '3') { // 加/减保
                        if (child_after.totalModalPrem - child_before.totalModalPrem === 0) continue;
                        child_after.changeSecondRecomm = 0;
                        child_after.changeFirstRecomm = 0;
                        child_after.changeTotalModalPrem = child_after.faceaMout - child_before.faceaMout;
                        child_after.changeTotalModalPrem = child_after.totalModalPrem - child_before.totalModalPrem;
                        child_after.changeAgencyfee = child_after.agencyfee - child_before.agencyfee;
                        child_after.changeCommissionPrem = child_after.commissionPrem - child_before.commissionPrem;
                        child_after.changeAgencyFeeRate = child_after.agencyFeeRate || 0;
                        child_after.changeCommissionRate = child_after.commissionRate || 0;

                        child_after.faceaMout = child_before.faceaMout;
                        child_after.totalModalPrem = child_before.totalModalPrem;
                        child_after.agencyfee = child_before.agencyfee;
                        child_after.commissionPrem = child_before.commissionPrem;
                        child_after.productFlag = '3';

                        changeObj.faceaMoutC += child_after.faceaMout;
                        changeObj.totalModalPremC += child_after.totalModalPrem;
                        changeObj.agencyfeeC += child_after.agencyfee;
                        changeObj.commissionPremC += child_after.commissionPrem;

                        changeObj.detailType = child_after.totalModalPrem > 0 ? '1' : '2';
                    }
                }
            }
        }
        changeObj.children = renewal_after.children;
        return Promise.resolve(changeObj);
    }).catch(err => {
        logger.info('renewalChangeToEndor--err: ', err);
        return Promise.reject(err);
    });
}

/**
 * 更新docking总数
 * @param {*} extractDate 提取时间
 */
function updateDockingCount(extractDate) {
    return co(function* () {
        const condition = {
            type: '1',
            extractDate: {
                $gte: moment(extractDate).startOf('days').toDate(),
                $lte: moment(extractDate).endOf('days').toDate(),
            },
        };
        yield Docking.update(condition, { $inc: { count: -1 } });
    }).catch(err => {
        logger.info('updateDockingCount--err:', err);
        return Promise.reject(err);
    });
}



/**
 * 百年人寿续期数据查询
 */
exports.bnrs_renewalQuery = (obj) => {
    return co(function* () {
        const startDate = moment(obj && obj.startDate).add(-2, 'd').format('YYYY-MM-DD');
        const endDate = moment(obj && obj.endDate).add(-1, 'd').format('YYYY-MM-DD');
        let list = []
        let types = ['1', '2', '3', '4'] //1 应收数据、2 实收数据、3 转账失败数据、4 保单失效数据
        for (let i = 0; i < types.length; i++) {
            let queryType = types[i];
            const reqObj = { startDate, endDate, queryType, productCode: 'BNKHBZDJBBX' };
            const resObj = yield restful.renewalQueryAsync(reqObj);
            if (resObj.code == '000') {
                resObj.list.map(m => {
                    m.queryType = queryType;
                    list.push(m);
                })
            }
        }

        let deduct_status = {
            '1': '待扣款',
            '2': '成功',
            '3': '失败',
            '4': '失效'
        }
        let deduct_list = []
        let uniqueSet = new Set();//可能有重复数据Set去重
        for (let i = 0; i < list.length; i++) {
            const rObj = list[i];
            let tmpObj = {};
            if (uniqueSet.has(rObj.contractNo)) continue
            tmpObj.contractNo = rObj.contractNo;
            if (rObj.queryType == '4' && ~['101', '309'].indexOf(rObj.status)) continue;
            tmpObj.paymentYear = parseInt(rObj.payCount);           // 缴费年度
            tmpObj.deductStatus = deduct_status[rObj.queryType]; // 扣款状态
            tmpObj.deductDate = rObj.enterAccDate || '';                // 扣款时间
            tmpObj.remark = rObj.transferResult || '';
            deduct_list.push(tmpObj);
            uniqueSet.add(rObj.contractNo);
        }
        console.log('bnrs_deduct_list:', JSON.stringify(deduct_list))
        if (deduct_list.length > 0) {
            yield deductImport(deduct_list, { name: 'system', fullName: '自动_百年api获取' });
        }
        return deduct_list;
    }).catch(err => {
        logger.info('bnrs获取续期数据出错', err);
        return {};
    });
}


/**
 * 瑞华健康续期数据查询
 */
 exports.rhjk_renewalQuery = (obj) => {
    return co(function* () {
        const startDate = moment(obj && obj.startDate).add(-2, 'd').format('YYYY-MM-DD');
        const endDate = moment(obj && obj.endDate).add(-1, 'd').format('YYYY-MM-DD');
        let list = []
        let types = ['2'] //1 应收数据、2 实收数据、3 转账失败数据、4 保单失效数据
        for (let i = 0; i < types.length; i++) {
            let queryType = types[i];
            const reqObj = { startDate, endDate, channel: obj.channel, queryType, productCode: 'RHJKYYWYZSHLBX' };
            const resObj = yield restful.renewalQueryAsync(reqObj);
            if (resObj.code == '000') {
                resObj.list.map(m => {
                    m.queryType = queryType;
                    list.push(m);
                })
            }
        }

        let deduct_status = {
            '1': '待扣款',
            '2': '成功',
            '3': '失败',
        }
        let deduct_list = []
        let uniqueSet = new Set();//可能有重复数据Set去重
        for (let i = 0; i < list.length; i++) {
            const rObj = list[i];
            let tmpObj = {};
            if (uniqueSet.has(rObj.contractNo)) continue
            tmpObj.contractNo = rObj.contractNo;
            tmpObj.paymentYear = parseInt(rObj.payCount); // 缴费年度
            if(rObj.actuPrem && rObj.enterAccDate) {
                tmpObj.deductStatus = deduct_status['2']; // 成功
                tmpObj.deductDate = rObj.enterAccDate;    // 扣款时间
                tmpObj.remark = "保司续期查询->扣款成功!";
            } else{
                if(rObj.transferResult) {
                    tmpObj.remark = rObj.transferResult;
                    tmpObj.deductStatus = deduct_status['3'];
                } else {
                    tmpObj.remark = '待扣款';
                    tmpObj.deductStatus = deduct_status['1'];
                }
            }
            deduct_list.push(tmpObj);
            uniqueSet.add(rObj.contractNo);
        }
        console.log('rhjk_deduct_list:', JSON.stringify(deduct_list))
        if (deduct_list.length > 0) {
            yield deductImport(deduct_list, { name: 'system', fullName: '自动_瑞华健康api获取' });
        }
        return deduct_list;
    }).catch(err => {
        logger.info('rhjk获取续期数据出错', err);
        return {};
    });
}

/**
 * 华贵人寿续期数据查询
 */
exports.hgrs_renewalQuery = (obj) => {
    return co(function* () {
        const startDate = moment(obj && obj.startDate).add(-2, 'd').format('YYYY-MM-DD');
        const endDate = moment(obj && obj.endDate).add(-1, 'd').format('YYYY-MM-DD');
        let list = []
        let types = ['0', '1', '2'] //0 未交费、1 交费成功、2 交费失败
        for (let i = 0; i < types.length; i++) {
            let state = types[i];
            const reqObj = { startDate, endDate, state, productCode: obj.code };
            const resObj = yield restful.renewalQueryAsync(reqObj);
            if (resObj.code == '000') {
                resObj.list.map(m => {
                    m.queryType = state;
                    list.push(m);
                })
            }
        }

        let deduct_status = {
            '0': '待扣款',
            '1': '成功',
            '2': '失败'
        }
        let deduct_list = []
        let uniqueSet = new Set();//可能有重复数据Set去重
        for (let i = 0; i < list.length; i++) {
            const rObj = list[i];
            let tmpObj = {};
            if (uniqueSet.has(rObj.contractNo)) continue
            tmpObj.contractNo = rObj.contractNo;
            tmpObj.paymentYear = parseInt(rObj.payCount);           // 缴费年度
            tmpObj.deductStatus = deduct_status[rObj.queryType]; // 扣款状态
            tmpObj.deductDate = rObj.enterAccDate || '';                // 扣款时间
            tmpObj.remark = rObj.transferResult || '';
            deduct_list.push(tmpObj);
            uniqueSet.add(rObj.contractNo);
        }
        console.log('hgrs_deduct_list:', JSON.stringify(deduct_list))
        if (deduct_list.length > 0) {
            yield deductImport(deduct_list, { name: 'system', fullName: '自动_华贵api获取' });
        }
        return deduct_list;
    }).catch(err => {
        logger.info('hgrs获取续期数据出错', err);
        return {};
    });
}

/**
 * 保司续期数据查询
 */
 exports.renewalQuery = (list) => {
    return co(function* () {

        let deduct_status = {
            '0': '待扣款',
            '1': '成功',
            '2': '失败',
            '5': '失效'
        }
        let deduct_list = []
        let uniqueSet = new Set();//可能有重复数据Set去重
        for (let i = 0; i < list.length; i++) {
            const rObj = list[i];
            let tmpObj = {};
            if (uniqueSet.has(rObj.contractNo)) continue
            tmpObj.contractNo = rObj.contractNo;
            tmpObj.paymentYear = parseInt(rObj.paymentYear);       // 缴费年度
            tmpObj.deductStatus = deduct_status[rObj.queryType];  // 扣款状态
            tmpObj.deductDate = rObj.realDate || '';             // 扣款时间
            tmpObj.remark = rObj.transferResult || '';
            deduct_list.push(tmpObj);
            uniqueSet.add(rObj.contractNo);
        }
        console.log('renewal_deduct_list:', JSON.stringify(deduct_list))
        if (deduct_list.length > 0) {
            yield deductImport(deduct_list, { name: 'system', fullName: '自动_保险公司sftp获取' });
        }
        return deduct_list;
    }).catch(err => {
        logger.info('获取续期数据出错', err);
        return {};
    });
}

/**
 * 生成续期佣金发放记录
 * @param {*} obj
 */
function* createRenewalCommission(elem) {
    elem.renewalId = elem._id;
    delete elem._id;
    delete elem.createdAt;
    delete elem.updatedAt;
    let agent = yield AgentBroker.findOne({ userCode: elem.serUserCode }).select('agentStatus saleman');
    if(agent.agentStatus != '2') {
        elem.remark = '业务员非已签约状态，佣金不予发放';
        elem.grantStatus = 'N';
    }
    // 利益发放给服务人员，如果有的话
    elem.userCode = elem.serUserCode || elem.userCode;
    elem.agentName = elem.serAgentName || elem.agentName;
    elem.agentId = agent._id;
    elem.saleman = agent.saleman;
    elem.productName = commonService.getProductName(elem);
    elem.cySubsidy = 0;
    // 利益结算
    // 用承保日期来确定归属日期，承保日期都是首年的数据，所以些处要做点处理
    const date = elem.renewalDate;// 费用发生日期
    elem.acceptDate = date;
    elem.reportDate = date;

    let commission = yield RenewalCommission.findOne({ contractNo: elem.contractNo, paymentYear: elem.paymentYear });
    if(!commission) {
        commission = new RenewalCommission(elem);
        yield commission.save();

        let remark = commission.remark || '';
        // 保存操作记录（清单id，操作内容，用户名称）
        commonService.addAperateLog({ _id: commission._id, action: '续期佣金', content: '生成续期佣金记录 ' + remark }, 'system');
    }
}
exports.createRenewalCommission = createRenewalCommission;

// 查询续期佣金记录
function* queryRenewalCommission(renewal) {
    let commission = yield RenewalCommission.findOne({ contractNo: renewal.contractNo, paymentYear: renewal.paymentYear});
    // 待发放状态
    if(commission.grantStatus == '0') {
        if(!renewal.unfreezeDate) {
            commission.unfreezeDate = moment(new Date()).add(1, 'day').startOf('day').add(12, 'hour').toDate();
        } else {
            commission.unfreezeDate = renewal.unfreezeDate;
        }
        commission._id = commission.renewalId;
        return commission;
    }
    return null;
}

// 更新续期佣金记录
function* updateRenewalCommission(elem, status, remark) {
    let condition = { contractNo: elem.contractNo, paymentYear: elem.paymentYear };
    let commission = yield RenewalCommission.findOne(condition);
    commission.remark = remark;
    commission.grantStatus = status;
    yield commission.save();

    // 同步更新续期表状态
    yield Renewal.update(condition, { $set: { grantStatus: status } });

    // 保存操作记录
    let content = remark ? '发放失败 ' + remark : '发放成功';
    commonService.addAperateLog({ _id: commission._id, action: '续期佣金', content }, 'system');
}

// 暂缓发放的进行补发
function* reissueCommission(obj) {
    for(let i = 0; i < obj.ids.length; i++) {
        let id = obj.ids[i];
        let commission = yield RenewalCommission.findById(id);
        if(!commission || commission.grantStatus != 'S') return Promise.reject('状态不为暂缓发放，不能进行补发，请核实');
        commission.unfreezeDate = moment(new Date()).add(1, 'day').startOf('day').add(12, 'hour').toDate();
        commission._id = commission.renewalId;
        const settleResult = yield service.calcLifeProfitAsync(commission, true);
        if (settleResult.isSuccess === 'F') {
            yield updateRenewalCommission(commission, 'N', settleResult.errorReason);
            return Promise.reject(settleResult.errorReason);
        }
        yield Renewal.update({ contractNo: commission.contractNo, paymentYear: commission.paymentYear}, {$unset: { grantStatus: 1 }});
        yield updateRenewalCommission(commission, '1', '');
    }
}
exports.reissueCommission = reissueCommission;

/**
 * 删除续期佣金发放记录
 * @param {*} uploadId
 * @param {*} renewal
 */
function* deleteRenewalCommission(renewal) {
    let commission = yield RenewalCommission.findOne({ contractNo: renewal.contractNo, paymentYear: renewal.paymentYear});
    if (!commission) return;
    if (commission.grantStatus != '1') {
        // 没有发放的情况下进行删除
        yield RenewalCommission.remove(commission);
    }
}
exports.deleteRenewalCommission = deleteRenewalCommission;
