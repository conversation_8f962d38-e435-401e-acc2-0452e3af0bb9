'use strict';
const _ = require('lodash');
const moment = require('moment');
const rp = require('request-promise');
const logger = require('@huibao/logger').logger();
const baseCode = require('../lib/baseCode');

const AgentBroker = require('../models/agent/AgentBroker');
const MiniProductInfo = require('../models/wechat/MiniProductInfo');
const MiniAgentProductFee = require('../models/wechat/MiniAgentProductFee');
const param = require('../lib/paramHelper').param;
const { getEnvConfig } = require('../lib/env-loader');

// 生成邀请二维码
async function generateInviteQrcode(userCode, promotionCode) {
    // 获取环境变量配置用于业务逻辑
    const envConfig = getEnvConfig();
    let agent = await AgentBroker.findOne({ userCode });

    if(!promotionCode) {
        // 生成推广码
        let randomStrUrl = `${envConfig.restUrl.apisRestUrl}/apis/sh/generate/string?key=JCjUhm7AsvzhS4Kq`;
        console.log('randomStrUrl', randomStrUrl);
        let { code, data } = await rp({ uri: randomStrUrl, json: true });
        if(code != '000') throw new Error(data.msg);

        promotionCode = data.randomStr;
    }

    let qrcode_params = {
        "app": param.baoelite,
        "page":"pages/home/<USER>",
        "scene_str": `1_${userCode}_${promotionCode}`,
        "mobile": agent.saleman,
        "userCode": userCode
    }
    let res = await rp({
        method: 'POST',
        uri: `${apisUrl}/apis/weixin/invite/qrcode`,
        body: qrcode_params,
        json: true
    });
    if(res.code != '000') throw new Error(res.msg);
    console.log('qr_code_url:', JSON.stringify(res));
    let qrcodeUrl = res.data.url;
    return { promotionCode, qrcodeUrl }
}

exports.generateInviteQrcode = generateInviteQrcode;

/**
 * C端产品分类
 */
async function getInsuranceType() {
    let types = await baseCode.getDict('c_product_type');
    let insuranceType = _.map(types, (v) => ({
        value: v.key,
        label: v.value
    }));
    return insuranceType;
}

exports.getInsuranceType = getInsuranceType;



/**
 * 查询产品费率配置
 * @param {*} productId
 * @param {*} userCode
 */
async function getProductFees(productId, userCode) {
    let productFee = await MiniAgentProductFee.findOne({ productId, userCode });
    if (!productFee) {
        const productInfo = await MiniProductInfo.findOne({ productId, productStatus: '1' });
        if (!productInfo) {
            throw new Error('Product not found');
        }
        productFee = {
            userCode,
            productId,
            limitCommission: productInfo.limitCommission,
            promoFeeRate: productInfo.promoFeeRate,
            firstFactor: productInfo.firstFactor,
            secondFactor: productInfo.secondFactor
        };
    }
    return productFee;
}
exports.getProductFees = getProductFees
