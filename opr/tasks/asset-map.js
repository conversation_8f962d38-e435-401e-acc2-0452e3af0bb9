'use strict';

module.exports = function(grunt) {
    // 生成资产映射文件
    grunt.registerTask('generateAssetMap', 'Generate asset mapping file', function() {
        var fs = require('fs');
        var path = require('path');
        
        var assetMap = {};
        var buildDir = '.build';
        
        // 递归扫描目录的函数
        function scanDirectory(dir, baseDir) {
            if (!fs.existsSync(dir)) return;
            
            var files = fs.readdirSync(dir);
            files.forEach(function(file) {
                var filePath = path.join(dir, file);
                var stat = fs.statSync(filePath);
                
                if (stat.isDirectory()) {
                    scanDirectory(filePath, baseDir);
                } else if (file.endsWith('.js') || file.endsWith('.css')) {
                    // 生成相对路径（去掉.build/前缀）
                    var relativePath = path.relative(baseDir, filePath);
                    
                    // 生成原始文件名映射
                    var originalName = file;
                    var originalPath = '';
                    
                    // 处理带hash的min文件 (例如: controllers.min.b39c9562.js -> controllers.min.js)  
                    if (file.match(/\.min\.[a-f0-9]{8}\.(js|css)$/)) {
                        originalName = file.replace(/\.min\.[a-f0-9]{8}\.(js|css)$/, '.min.$1');
                    }
                    // 处理带hash的文件 (例如: bootbox.846fc8bc.js -> bootbox.js)
                    else if (file.match(/\.[a-f0-9]{8}\.(js|css)$/)) {
                        originalName = file.replace(/\.[a-f0-9]{8}\.(js|css)$/, '.$1');
                    }
                    
                    // 只有当文件有hash时才加入映射
                    if (originalName !== file) {
                        // 生成原始文件的完整路径
                        var dirPath = path.dirname(relativePath);
                        originalPath = path.join(dirPath, originalName).replace(/\\/g, '/');
                        
                        // 添加完整路径映射和文件名映射
                        assetMap[originalPath] = relativePath;
                        assetMap[originalName] = relativePath;
                    }
                }
            });
        }
        
        // 扫描构建目录
        scanDirectory(buildDir, buildDir);
        
        // 写入映射文件
        var manifestPath = path.join(buildDir, 'assets.json');
        fs.writeFileSync(manifestPath, JSON.stringify(assetMap, null, 2));
        
        console.log('Generated asset map with ' + Object.keys(assetMap).length + ' entries at ' + manifestPath);
    });
};