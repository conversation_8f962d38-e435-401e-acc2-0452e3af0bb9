'use strict';

module.exports = function cssmin(grunt) {
    // Load task
    grunt.loadNpmTasks('grunt-contrib-cssmin');

    // Options
    return {
        options: {
            banner: '/*! <%= pkg.name %> <%= grunt.template.today("yyyy-mm-dd") %> */\n',
            keepSpecialComments: 0,
            report: 'min'
        },
        build: {
            files: [{
                expand: true,
                cwd: '.build/css',
                src: ['**/*.css', '!**/*.min.css'],
                dest: '.build/css/',
                ext: '.min.css'
            }, {
                expand: true,
                cwd: '.build/html',
                src: ['**/*.css', '!**/*.min.css'],
                dest: '.build/html/',
                ext: '.min.css'
            }, {
                expand: true,
                cwd: '.build/components',
                src: ['**/*.css', '!**/*.min.css'],
                dest: '.build/components/',
                ext: '.min.css'
            }, {
                expand: true,
                cwd: '.build/js',
                src: ['**/*.css', '!**/*.min.css'],
                dest: '.build/js/',
                ext: '.min.css'
            }]
        },
        combine: {
            files: {
                '.build/css/app.min.css': [
                    '.build/css/bootstrap.css',
                    '.build/css/style.css',
                    '.build/css/app.css'
                ]
            }
        }
    };
};
