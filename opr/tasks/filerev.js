'use strict';

module.exports = function filerev(grunt) {
    // Load task
    grunt.loadNpmTasks('grunt-filerev');

    // Options
    return {
        options: {
            algorithm: 'md5',
            length: 8
        },
        assets: {
            src: [
                '.build/js/**/*.min.js',
                '.build/css/**/*.min.css',
                '.build/html/**/*.min.js',
                '.build/html/**/*.min.css',
                '.build/components/**/*.min.js',
                '.build/components/**/*.min.css'
            ]
        }
    };
};
