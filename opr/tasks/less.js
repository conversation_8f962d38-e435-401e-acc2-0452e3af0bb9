'use strict';

module.exports = function less(grunt) {
    // Load task
    grunt.loadNpmTasks('grunt-contrib-less');

    // Options
    return {
        build: {
            options: {
                cleancss: false,
                compress: false,
                sourceMap: true
            },
            files: [
                // 编译主要的Less文件
                {
                    expand: true,
                    cwd: 'public/css',
                    src: ['app.less', 'design-system.less', 'components.less'],
                    dest: '.build/css/',
                    ext: '.css'
                },
                // 编译各模块的Less文件
                {
                    expand: true,
                    cwd: 'public/html',
                    src: ['**/css/*.less'],
                    dest: '.build/html/',
                    ext: '.css'
                }
            ]
        },
        // 开发环境构建 - 不压缩，便于调试
        dev: {
            options: {
                cleancss: false,
                compress: false,
                sourceMap: true
            },
            files: [
                {
                    expand: true,
                    cwd: 'public/css',
                    src: ['**/*.less'],
                    dest: '.build/css/',
                    ext: '.css'
                }
            ]
        }
    };
};
