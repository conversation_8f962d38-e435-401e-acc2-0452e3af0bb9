'use strict';

module.exports = function(grunt) {
    // 处理模板文件中的资源路径
    grunt.registerTask('processTemplateAssets', 'Process asset paths in template files', function() {
        var fs = require('fs');
        var path = require('path');
        
        // 读取资源映射文件
        var assetMapPath = '.build/assets.json';
        var assetMap = {};
        
        if (grunt.file.exists(assetMapPath)) {
            try {
                assetMap = grunt.file.readJSON(assetMapPath);
                grunt.log.writeln('Loaded asset map with ' + Object.keys(assetMap).length + ' entries');
            } catch (e) {
                grunt.log.warn('Failed to read asset map: ' + e.message);
                return;
            }
        } else {
            grunt.log.warn('Asset map not found at: ' + assetMapPath);
            return;
        }
        
        // 处理tmp目录中的所有dust文件
        var tmpDir = 'tmp';
        if (fs.existsSync(tmpDir)) {
            grunt.file.recurse(tmpDir, function(abspath, rootdir, subdir, filename) {
                if (filename.endsWith('.dust')) {
                    var content = grunt.file.read(abspath);
                    var originalContent = content;
                    
                    // 替换 {@asset path="..."} 标记
                    content = content.replace(/\{@asset\s+path="([^"]+)"\s*\/?\}/g, function(match, assetPath) {
                        // 移除开头的 / 如果存在
                        var cleanPath = assetPath.replace(/^\//, '');
                        
                        // 直接查找完整路径映射
                        var versionedPath = assetMap[cleanPath];
                        
                        if (versionedPath) {
                            // 确保路径以 / 开头
                            if (!versionedPath.startsWith('/')) {
                                versionedPath = '/' + versionedPath;
                            }
                            grunt.log.writeln('  Asset mapping: ' + assetPath + ' -> ' + versionedPath);
                            return versionedPath;
                        } else {
                            // 如果没有找到映射，保持原始helper引用
                            grunt.log.warn('  No mapping found for: ' + assetPath + ', keeping original helper');
                            return match;
                        }
                    });
                    
                    // 只有在内容改变时才写入文件
                    if (content !== originalContent) {
                        grunt.file.write(abspath, content);
                        grunt.log.writeln('Processed template: ' + subdir + '/' + filename);
                    }
                }
            });
        } else {
            grunt.log.warn('Template directory not found: ' + tmpDir);
        }
    });
};