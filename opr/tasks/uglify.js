'use strict';

module.exports = function uglify(grunt) {
    // Load task
    grunt.loadNpmTasks('grunt-contrib-uglify');

    // Options
    return {
        options: {
            banner: '/*! <%= pkg.name %> <%= grunt.template.today("yyyy-mm-dd") %> */\n',
            mangle: {
                reserved: ['jQuery', 'angular']
            },
            compress: {
                drop_console: true,
                drop_debugger: true
            }
        },
        build: {
            files: [
                {
                    expand: true,
                    cwd: 'public/js',
                    src: ['**/*.js', '!**/*.min.js', '!vendor/**/*.js', '!**/template-*.js', '!**/blueimp/**/*.js'],
                    dest: '.build/js/',
                    ext: '.min.js'
                },
                {
                    expand: true,
                    cwd: 'public/html',
                    src: ['**/*.js', '!**/*.min.js'],
                    dest: '.build/html/',
                    ext: '.min.js'
                },
                {
                    expand: true,
                    cwd: 'public/components',
                    src: ['**/*.js', '!**/*.min.js'],
                    dest: '.build/components/',
                    ext: '.min.js'
                }
            ]
        },
        vendor: {
            options: {
                mangle: false
            },
            files: [{
                expand: true,
                cwd: 'public/js/vendor',
                src: ['**/*.js', '!**/*.min.js'],
                dest: '.build/js/vendor/',
                ext: '.min.js'
            }]
        }
    };
};
