/**
 * 业务员离职
 */
const rp = require("request-promise");
const co = require("co");

function sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
}
function dismiss() {
    return co(function* () {
        const token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJfaWQiOiI2MTE2MDE1MWI5YjAxNzAxNzc3OTRkMDQiLCJ1c2VybmFtZSI6Imd1b3lhbnlhbiIsInNjb3BlIjpbIkJBU0lDX0xBV19BRE1JTiIsIkVYUE9SVF9QX1JPTEUiLCJFWFBPUlRfVV9ST0xFIiwiVVNFUl9JTkZPX1JPTEUiLCJDSE5MQ09OVFIiLCJQUk9EVUNUX1JPTEUiLCJST0xFX1VTRVIiLCJYWkJZWSIsIlhSWUdMIiwiUkdfT19ST0xFIiwiRlNYWVkiXSwiZXhwIjoxNjM4NzU1NTA5LCJpYXQiOjE2MzgxNTA3MDl9.ZA4UtxjUbj7u8PeUr5DBHLM87pEIDjNbFKhnWzXGi7M";
        const nums = [
            "5993d0f262a7507c0d95d471",
            "599540d0ea9a763c4a271c70",
            "5995442940fe40234aa379fe",
            "5996728d40fe40234aa38ffe",
            "59967c3fe622b6de28d0ad8b",
            "5996a7bfe622b6de28d0b23c",
            "5996a7da40fe40234aa39ac0",
            "5996ab73e622b6de28d0b25b",
            "5996b6bfe622b6de28d0b469",
            "5996c62040fe40234aa39ee6",
            "5996da0a40fe40234aa39f91",
            "5996f29ee622b6de28d0b9f1",
            "5996fc9ce622b6de28d0ba27",
            "59970a7140fe40234aa3a0f4",
            "5997737fe622b6de28d0bacb",
            "599775cf40fe40234aa3a571",
            "59977ad6e622b6de28d0bad0",
            "59977b5040fe40234aa3a577",
            "59977f6140fe40234aa3a58a",
            "59978d9d40fe40234aa3a5ad",
            "59978f09e622b6de28d0bb26",
            "599790d140fe40234aa3a5c9",
            "599796ac40fe40234aa3a5f0",
            "5997a332e622b6de28d0bb8c",
            "5997a789e622b6de28d0bba9",
            "5997b860e622b6de28d0bc29",
            "5997c7a240fe40234aa3a77f",
            "5997c7aae622b6de28d0bc8c",
            "5997c943e622b6de28d0bc90",
            "5997d10ae622b6de28d0bcb9",
            "5997d41540fe40234aa3a7b5",
            "5997d84c40fe40234aa3a7ce",
            "5997df55e622b6de28d0bd1d",
            "5997e97b40fe40234aa3a837",
            "5997f58540fe40234aa3a86a",
            "5997fff940fe40234aa3a8dc",
            "59982d8c40fe40234aa3aa4d",
            "59982f81e622b6de28d0bf5c",
            "5998302fe622b6de28d0bf61",
            "5998564be622b6de28d0c033",
            "599863c2e622b6de28d0c04b",
            "5998c7e4e622b6de28d0c0ca",
            "5998ff0ce622b6de28d0c1dc",
            "5999057d40fe40234aa3ad8d",
            "599908b9e622b6de28d0c216",
            "59990908e622b6de28d0c219",
            "59990beee622b6de28d0c21e",
            "5999223e40fe40234aa3ae07",
            "599936b2e622b6de28d0c2d1",
            "5999430640fe40234aa3ae7b",
            "5999485540fe40234aa3aeb9",
            "59994bf6e622b6de28d0c35d",
            "59997d5a40fe40234aa3b090",
            "5999992ce622b6de28d0c567",
            "599a162a40fe40234aa3b6b2",
            "599a3e00968997a86d9db6f2",
            "599a4bd2968997a86d9dbeff",
            "599a4c7fbc1e31c16d736884",
            "599a4fa8968997a86d9dbf17",
            "599a5049968997a86d9dbf23",
            "599a6789968997a86d9dc048",
            "599a75f3968997a86d9dc109",
            "599a77e0968997a86d9dc11c",
            "599a7b22bc1e31c16d736a68",
            "599a7cd6bc1e31c16d736a86",
            "599a82cbbc1e31c16d736b2e",
            "599a8355bc1e31c16d736b3f",
            "599a83d2968997a86d9dc298",
            "599a865dbc1e31c16d736b7a",
            "599a8a91bc1e31c16d736bb6",
            "599a8d84968997a86d9dc347",
            "599a9005968997a86d9dcf35",
            "599a906cbc1e31c16d738775",
            "599a9d71bc1e31c16d738fe6",
            "599a9e3e968997a86d9de069",
            "599aa7a9bc1e31c16d739366",
            "599ab3dcbc1e31c16d73943f",
            "599abdf7968997a86d9de627",
            "599acc83bc1e31c16d7394e8",
            "599acd0dbc1e31c16d7394eb",
            "599ad920bc1e31c16d73952d",
            "599adc60968997a86d9de6f6",
            "599ae67f968997a86d9de792",
            "599afb5a968997a86d9de836",
            "599afc70bc1e31c16d73969c",
            "599b4912bc1e31c16d7396dd",
            "599b5aebbc1e31c16d7396e0",
            "599b6e30bc1e31c16d739700",
            "599bce16bc1e31c16d73fda9",
            "599c19ba968997a86d9f191b",
            "599c32debc1e31c16d744b82",
            "599cbda8968997a86d9f2032",
            "599cc5fbbc1e31c16d744ce6",
            "599cf36cc66e1a4b3e9153d5",
            "599d1df57c0b4c224bc5fcca",
            "599d4d847c0b4c224bc60940",
            "599d9c0d07a7833b4b75326c",
            "599e8b094932d6970dbc5f49",
            "599ec03f314488bc76324a34",
            "599ec6c94c5204d5765936f2",
            "599f0f4e4c5204d576593ab7",
            "599ff8e14c5204d576595cf9",
            "59a01ddb4c5204d576596d46",
            "59a01e63314488bc76327781",
            "59a0eab7314488bc76327a48",
            "59a140ad314488bc76327c0b",
            "59a15b46314488bc76327c45",
            "59a22261314488bc76327e70",
            "59a37d154c5204d57659863e",
            "59a3ab5d4c5204d576598a76",
            "59a3e021314488bc7632ca66",
            "59a3edc5314488bc7632cb65",
            "59a408174c5204d57659bd21",
            "59a4f96c19cf0a627f5d1cd9",
            "59a5305419cf0a627f5d2138",
            "59a551ecaddcc23c6bfe59a5",
            "59a68eb505edfcf205ad48d4",
            "59a69a6276d9950b06c56380",
            "59a78cca05edfcf205ad586d",
            "59a78cf105edfcf205ad5870",
            "59a791d776d9950b06c56d4b",
            "59a7ba4676d9950b06c58adf",
            "59a7ccf176d9950b06c5aa68",
            "59a7d64c76d9950b06c5ab7b",
            "59a814780a8251d44d91e2b6",
            "59a899990a8251d44d91e488",
            "59a9469ca837b2823ee27869",
            "59a9f3624a1ca8af3eead0af",
            "59aa32754a1ca8af3eead2f0",
            "59aa35b4a837b2823ee27d0e",
            "59aaa1e1a837b2823ee28028",
            "59aacc9c4a1ca8af3eead66f",
            "59ab3ad14a1ca8af3eead713",
            "59ab516aa837b2823ee28176",
            "59abbe4aa837b2823ee28413",
            "59acb55405a589861c6db1db",
            "59acfa8805a589861c6dba59",
            "59acffe4c9c5a55f7c39919a",
            "59ad3a05af8a090506f2f46c",
            "59ad3b25c9c5a55f7c39a679",
            "59ad6020fed6032374450209",
            "59ae787ddddb766f0837352e",
            "59ae905e08ee28e159c47fc0",
            "59aeb1146fe244c50a8c987f",
            "59af2b586fe244c50a8d81f4",
            "59af48326fe244c50a8d8229",
            "59afaefd6fe244c50a8d86fa",
            "59b3a35fa4d9c7d35877bb8d",
            "59b3d72fa4d9c7d35877bd0a",
            "59b61b30bb36f9d958196623",
            "59b6670d451098b978db76ee",
            "59b8f7f9f297fef7331ee49e",
            "59ba50b74efe8efd3315d7f4",
            "59ba580b4efe8efd3315d87a",
            "59ba98924efe8efd3315daf0",
            "59bb1cf6f297fef7331f136d",
            "59bbbc31f297fef7331f2271",
            "59bbc49cf297fef7331f2298",
            "59bc8b2dabef8e9b5f317676",
            "59bdf5c5f297fef7331f341b",
            "59be261bf297fef7331f35a1",
            "59c111689b5e8481015e7d36",
            "59c21030d6eeef7b013f6e39",
            "59c2105f9b5e8481015e89f5",
            "59c8aa494b0f262c1dcfcc86",
            "59c8ac53be3e70fe5e10c436",
            "59d396746009d6de03f339ad",
            "59e6c3091bf8e1ea6427b67e",
            "59e6d303036d92734ebdc5e2",
            "59e7072415f2facc4986f052",
            "59ee8da2be1169c5294840e4",
            "59f439adfd165c9831d065cf",
            "5b0ba737f5984f1a30d84718",
            "5bd1b9a9ec6a1eb57adff4de"
        ];

        for (let i = 0; i < nums.length; i++) {
            yield sleep(1000);
            const repos = yield rp({
                uri: "https://opr.hizom.cn/agent/agent/update?token=" + token,
                method: "POST",
                body: {
                    _id: nums[i],
                    type: "dismiss",
                    agentStatus: "3",
                    leaveDate: "2021-11-30T08:37:14.400Z",
                    noRecursion: false,
                    serUserCode: "337142",
                    serAgentName: "涂相凤",
                },
                json: true,
            });
            console.log(nums[i], repos);
        }
        return nums;
    }).catch((err) => {
        console.log(err);
    });
}
// dismiss();
