const rp = require('request-promise');
const co = require('co');
const moment = require('moment');
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
co(function* () {
    let nums = "2025-02-18";
    for (let i = 0; i < 1; i++) {
        yield sleep(1000);
        let date = moment(nums).add(i, 'd').format('YYYY-MM-DD');
        const repos = yield rp({
            uri: 'https://opr.hizom.cn/api/manual/cronJob',
            method: 'POST',
            body: {
                "data": {
                    "name": "DCRS_getPolicyRenewal",
                    "file": "../../../lib/cront/provider/dingchenglife",
                    "params": date
                }
            },
            json: true,
        });
        console.log(date, repos);
    }
    return nums;
}).catch(err => {
    console.log(err);
});
