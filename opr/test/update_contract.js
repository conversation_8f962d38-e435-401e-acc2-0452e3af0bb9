const rp = require('request-promise');
const co = require('co');

function update() {
    return co(function* () {
        const token = '';
        const nums = [
            "016366244717158"
        ]
        for (let i = 0; i < nums.length; i++) {
            const repos = yield rp({
                uri: 'https://opr.hizom.cn/biz/contract/life/update',
                method: 'POST',
                body: { contractNo: nums[i], token },
                json: true,
            });
            console.log(nums[i], repos);
        }
        return nums;
    }).catch(err => {
        console.log(err);
        return global.map.get('defaultUrl');
    });
}
update();
