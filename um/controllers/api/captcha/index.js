'use strict';

const co = require('co');
const cors = require('cors');
const _ = require('lodash');
const moment = require('moment');
const Promise = require('bluebird');
const svgCaptcha = require('svg-captcha');
const { createCanvas, loadImage } = require('canvas');

const auth = require('../../../lib/auth');
const uid = require('../../../lib/uid');
const logger = require('../../../lib/logger');
const User = require('../../../models/system/User');
const SmsCodeHis = require('../../../models/SmsCodeHis');
const SmsVCode = require('../../../models/SmsVCode');

const captchaService = require('../../../services/captchaService');
const responseJSON = require('../../../lib/middlewear/responseJSON');
const cacheHelper = Promise.promisifyAll(require('../../../lib/cacheHelper'));
const restClient = Promise.promisifyAll(require('../../../lib/restClient/restClient'));

module.exports = function (app) {

    var corsOptions = require('../../../lib/cors-options');
    app.options('*', cors(corsOptions));

    /**
     * 获取短信验证码
     * params:
     * type 1-验证码登录 2-密码登录 3-忘记密码 4-注册 5-修改密码 6-产品购买
     */
    app.get('/v2', cors(corsOptions), function (req, res, next) {
        const obj = req.query;
        logger.info('req_captcha_v2:' + JSON.stringify(obj));
        const mobile = obj.mobile;
        let verifycode = obj.verifycode;
        const type = obj.type;
        const vflag = obj.vflag;
        if (verifycode) verifycode = verifycode.toLowerCase();

        // https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Access_control_CORS
        res.setHeader('Access-Control-Allow-Credentials', true);
        const data = { codeFlag: 'false', code: '000' };
        co(function* () {
            if (!mobile) return Promise.reject('手机号码不能为空');
            if (!type) return Promise.reject('操作类型不能为空');

            // 频率限制：60秒内同一手机号不能重复发送
            let ckey = `sms_limit_${mobile}`;
            const limit = yield cacheHelper.co_getEX(ckey);
            if (limit) return Promise.reject('验证码有效, 60秒内请勿重复发送!');

            // 每日发送次数限制
            let dayKey = `sms_day_${mobile}_${moment().format('YYYYMMDD')}`;
            let dayCount = yield cacheHelper.co_getEX(dayKey);
            dayCount = Number(dayCount) || 0;
            if (dayCount >= 5) return Promise.reject('当天短信验证码使用次数已达上限!');

            // 非注册类型和投保人校验，判断用户是否是系统中用户
            if (type != '4' && type != '6') {
                const user = yield User.findOne({ name: mobile, status: '1' });
                if (!user) return Promise.reject('用户未注册，无法发送验证码！');
            }

            let vfyCode = obj.vfy || 'lastcode';
            data.codeFlag = 'true';
            if (type == '1' && !verifycode) {
                data.code = '001';
                data.msg = '图片验证码不能为空!';
                res.locals.data = data;
                return next();
            }

            // req.session取不到值，redis中再取一次
            let lastcode = yield cacheHelper.co_get('VerifyCode', vfyCode);
            console.log('lastcode:', lastcode, req.session.captchaPng);
            lastcode = req.session.captchaPng || lastcode;
            if (vflag != 'product' && (!lastcode || (verifycode != lastcode))) {
                logger.info(`verifycode:${verifycode}||captchaPng:${lastcode}`);
                data.code = '002';
                data.msg = '图片验证码不正确!';
                res.locals.data = data;
                return next();
            }
            // 验证通过了删除session的值
            req.session.captchaPng = null;

            // create captcha
            const captcha = yield captchaService.create({ type, mobile });

            const sms = new SmsCodeHis();
            sms.mobile = mobile;
            sms.checkCode = captcha;
            sms.type = type;
            sms.smsDate = moment().format('YYYY-MM-DD');
            yield sms.save();

            // captcha 最终通过短信发送
            const result = yield restClient.sendMsgAsync({
                mobile,
                content: '您好，您的验证码是' + captcha,
            });
            if (result.code === '999') return Promise.reject(result.msg);
            // 发送成功，设置60秒限制
            yield cacheHelper.co_putEX(ckey, captcha, 60);
            // 发送成功，增加每日计数
            yield cacheHelper.co_putEX(dayKey, dayCount + 1, 86400);

            res.locals.data = data;
            next();
        }).catch(err => {
            logger.info('captcha err:', err.stack || err);
            res.locals.err = err;
            next();
        });
    }, responseJSON());

    /**
     * 获取短信验证码 不使用图形验证码
     * params:
     * type 1-验证码登录 2-密码登录 3-忘记密码 4-注册 5-修改密码 6-产品购买
     */
    app.get('/v3', cors(corsOptions), function (req, res, next) {
        const obj = req.query;
        logger.info('req_captcha_v3:' + JSON.stringify(obj));
        const mobile = obj.mobile;
        const type = obj.type;

        // https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Access_control_CORS
        res.setHeader('Access-Control-Allow-Credentials', true);
        const data = { codeFlag: 'false', code: '000' };
        co(function* () {
            if (!mobile) return Promise.reject('手机号码不能为空');
            if (!type) return Promise.reject('操作类型不能为空');

            // 非注册类型和投保人校验，判断用户是否是系统中用户
            if (type != '4' && type != '6') {
                const user = yield User.findOne({ name: mobile, status: '1' });
                if (!user) return Promise.reject('用户未注册，无法发送验证码！');
            }

            // 验证码有效, 60秒内请勿重复发送
            let ckey = `login_${mobile}`;
            const config = yield cacheHelper.co_getEX(ckey);
            if (config) return Promise.reject('验证码有效, 60秒内请勿重复发送!' );

            if (type == '1' || type == '2') {
                // 查询当天发送验证码的次数
                const count = yield SmsCodeHis.count({ type, mobile, smsDate: moment().format('YYYY-MM-DD') });
                if (count >= 5) {
                    return Promise.reject('当天短信验证码使用次数已达上限!');
                }
            }

            // create captcha
            const captcha = yield captchaService.create({ type, mobile });

            const sms = new SmsCodeHis();
            sms.mobile = mobile;
            sms.checkCode = captcha;
            sms.type = type;
            sms.smsDate = moment().format('YYYY-MM-DD');
            yield sms.save();

            // captcha 最终通过短信发送
            const result = yield restClient.sendMsgAsync({
                mobile,
                content: '您好，您的验证码是' + captcha,
            });
            if (result.code === '999') return Promise.reject(result.msg);
            // 存一下 60秒过期
            yield cacheHelper.co_putEX(ckey, captcha, 60);

            res.locals.data = data;
            next();
        }).catch(err => {
            logger.info('captcha err:', err.stack || err);
            res.locals.err = err;
            next();
        });
    }, responseJSON());

    // 生成验证码图片(运营系统)
    app.get('/captcha', cors(corsOptions), function (req, res, next) {
        co(function* () {
            const obj = req.query;
            const option = {
                size: 4,
                ignoreChars: '01OoIil',
                noise: 2,
                background: '#fff',
            };
            // 只生成数字的
            if (obj.preset) option.charPreset = '0123456789';

            if (obj.width) option.width = Number(obj.width);
            if (obj.height) option.height = Number(obj.height);
            const captcha = svgCaptcha.create(option);
            let code = (captcha.text + '').toLowerCase();// 统一转小写
            req.session.captchaPng = code;
            console.log('captcha_text:', JSON.stringify(obj), code);
            let vfyCode = obj.vfy || 'lastcode';
            cacheHelper.put('VerifyCode', vfyCode, code, function () { });
            res.type('svg');
            res.send(captcha.data);
        }).catch(err => {
            console.log('captcha==err: ', err.message, err.stack);
            res.json({ code: '999', message: err.message });
        });
    });
    
    /**
     * https://github.com/lemonce/svg-captcha
     * 生成验证码图片
     */
    app.get('/v2/captcha.svg', cors(corsOptions), function (req, res, next) {

        const options = {
            size: 4,                       // 验证码长度
            // ignoreChars: '0o1i',      // 验证码字符中排除 0o1i
            noise: 2,                     // 干扰线条的数量
            charPreset: '0123456789',
            // color: true,                  // 验证码的字符是否有颜色，默认没有，如果设定了背景，则默认有
            // background: '#cc9966' // 验证码图片背景颜色
        };
        const captcha = svgCaptcha.create(options);
        req.session.captchaPng = captcha.text.toLowerCase();
        console.log('v2_captcha:'+req.session.captchaPng);
        res.type('svg');
        res.status(200).send(captcha.data);
    });

    app.get('/test', cors(corsOptions), function (req, res, next) {
        const obj = req.query;
        logger.info('test_captcha:' + JSON.stringify(obj));
        const mobile = obj.mobile;
        const type = obj.type;

        co(function* () {
            if (!mobile) return Promise.reject('手机号码不能为空');
            if (!type) return Promise.reject('操作类型不能为空');

            let captcha = '';
            // create captcha
            if (global.env != 'production') {
                captcha = yield captchaService.create({ mobile, type });
            }
            res.locals.data = { code: captcha };
            next();
        }).catch(err => {
            logger.info('captcha err:', err.stack || err);
            res.locals.err = err;
            next();
        });
    }, responseJSON());

    app.get('/:mobile/:captcha/check', cors(corsOptions), function (req, res) {
        let params = req.params;
        let obj = req.query;
        console.log('captcha_check_req:', params, obj)
        co(function* () {
            let check = yield captchaService.validate({
                mobile: params.mobile,
                captcha: params.captcha,
                type: obj.type,
            })
            console.log('captcha_check_res:', check)
            res.json({ result: 'ok' });
        }).catch(err => {
            console.log('check__', err)
            res.json({ err: '验证码错误' });
        })
    });

    app.get('/:mobile/:captcha/check/v2', cors(corsOptions), function (req, res) {
        let params = req.params;
        let obj = req.query;
        console.log('captcha_check_req:', params, obj)
        co(function* () {
            let check = yield captchaService.validate({
                mobile: params.mobile,
                captcha: params.captcha,
                type: obj.type,
            })
            console.log('captcha_check_res:', check);
            res.json({ code: '000', data: { result: 'ok' } });
        }).catch(err => {
            console.log('check__', err)
            res.json({ code:'999', msg: '验证码错误' });
        })
    });

    app.get('/checkCaptcha', cors(corsOptions), function (req, res) {
        co(function* () {
            let obj = req.query;
            console.log('captch_validate:', obj);
            let code = yield SmsVCode.findOne({
                mobile: obj.mobile,
                checkCode: obj.captcha,
                type: obj.type
            });

            if (!code) {
                throw 'cannot find captcha';
            }

            res.json({ code: '000', data: { successful: true }, msg: 'ok!' });
        }).catch(err => {
            console.log(err);
            res.json({ code: '000', data: { successful: false }, msg: '验证码错误' });
        });
    });

    /**
     * 获取滑动验证码
     *
     * @width {number} 背景图宽度
     * @height {number} 背景图高度
     * @size {number} 拼图碎片大小
     */
    app.get('/slide', cors(corsOptions), function (req, res) {
        co(function* () {
            let { width = 200, height = 150, size = 50, vfy } = req.query;
            width = Number(width);
            height = Number(height);
            // 拼图碎片凸起大小
            const humpSize = 5;
            // 两侧拼图碎片凸起大小
            const bothHumpSize = humpSize * 2;
            const fragmentSize = Number(size) + humpSize;
            size = Number(size) - bothHumpSize;

            const canvasImg = createCanvas(width, height);
            const canvasFragment = createCanvas(fragmentSize, fragmentSize);
            const ctxImg = canvasImg.getContext('2d');
            const ctxFragment = canvasFragment.getContext('2d');

            const minRate = 0.10;
            const maxRate = 0.95;
            const minWX = width * minRate + size;
            const maxWX = width * maxRate - size;

            const minHX = height * minRate;
            const maxHX = height * maxRate - size;

            const offsetX = _.clamp(Math.random() * maxWX, minWX, maxWX);
            let offsetY = _.clamp(Math.random() * maxHX, minHX, maxHX);

            const styleIndex = Math.floor(Math.random() * 16);
            const ossUrl = 'https://oss.99bx.cn/images/captcha/';
            const images = [ 'fantasy.jpeg', 'mountains.jpeg', 'tree.jpeg', 'seashells.jpeg', 'river.jpeg', 'plouzane.jpeg' ];
            const imageName = images[Math.floor(Math.random() * images.length)];
            const image = yield loadImage(ossUrl + imageName);

            ctxImg.drawImage(image, 0, 0, width, height);
            createClipPath(ctxImg, offsetX, offsetY, size, styleIndex);
            ctxImg.fillStyle = 'rgba(1, 1, 1, 0.5)';
            ctxImg.fill();
            // 添加边框
            ctxImg.lineWidth = 3;
            ctxImg.strokeStyle = '#ccc';
            ctxImg.stroke();

            createClipPath(ctxFragment, humpSize, humpSize, fragmentSize - bothHumpSize, styleIndex);
            ctxFragment.drawImage(image, -offsetX, -offsetY, width, height);

            // 添加边框
            ctxFragment.lineWidth = 3;
            ctxFragment.strokeStyle = 'white';
            ctxFragment.stroke();

            const backdrop = canvasImg.toDataURL();
            const fragment = canvasFragment.toDataURL('image/png');

            // 最终偏移应减去凸起大小
            req.session.offsetX = offsetX - humpSize;
            if (vfy) {
                cacheHelper.put('VerifyCode', vfy, req.session.offsetX, function () { });
            }
            console.log('offsetX', req.session.offsetX);
            offsetY = offsetY - humpSize;
            res.json({
                code: '000',
                msg: 'ok!',
                data: { backdrop, fragment, offsetY },
            });
        }).catch(err => {
            logger.info('get slide captcha:', err.stack || err);
            res.json({ code: '999', msg: '验证码异常' });
        });
    });

    // 验证滑动验证码
    app.post('/slide', cors(corsOptions), function (req, res) {
        co(function* () {
            const { offsetX, vfy } = req.body;
            let { offsetX: x } = req.session;

            if (!x && vfy) {
                x = yield cacheHelper.co_get('VerifyCode', vfy);
            }
            const deltaX = 10;
            const thresholdL = x - deltaX;
            const thresholdR = x + deltaX;
            console.log('offsetX: ', x, thresholdL, thresholdR, offsetX);
            if (offsetX >= thresholdL && offsetX <= thresholdR) {
                return res.json({
                    code: '000',
                    data: { msg: '验证成功' },
                });
            }

            throw Error('验证失败');
        }).catch(err => {
            logger.info('post slide captcha:', err);
            res.json({ code: '999', msg: '验证失败' });
        });
    });

    // 验证滑动验证码
    app.post('/v5', cors(corsOptions), function (req, res) {
        co(function* () {
            let { offsetX, mobile, type, vfy } = req.body;
            let { offsetX: x } = req.session;
            if (!x && vfy) {
                x = yield cacheHelper.co_get('VerifyCode', vfy);
            }
            type =  type || '1'; // 默认type
            let successful = false;
            let msg = '验证失败！'
            if(!mobile || mobile.length != 11) return Promise.reject('参数异常!');

            const thresholdL = x - 10;
            const thresholdR = x + 10;
            console.log('offsetX: ', thresholdL, thresholdR, offsetX);
            if (offsetX >= thresholdL && offsetX <= thresholdR) {
                successful = true;
                msg = '验证成功！';
            }

            //清除数据
            req.session.offsetX = null

            if(successful) {
                 // 验证码有效, 60秒内请勿重复发送
                let ckey = `login_${mobile}`;
                const config = yield cacheHelper.co_getEX(ckey);
                if (config) return Promise.reject('验证码有效, 60秒内请勿重复发送!' );

                // create captcha
                const captcha = yield captchaService.create({ type, mobile });

                const sms = new SmsCodeHis();
                sms.mobile = mobile;
                sms.checkCode = captcha;
                sms.type = type;
                sms.smsDate = moment().format('YYYY-MM-DD');
                yield sms.save();

                // captcha 最终通过短信发送
                const result = yield restClient.sendMsgAsync({
                    mobile,
                    content: '您好，您的验证码是' + captcha,
                });
                if (result.code === '999') return Promise.reject(result.msg);
                // 存一下 60秒过期
                yield cacheHelper.co_putEX(ckey, captcha, 60);
            }

            res.json({
                msg,
                code: '000',
                data: { successful },
            });
        }).catch(err => {
            logger.info('post v5_captcha:', err.stack || err);
            res.json({ code: '999', msg: err || '验证码异常！' });
        });
    });

};

// 生成裁剪路径
function createClipPath(ctx, offsetX, offsetY, size = 100, styleIndex = 0, humpSize = 5) {
    const styles = [
        [0, 0, 0, 0],
        [0, 0, 0, 1],
        [0, 0, 1, 0],
        [0, 0, 1, 1],
        [0, 1, 0, 0],
        [0, 1, 0, 1],
        [0, 1, 1, 0],
        [0, 1, 1, 1],
        [1, 0, 0, 0],
        [1, 0, 0, 1],
        [1, 0, 1, 0],
        [1, 0, 1, 1],
        [1, 1, 0, 0],
        [1, 1, 0, 1],
        [1, 1, 1, 0],
        [1, 1, 1, 1],
    ];
    const style = styles[styleIndex];

    // 半径
    const r = 0.1 * size;
    const halfWidth = 0.5 * size;
    ctx.save();
    ctx.beginPath();
    // left
    ctx.moveTo(offsetX, offsetY);
    ctx.lineTo(offsetX, offsetY + halfWidth - humpSize);
    ctx.arc(
        offsetX,
        offsetY + halfWidth,
        r,
        1.5 * Math.PI,
        0.5 * Math.PI,
        style[0]
    );
    ctx.lineTo(offsetX, offsetY + size);
    // bottom
    ctx.lineTo(offsetX + halfWidth - humpSize, offsetY + size);
    ctx.arc(offsetX + halfWidth, offsetY + size, r, Math.PI, 0, style[1]);
    ctx.lineTo(offsetX + size, offsetY + size);
    // right
    ctx.lineTo(offsetX + size, offsetY + halfWidth + humpSize);
    ctx.arc(
        offsetX + size,
        offsetY + halfWidth,
        r,
        0.5 * Math.PI,
        1.5 * Math.PI,
        style[2]
    );
    ctx.lineTo(offsetX + size, offsetY);
    // top
    ctx.lineTo(offsetX + halfWidth + humpSize, offsetY);
    ctx.arc(offsetX + halfWidth, offsetY, r, 0, Math.PI, style[3]);
    ctx.lineTo(offsetX, offsetY);
    ctx.clip();
    ctx.closePath();
}
