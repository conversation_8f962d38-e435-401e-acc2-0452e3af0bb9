const co = require('co');
const cors = require('cors');
const moment = require('moment');
const User = require('../../models/system/User');
const UserInfo = require('../../models/UserInfo');
const auth = require('../../lib/auth');
const logger = require('../../lib/logger');


module.exports = function(app) {

    app.options('*', cors());

    /*
     *新增用户接口
     */
     app.post('/addUser', cors(), auth.isAuthenticatedByToken(), function (req, res, next) {
        var reqObj = req.body;
        logger.info("addUser_Req", reqObj);

        var branch = reqObj.branchCode;
        logger.info(branch);

        var name = reqObj.saleman; //登录名
        var codepwd = '';
        if (reqObj.password) codepwd = reqObj.password;

        var fullName = reqObj.agentName; //全名
        var roles = ['ROLE_USER']; //角色
        var userType = '1'; //用户类型
        var oprBranches = branch; //操作机构
        var status = '1'; //状态
        var agentId = reqObj._id;
        var email = reqObj.email;

        var userObj = new User();
        userObj.source = 'opr';
        userObj.name = name;
        userObj.password = codepwd;
        userObj.fullName = fullName;
        userObj.roles = roles;
        userObj.userType = userType;
        userObj.oprBranches = oprBranches;
        userObj.status = status;
        userObj.agentId = agentId;
        userObj.userCode = reqObj.userCode;
        userObj.branch = branch;
        userObj.referee = reqObj.recommMobile;


        co(function* () {

            let user = yield User.findOne({ name: name })
            if (user) return Promise.reject("此用户已经存在,不能重复添加!");
            yield userObj.save();

            var userInfoObj = new UserInfo();
            userInfoObj.name = name;
            userInfoObj.user = userObj._id;
            userInfoObj.email = email;
            userInfoObj.mobile = name;
            userInfoObj.userType = '1';
            logger.info(userInfoObj);
            yield userInfoObj.save();
            // 关联id
            userObj.userInfo = userInfoObj._id;
            yield userObj.save();

            res.json({ rec: '开通帐号成功!', resultCode: '0', user: userObj });
        }).catch(err => {
            logger.error(err);
            res.json({ rec: err })
        })
    });
};
