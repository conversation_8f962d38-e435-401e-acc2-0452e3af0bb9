'use strict';
const co = require('co');
const cors = require('cors');
const async = require('async');
const moment = require('moment');
const Promise = require('bluebird');

const Client = require('../../../models/oauth/Client');
const WxUser = require('../../../models/WxUser');
const User = require('../../../models/system/User');

const logger = require('../../../lib/logger');
const auditHelper = require('../../../lib/auditHelper');
const responseJSON = require('../../../lib/middlewear/responseJSON');

const loginService = require('../../../services/loginService');
const captchaService = require('../../../services/captchaService');
const commonService = require('../../../services/commonService');
const tokenHelper = Promise.promisifyAll(require('../../../lib/tokenHelper'));
const cacheHelper = Promise.promisifyAll(require('../../../lib/cacheHelper'));

const WechatHandle = require('./WechatHandle');
const CryptoJS = require('crypto-js');

const LOCK_TIME = 15 * 60; // 锁定15分钟
const MAX_FAIL_ATTEMPTS = 3; // 最大失败次数

module.exports = function (app) {
    var corsOptions = require('../../../lib/cors-options');
    app.options('*', cors(corsOptions));

    /**
     * PC端登录接口（整合手机号+短信验证码、用户名+密码两种方式）
     */
    app.post(
        '/',
        cors(corsOptions),
        async (req, res, next) => {
            const obj = req.body;
            console.log('pc_login_req:' + JSON.stringify(obj));
            const type = obj.type; // 登录类型 1-手机号&短信 2-用户名&密码
            const userType = obj.userType || '3'; // 用户类型
            const username = obj.username; // 用户名
            let password = obj.password; // 密码
            let verifycode = obj.verifycode; // 图形验证码
            if (verifycode) verifycode = verifycode.toLowerCase();

            if (password && global.auth_key) {
                //密码解密
                try {
                    let bytes = CryptoJS.AES.decrypt(password, global.auth_key);
                    password = bytes.toString(CryptoJS.enc.Utf8);
                } catch (err) {
                    logger.error('密码解密失败:', err);
                    throw '密码格式错误';
                }
            }

            const mobile = obj.mobile; // 手机号
            const captcha = obj.captcha; // 短信验证码

            try {
                // 1. 查找用户
                let condition = { status: '1' };
                if (type == '1') {
                    condition.name = mobile;
                } else {
                    condition.name = username;
                }
                condition.userType = userType;
                console.log('log_condition:', JSON.stringify(condition));
                const user = await User.findOne(condition).populate('userInfo');
                if (!user) throw '用户名或密码错误！';

                // 2. 登录方式判断
                if (type == '1') {
                    // 手机号+短信验证码
                    if (!captcha) throw '请输入短信验证码';
                    // 校验短信验证码
                    await captchaService.validate({ mobile, captcha });
                } else if (type == '2') {
                    // 用户名+密码
                    // 登录失败次数校验
                    const failKey = `pc_login:fail:count:${username}`;
                    const failCount = (await cacheHelper.co_getEX(failKey)) || 0;
                    console.log('failKey:', failKey, failCount);

                    // 超过最大失败次数直接锁定
                    if (failCount >= MAX_FAIL_ATTEMPTS) {
                        throw `账号已锁定，请${LOCK_TIME / 60}分钟后再试`;
                    }

                    const captcha_1 = req.session.captchaPng
                        ? req.session.captchaPng.toLowerCase()
                        : '';

                    if (!captcha_1 || verifycode != captcha_1) {
                        logger.info(`verifycode:${verifycode}||captchaPng:${captcha_1}`);
                        throw '图片验证码不正确!';
                    }
                    // 验证通过了删除session的值
                    req.session.captchaPng = null;

                    if (!user.password || !user.validPassword(password)) {
                        // 记录失败次数
                        const newFailCount = parseInt(failCount) + 1;
                        await cacheHelper.co_putEX(failKey, newFailCount, LOCK_TIME);
                        // 保存密码错误的历史
                        await loginService.saveLoginHis(username, '2', {}, 'pc');
                        // 如果达到锁定阈值，直接提示锁定
                        if (newFailCount >= MAX_FAIL_ATTEMPTS) {
                            throw `账号已锁定，请${LOCK_TIME / 60}分钟后再试`;
                        } else {
                            throw '用户名或密码错误！';
                        }
                    }
                    // 登录成功清除失败记录
                    await cacheHelper.clearEX(failKey, () => {});
                } else {
                    throw '登录参数异常!';
                }

                // 登录成功历史记录
                await loginService.saveLoginHis(username || mobile, '1', {}, 'pc');

                // 生成token
                const token = await tokenHelper.saveAsync(user, {});

                // 返回用户信息
                res.locals.data = {
                    token,
                    user: user,
                };
                return next();
            } catch (err) {
                logger.error('pc_login_err:', err && err.stack ? err.stack : err);
                res.locals.err = err && err.message ? err.message : err;
                return next();
            }
        },
        responseJSON(),
    );

    /**
     * 新版本登录接口
     * author:libinbin
     * date:2017-06-05
     */
    app.post(
        '/v2',
        cors(corsOptions),
        function (req, res, next) {
            const obj = req.body;
            console.log('v2_login_req:' + JSON.stringify(obj));
            const mobile = obj.mobile;
            const captcha = obj.captcha;

            let password = obj.password;
            let verifycode = obj.verifycode;
            const userType = obj.userType || '1';
            const type = obj.type; // 登录类型 1-验证码 2-密码 3-忘记密码
            const audit = obj.audit;
            if (verifycode) verifycode = verifycode.toLowerCase();

            co(function* () {
                const data = { code: '000' };
                let condition = {
                    name: mobile,
                };
                condition.userType = userType;

                const user = yield User.findOne(condition).populate('userInfo');
                if (!user) return Promise.reject('用户尚未注册！');
                if (user.status != '1') return Promise.reject('用户为已注销状态，请联系运营处理！');
                if (password && !user.password)
                    return Promise.reject('用户未设置密码,请使用手机验证码方式登录！');

                if (type == '1') {
                    // 验证码登录
                    // validate captcha
                    yield captchaService.validate({ mobile, captcha, type }).catch((err) => {
                        logger.error('captcha.validate:', err.message || err);
                        return Promise.reject('验证码错误!');
                    });
                } else if (type == '2') {
                    // 密码登录
                    data.codeFlag = 'false';

                    // 使用Redis检查登录失败次数
                    const failKey = `login:fail:count:${mobile}`;
                    const failCount = (yield cacheHelper.co_getEX(failKey)) || 0;
                    console.log('failKey:', failKey, failCount);

                    if (failCount >= MAX_FAIL_ATTEMPTS && userType != '5') {
                        data.codeFlag = 'true';
                        if (!verifycode) {
                            data.code = '001';
                            res.locals.data = data;
                            return next();
                        }
                        if (!req.session.captchaPng || verifycode != req.session.captchaPng) {
                            logger.info(
                                `verifycode:${verifycode}||captchaPng:${req.session.captchaPng}`,
                            );
                            data.code = '002';
                            data.msg = '图片验证码不正确!';
                            res.locals.data = data;
                            return next();
                        }
                        // 验证通过了删除session的值
                        req.session.captchaPng = null;
                    }
                    if (!user.password || !user.validPassword(password)) {
                        // 使用Redis记录失败次数
                        const newFailCount = parseInt(failCount) + 1;
                        yield cacheHelper.co_putEX(failKey, newFailCount, LOCK_TIME);
                        // 保存密码错误的次数
                        yield loginService.saveLoginHis(mobile, '2', audit, '');
                        return Promise.reject('密码错误!');
                    }
                    // 登录成功，清除失败记录
                    cacheHelper.clearEX(failKey);
                } else if (type == '3') {
                    data.codeFlag = 'true';

                    yield captchaService.validate({ mobile, captcha, type }).catch((err) => {
                        logger.error('captcha.validate:', err.message || err);
                        return Promise.reject('验证码错误!');
                    });
                } else {
                    return Promise.reject('登录类型错误!');
                }
                console.log('save_login_hist...');
                // 登录成功历史记录
                yield loginService.saveLoginHis(mobile, '1', audit, '');

                // audit user login
                auditHelper.login(user.id);

                // 判断是否默认密码(空或6个0)
                user.defaultPass = 'false';
                if (!user.password || user.validPassword('000000') || type == '3') {
                    user.defaultPass = 'true';
                }
                data.token = yield tokenHelper.saveAsync(user, audit);

                const mqttKey = tokenHelper.generateMQTTKey(user.name);
                user.mqttKey = mqttKey;
                data.user = user;

                res.locals.data = data;
                console.log('login_v2_response:' + JSON.stringify(data));
                next();
            }).catch((err) => {
                logger.info('login_v2 err:', err.stack || err);
                res.locals.err = err;
                return next();
            });
        },
        responseJSON(),
    );

    app.get(
        '/app',
        cors(corsOptions),
        function (req, res, next) {
            const clientID = req.headers.client;
            const secret = req.headers.secret;
            console.log(req.headers);
            async.waterfall(
                [
                    function (cb) {
                        Client.findOne(
                            {
                                clientID,
                                clientSecret: secret,
                                scope: 'um',
                            },
                            function (err, client) {
                                if (err) {
                                    return cb(err);
                                }
                                if (!client) {
                                    return cb('未找到授权应用');
                                }
                                cb(null, client);
                            },
                        );
                    },
                    function (client, cb) {
                        tokenHelper.saveClientToken(client, function (err, clientToken) {
                            if (err) {
                                return cb(err);
                            }
                            cb(null, clientToken);
                        });
                    },
                ],
                function (err, clientToken) {
                    if (err) {
                        res.locals.err = err;
                        return next();
                    }
                    res.locals.data = { clientToken };
                    next();
                },
            );
        },
        responseJSON(),
    );

    app.get(
        '/public',
        cors(corsOptions),
        function (req, res, next) {
            const clientID = req.headers.client;
            const secret = req.headers.secret;
            console.log(req.headers);
            async.waterfall(
                [
                    function (cb) {
                        Client.findOne(
                            {
                                clientID,
                                clientSecret: secret,
                                scope: 'public',
                            },
                            function (err, client) {
                                if (err) {
                                    return cb(err);
                                }
                                if (!client) {
                                    return cb('未找到授权应用');
                                }
                                cb(null, client);
                            },
                        );
                    },
                    function (client, cb) {
                        tokenHelper.savePublicToken(client, function (err, clientToken) {
                            if (err) {
                                return cb(err);
                            }
                            cb(null, clientToken);
                        });
                    },
                ],
                function (err, clientToken) {
                    if (err) {
                        res.locals.err = err;
                        return next();
                    }
                    res.locals.data = { clientToken };
                    next();
                },
            );
        },
        responseJSON(),
    );

    app.post(
        '/refreshWechatToken/:fromWeixin',
        cors(corsOptions),
        function (req, res, next) {
            co(function* () {
                const fromWeixin = req.params.fromWeixin;
                const refreshToken = req.body.refreshToken;
                if (!refreshToken) throw 'refreshToken 不能为空！';

                let wechatHandle = new WechatHandle();
                let wechatPage = WechatHandle.getWechatPage(req, fromWeixin); // 微信配置
                let result = yield wechatHandle.refreshWechatToken(wechatPage, refreshToken);

                res.locals.data = result;
                return next();
            }).catch((err) => {
                logger.error('refreshWechatToken err:', err.stack || err);
                res.locals.err = err.message || err.stack || err;
                return next();
            });
        },
        responseJSON(),
    );

    const getLocation = function (location, openid) {
        let hash = location.split('#!')[1];
        if (hash) {
            var querys = hash.split('?');
            var newLocation =
                location.split('#!')[0] +
                '#!' +
                querys[0] +
                '/' +
                openid +
                (querys[1] ? '?' + querys[1] : '');
            return newLocation;
        }
        hash = location.split('#')[1];
        if (hash) {
            var querys = hash.split('?');
            var newLocation =
                location.split('#')[0] +
                '#' +
                querys[0] +
                '/' +
                openid +
                (querys[1] ? '?' + querys[1] : '');
            return newLocation;
        }
        var querys = location.split('?');
        var newLocation = querys[0] + '/' + openid + (querys[1] ? '?' + querys[1] : '');
        return newLocation;
    };

    /** 微信授权登录
     * // http://localhost:4400/api/login/wechatOauth2/?wechatName=okpanda&needToken=0&redirectUrl=http%3A%2F%2Flocalhost%3A4200%2Fokpanda%2Findex.html%3Fv%3D1588043366205%23!%2Fwechat%2Foauth2%2Fokpanda%3Fscope%3Dsnsapi_base
     * @param wechatName 微信APP名
     * @param needToken 是否需要token  "0"-不需要 "1"-需要
     * @param redirectUrl
     *  如果成功则返回 token;如果失败 则跳转到错误页面且返回错误消息 */
    app.get('/wechatOauth2', function (req, res, next) {
        let obj = req.query;
        console.log('wechatOauth2_req:', JSON.stringify(obj));
        const redirectUrl = decodeURIComponent(obj.redirectUrl);
        co(function* () {
            let wechatName = obj.wechatName;
            if (!wechatName || wechatName == undefined || wechatName == 'undefined')
                throw 'wechatName 不能为空';
            let needToken = obj.needToken; //
            if (!needToken) needToken = '0';

            // 授权方式: (1)snsapi_base:静默授权(仅返回openid); (2)snsapi_userinfo:手动授权（返回openid和token）
            let scope = 'snsapi_userinfo';
            if (!needToken || needToken == '0' || needToken == 'snsapi_base') scope = 'snsapi_base';

            let wechatPage = WechatHandle.getWechatPage(req, wechatName); // 微信配置
            let appid = wechatPage.appid;

            const esbUmUrl = req.app.kraken.get('restUrl:esbUmUrl');

            // let state = encodeURIComponent(redirectUrl); // 放到微信Oauth 中的 参数
            // 生成短链接
            let { shortUrl } = yield commonService.getShortUrl({ url: redirectUrl });
            let _url = `${esbUmUrl}/api/login/wechatCallbackV2/${wechatName}/scope`;
            let shared = redirectUrl.includes('sharedKey');
            if (obj.key) _url += `?key=${obj.key}&shared=${shared}`;
            const redirect_uri = encodeURIComponent(_url);

            const oauthUrl =
                'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
                appid +
                '&redirect_uri=' +
                redirect_uri +
                '&response_type=code&scope=' +
                scope +
                '&state=' +
                shortUrl +
                '#wechat_redirect';

            console.log('wechatOauth2 oauthUrl:' + oauthUrl);
            return res.redirect(oauthUrl);
        }).catch((err) => {
            logger.error('wechatOauth2 err:', err.stack || err);
            let errMsg = err.message || err.stack || err;

            let errUrl = redirectUrl + '?errcode=999&errmsg=' + errMsg;
            console.log('wechatOauth2 errUrl:' + errUrl);
            return res.redirect(errUrl);
        });
    });

    /**
     * @param fromWeixin 来源的微信
     * @param scope  (1)snsapi_base:静默授权(仅返回openid); (2)snsapi_userinfo:手动授权（返回openid和token）
     */
    app.get('/wechatCallbackV2/:fromWeixin/:scope', function (req, res, next) {
        let obj = req.query;
        console.log('wechatCallbackV2_req:', JSON.stringify(obj));
        let location = decodeURIComponent(obj.state);
        co(function* () {
            const fromWeixin = req.params.fromWeixin;
            const code = obj.code; // 微信回调之后返回的code
            let scope = req.params.scope;

            const handle = new WechatHandle();
            let wechatPage = WechatHandle.getWechatPage(req, fromWeixin); // 微信配置
            if (obj.shared) scope = '0'; // 有sharedKey时不用生成token
            let wechatCallbackRes = yield handle.wechatCallback(wechatPage, code, scope, obj.key);

            let paramSeparator = location.includes('?') ? '&' : '?';
            location += `${paramSeparator}openid=${wechatCallbackRes.openid}`;
            if (!obj.shared) {
                // 没有sharedKey的情况下增加token
                location += '&token=' + wechatCallbackRes.token;
            }
            console.log('wechatCallbackV2_res', location);
            return res.redirect(location);
        }).catch((err) => {
            logger.error('wechatCallback err:', err.stack || err);
            let errMsg = err.message || err.stack || err;

            let errUrl = location + '?errcode=999&errmsg=' + errMsg;
            console.log('wechatOauth2 errUrl:' + errUrl);
            return res.redirect(errUrl);
        });
    });

    /**
     * 返回用户token（调用前需要使用手动授权方式） 2020年5月1日之后建议调用上面的 wechatCallback/v2 方法
     * 保持原有的逻辑（仅重写） */
    app.get('/wechatCallback/:fromWeixin', function (req, res, next) {
        const location = decodeURIComponent(req.query.state);
        co(function* () {
            const fromWeixin = req.params.fromWeixin;
            const code = req.query.code; // 微信回调之后返回的code
            const needToken = '1';

            const handle = new WechatHandle();
            let wechatPage = WechatHandle.getWechatPage(req, fromWeixin); // 微信配置
            let wechatCallbackRes = yield handle.wechatCallback(wechatPage, code, needToken);
            let token = wechatCallbackRes.token;
            console.log('wechatCallback wechatCallbackRes :' + JSON.stringify(wechatCallbackRes));

            return res.redirect(getLocation(location, 'wechat/' + token));
        }).catch((err) => {
            logger.error('wechatCallback err:', err.stack || err);
            return res.redirect(getLocation(location, 'wechat/false'));
        });
    });

    /**
     * 返回用户 openid（调用前一般使用静默授权方式） 2020年5月1日之后建议调用上面的 wechatCallback/v2 方法
     * 保持原有的逻辑（仅重写）  */
    app.get('/wechatCallbackForOpenid/:fromWeixin', function (req, res, next) {
        console.log('wechat_callback:', req.query);
        const location = decodeURIComponent(req.query.state);
        co(function* () {
            const code = req.query.code;
            if (!code) throw 'code 不能为空';

            const fromWeixin = req.params.fromWeixin;
            const wechatPage = WechatHandle.getWechatPage(req, fromWeixin);

            let handle = new WechatHandle();
            let openid = yield handle.wechatCallbackForOpenid(wechatPage, code);

            console.log('openid:' + openid);
            res.redirect(getLocation(location, 'wechat/' + openid));
        }).catch((err) => {
            logger.error('wechatCallbackForOpenid err:', err.stack || err);
            return res.redirect(getLocation(location, 'wechat/false'));
        });
    });

    /**
     * 返回用户 token
     */
    app.get('/openid/:openid', cors(), function (req, res, next) {
        console.log('/openid/:openid', req.params);
        co(function* () {
            const { openid } = req.params;
            if (!openid) throw 'openid 不能为空';

            let condtion = { 'wechatInfo.openID': openid };
            console.log('get_user_by_openid:', JSON.stringify(condtion));
            const user = yield WxUser.findOne(condtion);
            let token;
            if (user) {
                token = yield tokenHelper.saveV2Async(user, {
                    deviceToken: 'connect|' + openid,
                });
            }

            res.json({ code: '000', data: { token } });
        }).catch((err) => {
            logger.error('/openid/:openid err:', err.stack || err);
            return res.json({ code: '999', msg: err.message });
        });
    });
};
