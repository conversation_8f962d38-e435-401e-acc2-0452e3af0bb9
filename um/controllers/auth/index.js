'use strict';
const co = require('co');
const url = require('url');
const _ = require('underscore');
const auth = require('../../lib/auth');
const User = require('../../models/system/User');
const UserInfo = require('../../models/UserInfo');
const oauth2Server = require('../../lib/OAuth2Server').server();
const passport = require('passport');
var Client = require('../../models/oauth/Client');
const menuHelper = require('../../lib/menuHelper');
const branchHelper = require('../../lib/branchHelper');
const Branch = require('../../models/system/Branch');
const Provincial = require('../../models/system/Provincial');
const responseJSON = require('../../lib/middlewear/responseJSON');
const Role = require('../../models/system/Role');
const Menu = require('../../models/system/Menu');
var Client = require('../../models/oauth/Client');

module.exports = function(router) {

    router.get('/authorize',
        // 兼容前后端分离的认证检查
        function(req, res, next) {
            if (!req.isAuthenticated()) {
                // 保存当前请求URL，登录后重定向回来
                req.session.goingTo = req.originalUrl;
                return res.redirect('/login');
            }
            next();
        },
        oauth2Server.authorize(
            function(clientID, redirectURI, scope, done) {
                Client.findOne({
                    clientID,
                }, function(err, client) {
                    console.log('authorize_client:', client);
                    console.log('authorize_redirectURI:', redirectURI);
                    if (err) {
                        console.error('authorize:', err);
                        return done(err);
                    }
                    if (!client) {
                        return done(null, false);
                    }
                    if (url.parse(client.redirectURI).pathname != url.parse(redirectURI).pathname) {
                        return done(null, false);
                    }
                    // client.scope = scope;
                    return done(null, client, client.redirectURI);
                });
            }
        ),
        function(req, res, next) {
            // 如果client是受信任的应用，那么直接授权
            Client.findOne({
                clientID: req.oauth2.client.clientID,
            }, function(err, client) {
                if (!err && client && client.trusted && client.trusted === true) {
                    oauth2Server.decision({
                        loadTransaction: false,
                    }, function(req, callback) {
                        callback(null, {
                            allow: true,
                        });
                    })(req, res, next);
                } else {
                    // 不是受信任的应用，提示用户是否授权
                    res.render('user/permit', {
                        transactionID: req.oauth2.transactionID,
                        user: req.user,
                        client: req.oauth2.client,
                    });
                }
            });
        }
    );

    // 处理用户授权
    router.post('/permit',
        auth.isAuthenticated(),
        oauth2Server.decision()
    );

    // 用于发放accessToken给client应用
    router.post('/token',
        passport.authenticate([ 'basic', 'oauth2-client-password' ], {
            session: false,
        }),
        oauth2Server.token(),
        oauth2Server.errorHandler()
    );

    // 供client应用请求用户数据
    router.get('/api/userinfo',
        passport.authenticate('bearer', {
            session: false,
        }),
        function(req, res, next) {
            const model = {};
            model.user = req.user;
            Role.find({ code: { $in: req.user.roles } }, function(err, roles) {
                if (err) {
                    return res.json(model);
                }
                // console.log(roles);
                let menus = [];
                _.each(roles, function(role) {
                    menus = _.union(menus, role.menus);
                });
                // console.log(menus);
                Menu.find({ _id: { $in: menus } }, { fullUrl: 1, application: 1 }, function(err, objs) {
                    // console.log(urls);
                    if (err) {
                        return res.json(model);
                    }
                    const authUrls = [];
                    const apps = new Set();
                    _.each(objs, function(item) {
                        authUrls.push(item.fullUrl.split('#')[0]);
                        item.application && apps.add(item.application);
                    });
                    model.authUrls = JSON.stringify(_.uniq(authUrls));
                    model.apps = JSON.stringify(Array.from(apps));
                    model.userInfo = JSON.stringify(req.user.userInfo);
                    Client.findById(req.authInfo.client, function(err, client) {
                        if (err) {
                            return res.json(model);
                        }
                        if (!client) {
                            return res.json(model);
                        }
                        menuHelper.getRoleMenuTree(client.clientID, req.user.roles, function(err, menuTree) {
                            model.menuTree = JSON.stringify(menuTree);
                            // console.log(model);
                            res.json(model);
                        });
                    });
                });
            });

        }
    );

    // 供client应用获取机构数
    router.get('/api/branchTree',
        passport.authenticate('bearer', {
            session: false,
        }),
        function(req, res, next) {
            branchHelper.branchTree(function(tree) {
                res.json({
                    branchTree: tree,
                });
            });
        }
    );
    // 供client应用获取机构信息
    router.post('/api/queryBranchInfo',
        passport.authenticate('bearer', {
            session: false,
        }),
        function(req, res, next) {
            const condition = req.body;
            Branch.find(condition, function(err, branchs) {
                if (err) {
                    return res.json({});
                }
                res.json({
                    branchInfo: branchs,
                });
            });
        }
    );
    router.post('/api/queryBranchInfo/jwt',
        auth.isAuthenticatedByToken(),
        function(req, res, next) {
            const condition = req.body;
            Branch.find(condition, function(err, branchs) {
                if (err) {
                    return res.json({});
                }
                res.json({
                    branchInfo: branchs,
                });
            });
        }
    );
    router.post('/api/queryUserInfo', passport.authenticate('bearer', {
        session: false,
    }), function(req, res, next) {
        let page = 1;
        if (req.body.page) {
            page = req.body.page;
        }
        const count = req.body.count || 6;
        const condition = req.body.condition;

        auth.branchCondition(condition, req.body.user, 'branch'); // 限制机构范围
        User.paginate(condition, { page, limit: count }, function(err, result) {
            if (err) {
                return next(err);
            }
            const model = {
                title: '用户列表',
                users: result.docs,
                page,
                pageCount: result.pages,
                showMessage: req.flash('showMessage'),
            };
            res.json(model);
        }, {
            populate: 'userInfo',
            sortBy: {
                status: 1,
                name: 1,
            },
        });
    });

    // 省分列表
    router.get('/api/provInfo',auth.isAuthenticatedByToken(), function(req, res, next) {
        const type = req.query.type;
        const condition = { status: '1' };
        co(function* (){
            let list = yield Provincial.find(condition, 'code abbrName');
            res.json({ code: '000', list });
        }).catch(err=>{
            res.json({code: '999', msg: err})
        })
    });

    // 供client应用请求机构数据
    router.get('/api/branchInfo', auth.isAuthenticatedByToken(), function(req, res, next) {
        const type = req.query.type;
        const condition = { status: '1' };
        const model = {};
        if (type === 'getName') {
            var branchCode = req.query.branchCode;
            condition.code = branchCode;
            Branch.findOne(condition, 'name abbrName province provCode provName insurNO', function(err, branch) {
                if (err) {
                    model.err = '1';
                    return res.json(model);
                }
                model.name = branchCode
                if(branch){
                    model.province = branch.province;
                    model.provCode = branch.provCode || '';
                    model.provName = branch.provName || '';
                    model.name = branch.abbrName || branch.name;
                }
                res.json(model);
            });
        } else if (type == 'getLevel') {
            var branchCode = req.query.branchCode;
            condition.levelId = branchCode;
            Branch.findOne(condition, 'name abbrName code province provCode provName insurNO', function(err, branch) {
                if (err) {
                    model.err = '1';
                    return res.json(model);
                }
                model.branch = branch;
                res.json(model);
            });
        } else {
            // 是否查询所有的子节点
            console.log('----->查询所有子节点');
            const obj = req.query;
            console.log('branchInfo query paramter===' + JSON.stringify(obj));
            const isAll = obj.isAll;
            var branchCode = obj.branchCode;
            const condCode = branchCode.split(',');
            if (isAll && isAll == 'true') {
                condition.$or = [{ code: { $in: condCode } }, { parent: { $in: condCode } }];
            } else {
                condition.parent = branchCode;
            }
            if (obj.name) condition.name = new RegExp(obj.name);

            console.log('branchInfo condition===' + JSON.stringify(condition));
            Branch.find(condition, 'code name abbrName order province provCode provName insurNO').sort('order').exec(function(err, branches) {
                if (err) {
                    model.err = '1';
                    return res.json(model);
                }
                model.branches = branches;
                res.json(model);
            });
        }
    });

    // 查询机构名称
    router.get('/api/branch', function(req, res, next) {
        const obj = req.query;
        // 是否查询所有的子节点
        console.log('----->查询所有子节点');
        const condition = {};
        condition.code = new RegExp(obj.branchCode);

        console.log('branch_condition===' + JSON.stringify(condition));
        const model = {};
        Branch.find(condition, 'code name abbrName order province provCode provName insurNO').sort('order').exec(function(err, branches) {
            if (err) {
                model.err = '1';
                return res.json(model);
            }
            model.branches = branches;
            res.json(model);
        });
    });

    // 查询机构名称 从parent查
    router.get('/api/branchparent', function(req, res, next) {
        const obj = req.query;
        // 是否查询所有的子节点
        console.log('----->查询所有子节点');
        const condition = {};
        condition.parent = new RegExp(obj.branchCode);

        console.log('branch_condition===' + JSON.stringify(condition));
        const model = {};
        Branch.find(condition, 'code name abbrName order province provCode provName insurNO').sort('order').exec(function(err, branches) {
            if (err) {
                model.err = '1';
                return res.json(model);
            }
            model.branches = branches;
            res.json(model);
        });
    });

    router.post('/user/update', function(req, res, next) {
        if (!req.body.data) {
            return next();
        }
        const obj = req.body;
        console.log('user_update=====' + JSON.stringify(obj));
        const baseInfo = obj.data.baseInfo;
        const bankInfo = obj.data.bankInfo;
        const identityInfo = obj.data.identityInfo;
        const aptitudeInfo = obj.data.aptitudeInfo;
        const name = obj.data.name;
        const status = obj.data.status;
        const updateData = {};
        if (baseInfo) {
            updateData.baseInfo = baseInfo;
        }
        if (bankInfo) {
            updateData.bankInfo = bankInfo;
        }
        if (identityInfo) {
            updateData.identityInfo = identityInfo;
        }
        if (aptitudeInfo) {
            updateData.aptitudeInfo = aptitudeInfo;
        }
        if (_.isEmpty(updateData)) {
            res.locals.err = '传入数据不能为空';
            return next();
        }
        User.findOne({ name }, function(err, user) {
            if (err) {
                res.locals.err = err;
                return next();
            }
            if (!user) {
                res.locals.err = '用户不存在';
                return next();
            }
            UserInfo.findOne({
                name: user.name,
            }, function(err, userInfo) {
                if (err) {
                    res.locals.err = err;
                    return next();
                }
                if (userInfo) {
                    if (updateData.baseInfo) {
                        userInfo.nickname = updateData.baseInfo.nickname;
                        userInfo.headImageUrl = updateData.baseInfo.headImageUrl;
                        userInfo.headOsskey = updateData.baseInfo.headOsskey;
                    }
                    if (updateData.bankInfo) {
                        const has = _.some(userInfo.bankInfos, function(item) {
                            return item.account == updateData.bankInfo.account;
                        });
                        if (has) {
                            res.locals.err = '不要录入重复的银行卡号';
                            return next();
                        }
                        userInfo.bankInfos.push(updateData.bankInfo);
                    }
                    if (updateData.identityInfo) {
                        userInfo.identityInfo = updateData.identityInfo;
                    }
                    if (updateData.aptitudeInfo) {
                        userInfo.aptitudeInfo = updateData.aptitudeInfo;
                    }
                } else {
                    userInfo = new UserInfo();
                    userInfo.name = user.name;
                    if (updateData.baseInfo) {
                        userInfo.nickname = updateData.baseInfo.nickname;
                        userInfo.headImageUrl = updateData.baseInfo.headImageUrl;
                        userInfo.headOsskey = updateData.baseInfo.headOsskey;
                    }
                    if (updateData.bankInfo) {
                        userInfo.bankInfos = [ updateData.bankInfo ];
                    }
                    if (updateData.identityInfo) {
                        userInfo.identityInfo = updateData.identityInfo;
                    }
                    if (updateData.aptitudeInfo) {
                        userInfo.aptitudeInfo = updateData.aptitudeInfo;
                    }
                }
                userInfo.save(function(err) {
                    if (err) {
                        res.locals.err = err;
                        return next();
                    }
                    user.userInfo = userInfo.id;
                    user.fullName = userInfo.identityInfo.name;
                    if (status) user.status = status;
                    user.save(function(err) {
                        if (err) {
                            res.locals.err = err;
                            return next();
                        }

                        res.locals.data = { userInfo };
                        next();
                    });
                });
            });
        });
    }, responseJSON());

    // opr新增渠道小类时,给【新总部运营】角色的【运营内勤】分配该渠道
    router.post('/user/updateChannel', function(req, res) {
        return co(function* () {
            const obj = req.body.data || {};
            if (!obj.agentTypeCode) return res.json({ code: '000', message: '渠道小类代码不能为空！' });
            console.log('updateChannel=====' + JSON.stringify(obj));
            const agentTypeCode = obj.agentTypeCode;
            //新总部运营 S总部寿险运营 S总部非寿险运营 S总部财务 S总部结算 S总部保全 S业务部数据支持岗
            let roles = ['XZBYY', 'SXYY', 'FSXYY', 'SZBCW', 'SZBJS', 'SZBBQ', 'YWBSJZCG'];
            let condition = { status: '1', userType: '3' }
            condition.$or = [{ roles: { $in: roles } }];
            if (obj.user_account) condition.$or.push({ name: obj.user_account })
            const users = yield User.find(condition);
            for (const user of users) {
                if (!user.channels.includes(agentTypeCode)) {
                    user.channels.push(agentTypeCode);
                    user.updateBy = obj.updateBy || '';
                    user.updatedAt = new Date();
                    yield user.save();
                }
            }
            res.json({ code: '000', message: '更新成功！' });
        }).catch(err => {
            console.log('updateChannel===err： ', err.message, err.stack);
            res.json({ code: '999', message: err.message });
        });
    });
};
