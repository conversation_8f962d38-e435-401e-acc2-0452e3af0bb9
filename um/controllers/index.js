'use strict';
const passport = require('passport');
const auth = require('../lib/auth');
const Token = require('../lib/tokenHelper');
const UserInfo = require('../models/UserInfo');
const User = require('../models/system/User');
const SmsVCode = require('../models/SmsVCode');
const http = require('http');
const async = require('async');
const crypto = require('crypto');

module.exports = function (app) {
    app.get('/', auth.isAuthenticated(), function (req, res) {
        const user = req.user;
        const model = {
            user,
        };
        console.log(user);
        res.render('index', model);
    });

    app.get('/login', function (req, res) {
        let from = '';
        if (req.session.goingTo && req.session.goingTo.indexOf('sulaibao') >= 0) {
            from = 'sulaibao';
        }
        res.render('login', {
            title: '请登录',
            message: req.flash('error'),
            from,
        });
    });

    app.post('/login', (req, res, next) => {
            const captcha_1 = req.session.captchaPng.toLowerCase();
            const captcha_2 = req.body.captchaPNG.trim().toLowerCase();
            if (captcha_1 === captcha_2) {
                delete req.session.captchaPng;
                return next();
            }
            res.render('login', {
                title: '请登录',
                message: '验证码输入错误！',
            });
        },
        passport.authenticate('local-login', {
            failureRedirect: '/login',
            failureFlash: true,
        }),
        function (req, res, next) {
            // issue a remember me cookie if the option was checked
            if (!req.body.remember_me) {
                return next();
            }
            Token.saveRememberMe(req.user.id, function (err, token) {
                if (err) {
                    return next(err);
                }
                res.cookie('remember_me', token, {
                    path: '/',
                    httpOnly: true,
                    maxAge: *********,
                }); // 7 days
                return next();
            });
        },
        function (req, res) {
            res.redirect(req.session.goingTo || '/');
        },
    );

    app.get('/logout', auth.isAuthenticated(), function (req, res, next) {
        Token.logout(req.user.id, function (err) {
            if (err) {
                return next(err);
            }
            req.logout();
            req.session.roleMenuTree = null;
            const redirect = req.query.redirect;
            if (redirect) {
                res.redirect(redirect);
            } else {
                res.redirect('/');
            }
        });
    });

    app.get('/theme/:theme', auth.isAuthenticated(), function (req, res) {
        req.session.theme = req.params.theme;
        res.redirect('/');
    });

    app.get('/postPasswd', function (req, res, next) {
        const vcode = req.query.vcode;
        res.render('rePostPasswd', {
            vcode,
            goingTo: req.session.goingTo,
        });
    });
};
