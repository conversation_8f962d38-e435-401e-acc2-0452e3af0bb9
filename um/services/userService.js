'use strict';
const co = require('co');
const Promise = require('bluebird');
const rp = require("request-promise");
const mongoose = require('@huibao/database').mongoose;
const ObjectId = mongoose.Types.ObjectId;
const logger = require('@huibao/logger').logger('service/user');

const UserInfo = require('../models/UserInfo');
const Model = require('../models/system/User');

const tokenHelper = require('../lib/tokenHelper');
const restClient = Promise.promisifyAll(require('../lib/restClient/restClient'));

const validator = require('../lib/validator');
const utils = require('./utils/index');

const rootPath = '';

/**
 * 仅保存新数据
 *  * @param {*} obj 保存的对象
 *  * @param {*} operator 操作人
 */
exports.saveObj = function(obj, operator) {

    const functionPath = rootPath + '[保存信息]';
    return co(function* () {
        logger.info(functionPath + 'req:' + JSON.stringify(obj));
        // 1、数据校验 TODO
        if (!obj) throw '数据不能为空';
        if (!obj.name) throw '[登录名]不能为空';
        if (!obj.name) throw '[登录名]不能为空';
        if (!obj.userInfo) throw '[用户信息]不能为空';
        if (!obj.userInfo.mobile) throw '[手机号]不能为空';
        if (obj.userInfo.mobile && !validator.isMobile(obj.userInfo.mobile)) throw '手机号码格式不正确';
        if (!obj.userInfo.email) throw '[邮箱]不能为空';
        if (obj.userInfo.email && !validator.isEmail(obj.userInfo.email)) throw '邮箱格式不正确';
        if (obj.userInfo.tel && !validator.isTeleNO(obj.userInfo.tel)) throw '固定电话格式不正确';
        if (!obj.branch) throw '[归属机构]必需选择';

        let count = yield Model.count({ name: obj.name });
        if (count > 0) throw '该用户名已存在';
        if (obj.referee) {
            count = yield Model.conut({ name: obj.referee });
            if (count <= 0) throw '推荐人不存在';
        }

        // 2、入参处理
        operator = operator || 'system';// TODO
        obj.updatedBy = operator;
        obj.createdBy = operator;
        obj._id = new ObjectId();
        obj.userInfo._id = new ObjectId();
        obj.userInfo.name = obj.name;

        const userInfo = obj.userInfo;
        userInfo.user = obj._id;
        userInfo.userType = obj.userType;

        // 3、新增用户类型是业务员，则调Opr接口存相关信息
        if (obj.userType == '1') {
            if (obj.name != obj.userInfo.mobile) throw 'A端用户[登录名]必需与[手机号一致]';
            const reqObj = { mobile: obj.userInfo.mobile, branch: obj.branch, source: '3' };
            logger.info('agentInfoReq:' + JSON.stringify(reqObj));

            const agentInfo = yield restClient.generatorAgentAsync(reqObj);
            logger.info('agentInfoRes:' + JSON.stringify(agentInfo));
            obj.agentId = agentInfo.agentId;
            obj.userCode = agentInfo.userCode;
        } else if (obj.userType != '3') {
            throw '暂不支持注册此类型的用户';
        }

        // 4、保存 user信息
        logger.info('user:' + JSON.stringify(obj));
        const result = yield new Model(obj).save();

        // 5、保存userInfo
        logger.info('userInfo:' + JSON.stringify(userInfo));
        yield new UserInfo(userInfo).save();

        return result;
    }).catch(err => {

        logger.error(functionPath + 'err:' + err);
        return Promise.reject(err);
    });
};

// 退出登录
const logout = function(id) {
    const funName = '退出登录';
    return co(function* () {
        tokenHelper.logout(id, function(err) {
            if (err) throw err;
            return { success: true };
        });
    }).catch(err => {
        logger.error(funName + err);
        return Promise.reject(err);
    });
};

// 比较两个list 是否相等 （list中非对象）
const listIsEquals = function(a, b) {
    a.sort();
    b.sort();

    if (a.join() != b.join()) {
        return false;
    }
    return true;
};

/**
 * 根据ID更新
 * @param {*} obj （包含ID）
 * @param {*} operator      操作人姓名
 * @param {*} alreadyCheck  是否已经校验过
 */
const updateById = function(id, obj, operator, alreadyCheck) {

    const path = rootPath + '[updateById]';
    // logger.info(`${path}|入参obj:${JSON.stringify(obj)}|操作人:${operator}`);
    return co(function* () {

        if (!alreadyCheck) {
            const query = yield Promise.resolve(Model.findById(id));
            if (!query) return Promise.reject('根据该ID未查询到数据');
            if (obj.roles) { // 角色变更时 退出登录
                if (!listIsEquals(obj.roles, query.roles)) {
                    logout(id);
                    logger.info(path + '角色变更 退出登录,id=' + id);
                }
            }
        }

        if (obj.status && obj.status == '3') { // 注销用户
            logout(id);
        }

        let userInfo = obj.userInfo;
        userInfo = filterSurplusUserInfo(userInfo);

        let userInfoId = '';

        if (userInfo) {
            if (obj.userInfo.mobile && !validator.isMobile(obj.userInfo.mobile)) throw '手机号码格式不正确';
            if (obj.userInfo.email && !validator.isEmail(obj.userInfo.email)) throw '邮箱格式不正确';
            if (obj.userInfo.tel && !validator.isTeleNO(obj.userInfo.tel)) throw '固定电话格式不正确';
            if (!userInfo._id) {
                userInfo.name = obj.name || '';
                const userinfo = new UserInfo(userInfo);
                userinfo.user = obj._id;
                userinfo.save();
                // logger.info('\n userinfo save resUser :' + JSON.stringify(userinfo));
                userInfoId = userinfo._id;
            } else {
                yield UserInfo.findByIdAndUpdate(userInfo._id, userInfo);
            }
        }

        // 过滤不需要更新的数据
        obj = filterSurplus(obj);
        obj.updatedBy = operator;
        obj.updatedAt = new Date();

        if (userInfoId) obj.userInfo = userInfoId; // 历史数据中没有保存 userInfoId 时才更新

        const result = yield Model.findByIdAndUpdate(id, obj);
        logger.debug('user save result :' + JSON.stringify(result));

        // TODO记录日志
        return Promise.resolve(result);

    }).catch(err => {
        logger.error(path + err);
        return Promise.reject(err);
    });
};

exports.updateById = updateById;

/**
 * 批量处理角色信息
 * @param {*} obj （users,role,operation）
 * @param {*} operator 操作人姓名
 * @example obj = {users,role,operation}
 */
exports.batchRole = function(obj, operator) {

    const path = rootPath + '[批量处理角色信息]';
    logger.info(`${path}|入参obj:${JSON.stringify(obj)}|操作人:${operator}`);
    return co(function* () {
        if (!obj) throw '[data]不能为空';
        const users = obj.users;
        const role = obj.role;
        const operation = obj.operation;
        if (!users || users.length <= 0) throw '[用户]不能为空';
        if (!role) throw '[角色]不能为空';
        if (!operation) throw '[操作类型]不能为空';

        for (const i in users) {
            const user = yield Model.findById(users[i]);
            if (!user) throw '第' + (i / 1 + 1) + '个查询结果为空';
            user.roles = roleHandle(user.roles, role, operation);
            updateById(user._id, { roles: user.roles }, operator, true);
            logout(user._id); // 角色变更后 退出登录
        }
    }).catch(err => {
        logger.error(path + err);
        return Promise.reject(err);
    });
};

/** 角色处理 operation(add,remove)*/
function roleHandle(roles, role, operation) {
    if (operation == 'add') {
        roles.push(role);
    } else if (operation == 'remove') {
        for (let i = 0; i < roles.length; i++) {
            if (roles[i] == role) {
                roles.splice(i, 1);
                break;
            }
        }
    }
    roles = utils.objectUtil.removeDuplicatedItem(roles);

    return roles;
}

/**
 * 重置密码
 * @param {*} obj （包含ID 和 密码）
 * @param {*} operator 操作人姓名
 */
exports.resetPassword = function(obj, operator) {

    const path = rootPath + '[resetPassword]';
    logger.info(`${path}|入参obj:${JSON.stringify(obj)}|操作人:${operator}`);
    return co(function* () {
        // 1、入参校验
        if (!obj._id) throw '[id]不能为空';
        if (!obj.password) throw '[密码]不能为空';

        // 2、数据处理
        const model = yield Model.findById(obj._id);
        if (!model) throw '根据该ID未查询到数据';

        model.name = obj.name;
        model.password = obj.password;
        model.updatedBy = operator;

        // 3、保存
        return yield model.save();

    }).catch(err => {
        logger.error(path + err);
        return Promise.reject(err);
    });
};

/**
 * 过滤多余数据
 * @param {*} obj
 */
function filterSurplus(obj) {
    if (obj.userInfo) delete obj.userInfo;
    if (obj.name) delete obj.name;

    return obj;
}

/**
 * 过滤多余数据
 * @param {*} obj
 */
function filterSurplusUserInfo(obj) {
    if (obj && obj.user) delete obj.user;

    return obj;
}

/** 根据ID查询一条记录*/
exports.findById = function(id) {
    const path = rootPath + '[findById]';
    // logger.info(path + '入参:id' + JSON.stringify(id));

    return co(function* () {
        return yield Promise.resolve(Model.findById(id).populate({ path: 'userInfo' }));
    });
};

/** 查询一条记录*/
exports.findOne = function(params) {
    const path = rootPath + '[findOne 查询一条记录]';
    // logger.info(path + '入参:id' + JSON.stringify(params));

    return co(function* () {
        return yield Promise.resolve(Model.findOne(params));
    });
};

/** 列表查询 */
exports.find = function(condition) {
    const path = rootPath + '[find]';
    // logger.info(path + 'condition:' + JSON.stringify(condition));

    return co(function* () {
        const result = yield Model.find(condition);
        // logger.info(path + 'result:' + JSON.stringify(result));
        return result;
    }).catch(err => {
        logger.error(err);
        return Promise.reject(err);
    });
};

/** 条数查询 */
exports.count = function(condition) {
    const path = rootPath + '[count]';
    // logger.info(path + 'condition:' + JSON.stringify(condition));

    return co(function* () {
        const result = yield Model.count(condition);
        // logger.info(path + 'result:' + JSON.stringify(result));
        return result;
    }).catch(err => {
        logger.error(err);
        return Promise.reject(err);
    });
};

/** 分页查询 */
exports.pagingQuery = function(searchParams, pagingParams) {

    const path = rootPath + '[pagingQuery]';
    // logger.info(path + '入参:searchParams' + JSON.stringify(searchParams));
    /** 查询条件转换*/

    /** 分页条件 */
    if (!pagingParams) pagingParams = {};
    pagingParams.page = pagingParams.page || 1;
    pagingParams.limit = pagingParams.limit || 5;
    pagingParams.sort = pagingParams.sort || '-updatedAt';
    pagingParams.populate = 'userInfo';

    logger.info(path + '查询条件:  condition=' + JSON.stringify(searchParams) + ' , 分页条件:params=' + JSON.stringify(pagingParams));
    return co(function* () {
        const result = yield Promise.resolve(Model.paginate(searchParams, pagingParams));
        // logger.info(path + ' 出参:' + JSON.stringify(result));
        return Promise.resolve(result);
    }).catch(err => {
        logger.error(path + err);
        return Promise.reject(err);
    });
};

/**
 * 根据ID删除（物理删除）
 */
exports.deleteById = function(id) {
    const path = rootPath + '[deleteById]';
    logger.info(path + 'id' + JSON.stringify(id));

    return co(function* () {

        const obj = yield Model.findById(id);
        if (!obj) throw '根据该ID未查询到数据';
        if (obj.isValid && obj.isValid == '1') throw '有效数据不允许删除,请将数据设置成无效后再进行删除操作';

        const parent = yield Model.findById(obj.parentId);
        if (!parent) throw '数据格式异常,请联系管理员查看数据格式【无父ID】';
        const result = yield Promise.resolve(Model.remove({ _id: id }));

        Model.rebuildTree(parent, parent.lft, function() {
            logger.info('debug', 'rebuild tree for % sucess', parent.url);
            // 刷新菜单树缓存
            menuHelper.refresh();
            return result;
        });

        // yield Promise.resolve(Model.rebuildTree(parent, parent.lft));
        // //刷新菜单树缓存
        // menuHelper.refresh();
        // return result;
    }).catch(err => {
        logger.error(err);
        return Promise.reject(err);
    });
};


exports.generateBuser = function* (user) {
    if (user.buser) { return; }
    const reqObj = {
        data: {
            deptId: user.deptId,    // 2020年4月新增 部门ID
            userName: user.fullName,// 2020年4月新增 姓名
            mobile: user.mobile,
            source: '4',
            idName: user.idName,
            adminFlag: user.adminFlag,
            baseInfo: user.userInfo,
            channelType: user.channelType,
            agentType: user.agentType,
            agentCode: user.agentCode,
            deptAdmin: user.deptAdmin,
            status: user.buserStatus,
        }
    };

    if (user.inviterCode) {
        reqObj.data.inviterCode = user.inviterCode;
    }
    console.log('agentInfoBuserReq:' + JSON.stringify(reqObj));
    const result = yield restClient.generateBuserAsync(reqObj);
    console.log('agentInfoBuserRes:' + JSON.stringify(result));
    if (result.code != '000') {
        return Promise.reject('创建Buser失败');
    }
    user.buser = result.data.buserId;
    return;
};

exports.generateAuser = function* (user){
    // 注册成功后同步生成代理人信息
    const reqObj = { referrer: user.referee, mobile: user.name, source: user.source || '1',agentType:user.agentType };
    console.log('agentInfoAuserReq:' + JSON.stringify(reqObj));
    const result = yield restClient.generatorAgentAsync(reqObj);
    console.log('agentInfoAuserRes:' + JSON.stringify(result));
    if(!user.userType || user.userType!='1'){
        logger.info(`${user.name} 历史用户类型:'${user.userType}'，即将调整成:'1'`);
        user.userType = '1'; // 用户类型调整成 A;
    }
    user.agentId = result.agentId;
    user.userCode = result.userCode;
    return;
}

/**
 * 生成C端用户
 * @param {*} user
 * @returns
 */
exports.generateCuser = function* (user, agent){
    if (user.cuser) { return; }
    const reqObj = {
        mobile: user.mobile,
        source: '2',
        referrer: user.referee || ''
    };
    if(agent && agent.agentCode) {
        reqObj.agentCode = agent.agentCode;
        reqObj.agentName = agent.agentName;
    }
    logger.info('generateCuserReq:', reqObj);
    const resObj = yield restClient.generateCuserAsync(reqObj);
    logger.info('generateCuserRes:', resObj);
    user.userId = resObj.data.userId;
    user.userCode = resObj.data.userCode;
    return;
}


// 查询手机号对应的运营商信息
function* getPhoneLocation(mobile) {
    let province = '', city = '', isp = '';
    try{
        let url = `http://121.40.17.89:8020/phone-location/${mobile}`;
        let options = { method: 'GET', uri: url, json: true };
        let res = yield rp(options);
        if(res) {
            province = res.province || '';
            city = res.city || '';
            isp = res.isp || '';
        }
    }catch(error) {
        console.error('Error fetching phone location:', error);
    }
    return { province, city, isp };
}

exports.getPhoneLocation = getPhoneLocation;
